# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# VS Code related
.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.flutter-devtools-history
.packages
.pub-cache/
.pub/
/build/
*.lock

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# iOS/macOS related
**/ios/**/dgph
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/UserInterfaceState.xcuserstate
**/ios/.generated/
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/ephemeral/
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*
**/macos/Flutter/GeneratedPluginRegistrant.*

# Web related
lib/generated_plugin_registrant.dart

# Coverage
coverage/

# Firebase configuration files (注释掉以允许提交开发环境配置)
# google-services.json
# GoogleService-Info.plist

# Environment variables
.env
.env.local
.env.development
.env.production

# Keystore files
*.jks
*.keystore
*.p12
*.key
*.mobileprovision

# Generated files
*.g.dart
*.freezed.dart
*.gr.dart
*.config.dart

# FVM (Flutter Version Management)
.fvm/

# Legacy Node.js artifacts (keeping some for potential web dependencies)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Logs
logs
*.log

# OS generated files
Thumbs.db
Desktop.ini

# Swift Package Manager build directories（任意子路径）
**/.build/
**/.swiftpm/

# Java/Android debugging files
*.hprof
*.heapdump
*.threaddump

# IDE generated files
.vscode/
.idea/

# Temporary files
*.tmp
*.temp
