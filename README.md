# GAZO-SmartHome

## 项目简介

GAZO-SmartHome是一款智能家居管理应用，为用户提供家居智能化控制、装修服务、家居维修等一站式解决方案。本项目采用Flutter跨平台框架开发，集成涂鸦智能IoT平台，支持iOS和Android平台。

## 技术架构

本项目采用现代化的Flutter架构设计：

- **前端**：Flutter框架开发的跨平台移动应用
- **智能家居**：集成涂鸦智能(Tuya)IoT平台，支持设备控制和管理
- **后端服务**：RESTful API服务，处理业务逻辑和数据管理
- **Firebase**：提供崩溃分析和应用监控服务

### 架构特点

- **模块化设计**：采用Feature-based架构，按功能模块组织代码
- **分层架构**：Core层提供基础服务，Features层实现业务功能，Shared层提供共享组件
- **跨平台支持**：一套代码同时支持iOS和Android平台
- **IoT集成**：深度集成涂鸦智能平台，支持丰富的智能设备控制

## 功能模块

### 🏠 智能家居
- 设备管理和控制
- 房间分组管理
- 设备状态监控
- 场景自动化

### 🎨 装修服务
- 装修方案浏览
- 设计师服务
- 装修进度跟踪
- 合同管理

### 🛒 商城购物
- 家居商品浏览
- 购物车管理
- 订单处理
- 支付集成

### 👤 个人中心
- 用户信息管理
- 订单历史
- 地址管理
- 设置配置

### 🔍 发现
- 推荐内容
- 文章资讯
- 活动信息

## 技术栈

- **前端框架**：Flutter 3.1.0+
- **开发语言**：Dart
- **状态管理**：Provider
- **网络请求**：Dio
- **UI组件**：Material Design + 自定义组件
- **图片处理**：Cached Network Image
- **IoT平台**：涂鸦智能(Tuya Smart)
- **分析服务**：Firebase Crashlytics
- **平台支持**：iOS 12.0+、Android 5.0+

## 环境要求

- Flutter SDK: 3.1.0+
- Dart: 3.1.0+
- iOS: 12.0+
- Android: 5.0+ (API Level 21+)
- Xcode: 14.0+ (iOS开发)
- Android Studio: 2022.1+ 或 VS Code

## 安装与运行

### 前提条件

确保已安装以下工具：
- Flutter SDK 3.1.0+
- Android Studio 或 Visual Studio Code
- Xcode 14.0+ (仅macOS开发iOS应用时需要)
- Git

### 安装步骤

1. **克隆仓库**
```bash
git clone https://github.com/yourusername/GAZO-SmartHome.git
cd GAZO-SmartHome
```

2. **安装Flutter依赖**
```bash
flutter pub get
```

3. **配置Firebase**
   - 将Firebase配置文件放置到对应位置
   - iOS: `ios/Runner/GoogleService-Info.plist`
   - Android: `android/app/google-services.json`

4. **配置涂鸦智能**
   - 在涂鸦IoT平台创建应用
   - 配置相应的App Key和App Secret

5. **运行应用**
```bash
# 检查设备连接
flutter devices

# 运行Android版本
flutter run -d android

# 运行iOS版本
flutter run -d ios

# 构建发布版本
flutter build apk --release  # Android
flutter build ios --release  # iOS
```

## 项目结构

```
GAZO-SmartHome/
├── android/                    # Android原生代码
│   ├── app/
│   │   ├── src/main/kotlin/   # Kotlin代码
│   │   └── build.gradle.kts   # Android构建配置
│   └── ...
├── ios/                       # iOS原生代码
│   ├── Runner/                # iOS应用代码
│   └── ...
├── lib/                       # Flutter应用代码
│   ├── app/                   # 应用层
│   │   └── base_tabbar_controller.dart
│   ├── core/                  # 核心层
│   │   ├── models/           # 数据模型
│   │   ├── network/          # 网络服务
│   │   ├── services/         # 核心服务
│   │   └── utils/            # 工具类
│   ├── features/             # 功能模块
│   │   ├── decoration/       # 装修模块
│   │   ├── designer/         # 设计师模块
│   │   ├── discover/         # 发现模块
│   │   ├── personal/         # 个人中心模块
│   │   ├── shopping/         # 购物模块
│   │   └── smart_home/       # 智能家居模块
│   ├── shared/               # 共享组件
│   │   ├── components/       # 可复用UI组件
│   │   ├── dialogs/          # 弹窗组件
│   │   └── views/            # 共享视图
│   ├── pages/                # 独立页面
│   ├── firebase_options.dart # Firebase配置
│   └── main.dart             # 应用入口
├── assets/                   # 资源文件
│   └── images/              # 图片资源
│       ├── common/          # 通用图片
│       ├── decoration/      # 装修相关图片
│       ├── designer/        # 设计师相关图片
│       ├── discover/        # 发现页面图片
│       ├── personal/        # 个人中心图片
│       ├── shopping/        # 购物相关图片
│       ├── smart_home/      # 智能家居图片
│       └── tabbar/          # 底部导航图片
├── test/                    # 测试文件
├── pubspec.yaml            # Flutter项目配置
├── firebase.json           # Firebase配置
└── README.md              # 项目说明
```

## 开发规范

### 代码组织
- 采用Feature-based架构，按功能模块组织代码
- Core层提供基础服务和工具类
- Features层实现具体业务功能
- Shared层提供可复用的UI组件

### 命名规范
- 文件名使用snake_case
- 类名使用PascalCase
- 变量和方法名使用camelCase
- 常量使用UPPER_SNAKE_CASE

### Git提交规范
- feat: 新功能
- fix: 修复bug
- refactor: 重构代码
- docs: 文档更新
- style: 代码格式调整
- test: 测试相关

## 开发指南

### 添加新功能模块
1. 在`lib/features/`下创建新的功能目录
2. 按照MVC模式组织代码：
   - `controllers/` - 控制器
   - `models/` - 数据模型
   - `views/` - 视图组件
3. 在`lib/shared/`中添加可复用组件

### 资源管理
- 图片资源按功能模块分类存放
- 在`pubspec.yaml`中正确配置资源路径
- 使用`NetworkImageHelper`处理网络图片缓存

