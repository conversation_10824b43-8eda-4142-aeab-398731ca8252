#!/bin/bash

# GAZO-SmartHome 测试运行脚本
# 用于运行项目的各种测试和生成报告

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印标题
print_title() {
    echo
    print_message $BLUE "=================================="
    print_message $BLUE "$1"
    print_message $BLUE "=================================="
    echo
}

# 检查Flutter环境
check_flutter() {
    if ! command -v flutter &> /dev/null; then
        print_message $RED "错误: Flutter未安装或不在PATH中"
        exit 1
    fi
    
    print_message $GREEN "Flutter版本: $(flutter --version | head -n 1)"
}

# 获取依赖
get_dependencies() {
    print_title "获取项目依赖"
    flutter pub get
    print_message $GREEN "依赖获取完成"
}

# 运行所有测试
run_all_tests() {
    print_title "运行所有单元测试"
    flutter test --reporter=expanded
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 所有测试通过"
    else
        print_message $RED "❌ 部分测试失败"
        exit 1
    fi
}

# 运行特定模块测试
run_module_tests() {
    local module=$1
    print_title "运行 $module 模块测试"
    
    case $module in
        "core")
            flutter test test/core/ --reporter=expanded
            ;;
        "models")
            flutter test test/models_test.dart --reporter=expanded
            ;;
        "features")
            flutter test test/features/ --reporter=expanded
            ;;
        "shared")
            flutter test test/shared/ --reporter=expanded
            ;;
        "utils")
            flutter test test/core/utils/ --reporter=expanded
            ;;
        "services")
            flutter test test/core/services/ --reporter=expanded
            ;;
        *)
            print_message $RED "未知模块: $module"
            print_message $YELLOW "可用模块: core, models, features, shared, utils, services"
            exit 1
            ;;
    esac
    
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ $module 模块测试通过"
    else
        print_message $RED "❌ $module 模块测试失败"
        exit 1
    fi
}

# 生成测试覆盖率报告
generate_coverage() {
    print_title "生成测试覆盖率报告"
    
    # 运行测试并生成覆盖率数据
    flutter test --coverage
    
    if [ $? -ne 0 ]; then
        print_message $RED "测试失败，无法生成覆盖率报告"
        exit 1
    fi
    
    # 检查是否安装了lcov
    if command -v lcov &> /dev/null && command -v genhtml &> /dev/null; then
        # 生成HTML报告
        genhtml coverage/lcov.info -o coverage/html --quiet
        print_message $GREEN "✅ 覆盖率报告已生成: coverage/html/index.html"
        
        # 显示覆盖率摘要
        lcov --summary coverage/lcov.info
        
        # 询问是否打开报告
        read -p "是否打开覆盖率报告? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            if command -v open &> /dev/null; then
                open coverage/html/index.html
            elif command -v xdg-open &> /dev/null; then
                xdg-open coverage/html/index.html
            else
                print_message $YELLOW "请手动打开: coverage/html/index.html"
            fi
        fi
    else
        print_message $YELLOW "警告: 未安装lcov，无法生成HTML报告"
        print_message $YELLOW "覆盖率数据已保存到: coverage/lcov.info"
        print_message $YELLOW "安装lcov: brew install lcov (macOS) 或 apt-get install lcov (Ubuntu)"
    fi
}

# 运行性能测试
run_performance_tests() {
    print_title "运行性能测试"
    
    if [ -d "test_driver" ]; then
        flutter drive --target=test_driver/app.dart
        print_message $GREEN "✅ 性能测试完成"
    else
        print_message $YELLOW "⚠️  未找到性能测试文件"
    fi
}

# 清理测试缓存
clean_test_cache() {
    print_title "清理测试缓存"
    
    flutter clean
    rm -rf coverage/
    rm -rf .dart_tool/test/
    
    print_message $GREEN "✅ 测试缓存已清理"
}

# 验证测试文件
validate_tests() {
    print_title "验证测试文件"
    
    local test_files=$(find test -name "*_test.dart" | wc -l)
    local mock_files=$(find test/mocks -name "*.dart" 2>/dev/null | wc -l || echo 0)
    local helper_files=$(find test/helpers -name "*.dart" 2>/dev/null | wc -l || echo 0)
    
    print_message $GREEN "测试文件统计:"
    echo "  - 测试文件: $test_files 个"
    echo "  - Mock文件: $mock_files 个"
    echo "  - 辅助文件: $helper_files 个"
    
    # 检查是否有孤立的测试文件
    print_message $BLUE "检查测试文件完整性..."
    
    local issues=0
    
    # 检查每个测试文件是否有对应的源文件
    for test_file in $(find test -name "*_test.dart" -not -path "test/mocks/*" -not -path "test/helpers/*"); do
        local source_file=$(echo $test_file | sed 's|test/||' | sed 's|_test\.dart|.dart|')
        if [ ! -f "lib/$source_file" ] && [ ! -f "$source_file" ]; then
            print_message $YELLOW "⚠️  可能的孤立测试文件: $test_file"
            ((issues++))
        fi
    done
    
    if [ $issues -eq 0 ]; then
        print_message $GREEN "✅ 测试文件验证通过"
    else
        print_message $YELLOW "⚠️  发现 $issues 个潜在问题"
    fi
}

# 显示帮助信息
show_help() {
    echo "GAZO-SmartHome 测试运行脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  all                运行所有测试"
    echo "  module <name>      运行特定模块测试"
    echo "  coverage           生成测试覆盖率报告"
    echo "  performance        运行性能测试"
    echo "  validate           验证测试文件"
    echo "  clean              清理测试缓存"
    echo "  help               显示此帮助信息"
    echo
    echo "模块名称:"
    echo "  core               核心模块测试"
    echo "  models             数据模型测试"
    echo "  features           功能模块测试"
    echo "  shared             共享组件测试"
    echo "  utils              工具类测试"
    echo "  services           服务类测试"
    echo
    echo "示例:"
    echo "  $0 all                    # 运行所有测试"
    echo "  $0 module core            # 运行核心模块测试"
    echo "  $0 coverage               # 生成覆盖率报告"
}

# 主函数
main() {
    # 检查Flutter环境
    check_flutter
    
    # 获取依赖
    get_dependencies
    
    case "${1:-all}" in
        "all")
            run_all_tests
            ;;
        "module")
            if [ -z "$2" ]; then
                print_message $RED "错误: 请指定模块名称"
                show_help
                exit 1
            fi
            run_module_tests "$2"
            ;;
        "coverage")
            generate_coverage
            ;;
        "performance")
            run_performance_tests
            ;;
        "validate")
            validate_tests
            ;;
        "clean")
            clean_test_cache
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_message $RED "错误: 未知选项 '$1'"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
