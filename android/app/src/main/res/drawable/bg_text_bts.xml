<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/thing_mg_2" />

            <solid android:color="@color/primary_button_select_color" />
        </shape>
    </item>
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/thing_mg_2" />

            <solid android:color="@color/primary_button_select_color" />
        </shape>
    </item>
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/thing_mg_2" />

            <solid android:color="@color/primary_button_select_color" />
        </shape>
    </item>
    <item android:state_enabled="true">
        <shape android:shape="rectangle">
            <corners android:radius="@dimen/thing_mg_2" />

            <solid android:color="@color/primary_button_bg_color" />
        </shape>
    </item>
    <item android:state_enabled="false">
        <shape>
            <corners android:radius="@dimen/thing_mg_2" />

            <solid android:color="@color/gray_99" />
        </shape>
    </item>
</selector>

