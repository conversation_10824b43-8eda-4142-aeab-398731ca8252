{"hybrid_plugins": ["com.thingclips.smart.jsbridge.jscomponent.plugin.NavJSComponent", "com.thingclips.smart.jsbridge.jscomponent.plugin.PopupJSComponent", "com.thingclips.smart.jsbridge.jscomponent.origin.PhoneJSComponent", "com.thingclips.smart.activator.search.result.plugin.ThingConfigDeviceJSComponent", "com.thingclips.smart.jsbridge.jscomponent.plugin.ImagePickerJSComponent", "com.thingclips.smart.jsbridge.jscomponent.origin.NavigatorJSComponent", "com.thingclips.smart.jsbridge.jscomponent.plugin.AppJSComponent", "com.thingclips.smart.camera.jsbridge.IPCJSComponent", "com.thingclips.smart.jsbridge.jscomponent.plugin.ClipboardJSComponent", "com.thingclips.smart.logupload.LogJSComponent", "com.thingclips.smart.jsbridge.jscomponent.plugin.UserJSComponent", "com.thingclips.smart.jsbridge.jscomponent.plugin.KVJSComponent", "com.thingclips.smart.jsbridge.apm.plugins.APMJSComponent", "com.thingclips.smart.jsbridge.jscomponent.origin.ToastJSComponent", "com.thingclips.android.tracker.core.webpage.ThingHybridTrackPlugin", "com.thingclips.smart.jsbridge.jscomponent.origin.ImageJSComponent", "com.thingclips.smart.jsbridge.jscomponent.origin.TokenJSComponent", "com.thingclips.security.vas.hybrid.plugin.ThingNavActionComponent", "com.thingclips.smart.jsbridge.jscomponent.origin.AppInfoJSComponent", "com.thingclips.smart.jsbridge.jscomponent.plugin.NetJSComponent", "com.thingclips.smart.jsbridge.jscomponent.plugin.PageJSComponent", "com.thingclips.smart.theme.h5.ThemeJSComponent", "com.thingclips.smart.jsbridge.jscomponent.plugin.UIKitJSComponent", "com.thingclips.smart.jsbridge.jscomponent.plugin.LifecycleJSComponent", "com.thingclips.smart.jsbridge.jscomponent.plugin.HomeDataJSComponent", "com.thing.trackcontrol.jsplugin.JsTrackControlPlugin", "com.thingclips.smart.jsbridge.jscomponent.plugin.MediaPreviewJSComponent", "com.thingclips.security.vas.hybrid.plugin.ThingDoActionComponent", "com.thingclips.smart.jsbridge.jscomponent.origin.UserJSComponent"], "rct_packages": {"panel": ["com.thingclips.smart.rnplugin.trcttransfermanager.TRCTTransferManagerPackage", "com.swmansion.gesturehandler.react.RNGestureHandlerPackage", "com.thingclips.smart.rnplugin.trctvisionmap.TRCTVisionMapPackage", "com.thingclips.smart.rnplugin.trctsensorsmanager.TRCTSensorsManagerPackage", "com.thingclips.smart.rnplugin.trctaudiospectrumanager.TRCTAudioSpectruManagerPackage", "com.thingclips.smart.rnplugin.trctgidmanager.TRCTGIDManagerPackage", "com.thingclips.smart.rnplugin.trctslider.TRCTSliderPackage", "com.horcrux.svg.SvgPackage", "com.thingclips.smart.rnplugin.trctfiledownloadmanager.TRCTFileDownloadManagerPackage", "com.thingclips.smart.rnplugin.imagepickermanager.ImagePickerManagerPackage", "com.thingclips.smart.rnplugin.trctgesturelockviewmanager.TRCTGestureLockViewManagerPackage", "com.thingclips.smart.rnplugin.trcthuecircleview.TRCTHueCircleViewPackage", "com.thingclips.smart.rnplugin.trctzigbeeeventmanager.TRCTZigbeeEventManagerPackage", "com.thingclips.smart.rnplugin.trctthirdmusiccontrol.TRCTThirdMusicControlPackage", "com.thingclips.smart.rnplugin.trctactivatormanager.TRCTActivatorManagerPackage", "com.thingclips.smart.rnplugin.trctencryptimagemanager.TRCTEncryptImageManagerPackage", "com.thingclips.smart.rnplugin.trctblemanager.TRCTBLEManagerPackage", "com.thingclips.smart.rnplugin.trctcameraviewmanager.TRCTCameraViewManagerPackage", "com.thingclips.smart.rnplugin.trctdevicemultimanager.TRCTDeviceMultiManagerPackage", "com.thingclips.smart.rnplugin.trctvisionmanager.TRCTVisionManagerPackage", "com.thingclips.smart.rnplugin.trctrtspmediamanager.TRCTRTSPMediaManagerPackage", "com.thingclips.smart.rnplugin.trcthealthwatchmanager.TRCTHealthWatchManagerPackage", "com.thingclips.smart.rnplugin.trctjsbundleloadermanager.TRCTJSBundleLoaderManagerPackage", "com.thingclips.smart.rnplugin.trctlinechartview.TRCTLineChartViewPackage", "org.reactnative.maskedview.RNCMaskedViewPackage", "com.reactnativecommunity.webview.RNCWebViewPackage", "com.thingclips.smart.rnplugin.trcthomedevmanager.TRCTHomeDevManagerPackage", "com.thingclips.smart.rnplugin.trcttypemapmanager.TRCTTypeMapManagerPackage", "com.thingclips.smart.rnplugin.trctalexawebauthmanager.TRCTAlexaWebAuthManagerPackage", "com.airbnb.android.react.lottie.LottiePackage", "com.thingclips.smart.rnplugin.trctcountryselectmanager.TRCTCountrySelectManagerPackage", "com.thingclips.smart.rnplugin.trctrtspmediaplayermanager.TRCTRTSPMediaPlayerManagerPackage", "com.thingclips.smart.rnplugin.trctchartsmanager.TRCTChartsManagerPackage", "com.thingclips.smart.rnplugin.trctvolumemanager.TRCTVolumeManagerPackage", "com.thingclips.smart.rnplugin.trctofficialgeofencemanager.TRCTOfficialGeofenceManagerPackage", "com.thingclips.smart.rnplugin.trctsharemanager.TRCTShareManagerPackage", "com.thingclips.smart.rnplugin.trctswitch.TRCTSwitchPackage", "com.thingclips.smart.rnplugin.trctlifecyclemanager.TRCTLifecycleManagerPackage", "com.thingclips.smart.rnplugin.trctpicker.TRCTPickerPackage", "com.thingclips.smart.rnplugin.trctmqttmanager.TRCTMqttManagerPackage", "com.thingclips.smart.rnplugin.trctchartmarker.TRCTChartMarkerPackage", "com.thingclips.smart.rnplugin.trctlocalalarmmanager.TRCTLocalAlarmManagerPackage", "com.thingclips.smart.amap.TYRCTAMapPackage", "com.thingclips.smart.rnplugin.trctbtmanager.TRCTBTManagerPackage", "com.thingclips.smart.rnplugin.trctlasermap.TRCTLaserMapPackage", "com.thingclips.smart.rnplugin.trctapmtrackmanager.TRCTAPMTrackManagerPackage", "com.thingclips.smart.rnplugin.trctaudioplayermanager.TRCTAudioPlayerManagerPackage", "com.cmcewen.blurview.BlurViewPackage", "com.thingclips.smart.rnplugin.trctnavmanager.TRCTNavManagerPackage", "com.thingclips.smart.rnplugin.exceptionsmanager.ExceptionsManagerPackage", "com.thingclips.smart.rnplugin.trctdigitalfunbitmapview.TRCTDigitalFunBitmapViewPackage", "com.thingclips.smart.rnplugin.trctimageencryptuploadmanager.TRCTImageEncryptUploadManagerPackage", "com.thingclips.smart.rnplugin.trctoutdoormanager.TRCTOutdoorManagerPackage", "com.BV.LinearGradient.LinearGradientPackage", "com.swmansion.reanimated.ReanimatedPackage", "com.thingclips.smart.rnplugin.trctorientationmanager.TRCTOrientationManagerPackage", "com.thingclips.smart.rnplugin.trctmultiimagepickermanager.TRCTMultiImagePickerManagerPackage", "com.thingclips.smart.rnplugin.trctthememanager.TRCTThemeManagerPackage", "com.thingclips.smart.rnplugin.trctavsmanager.TRCTAVSManagerPackage", "com.thingclips.smart.rnplugin.frescomodule.FrescoModulePackage", "com.thingclips.smart.rnplugin.trctbeaconscanadvmanager.TRCTBeaconScanAdvManagerPackage", "com.thingclips.smart.rnplugin.trctpublicblebeaconmanager.TRCTPublicBLEBeaconManagerPackage", "com.thingclips.smart.rnplugin.trcttopbar.TRCTTopBarPackage", "com.thingclips.smart.rnplugin.trctsensorsdbmanager.TRCTSensorsDBManagerPackage", "com.th3rdwave.safeareacontext.SafeAreaContextPackage", "com.thingclips.smart.rnplugin.trctmusicmanager.TRCTMusicManagerPackage", "com.thingclips.smart.rnplugin.trctpbtbridgemanager.TRCTPBTBridgeManagerPackage", "com.thingclips.smart.rnplugin.trctpaneldevicemanager.TRCTPanelDeviceManagerPackage", "com.thingclips.smart.rnplugin.rnviewshot.RNViewShotPackage", "com.thingclips.smart.rnplugin.trcthomemanager.TRCTHomeManagerPackage", "com.thingclips.fetch.RNFetchBlobPackage", "com.thingclips.smart.rnplugin.trctlasermanager.TRCTLaserManagerPackage", "com.thingclips.smart.rnplugin.trctmeshpanelmanager.TRCTMeshPanelManagerPackage", "com.swmansion.rnscreens.RNScreensPackage", "com.thingclips.smart.rnplugin.trctnumberpicker.TRCTNumberPickerPackage", "com.thingclips.smart.rnplugin.trctfacealivedetectmanager.TRCTFaceAliveDetectManagerPackage", "com.thingclips.smart.rnplugin.trctrnstackmanager.TRCTRNStackManagerPackage", "com.thingclips.smart.rnplugin.trctbletimermanager.TRCTBleTimerManagerPackage", "com.thingclips.smart.rnplugin.trctqqwebview.TRCTQQWebViewPackage", "com.thingclips.smart.rnplugin.trctfilemanager.TRCTFileManagerPackage", "com.thingclips.smart.rnplugin.trctiotcardmanager.TRCTIoTCardManagerPackage", "com.thingclips.smart.rnplugin.trcthealthcentermanager.TRCTHealthCenterManagerPackage", "com.thingclips.smart.rnplugin.trctpanelmanager.TRCTPanelManagerPackage", "com.thingclips.smart.rnplugin.trctwheelviewmanager.ITRCTWheelViewManagerPackage", "com.thingclips.smart.rnplugin.trctsysutilsmanager.TRCTSysUtilsManagerPackage", "com.thingclips.smart.rnplugin.trctpointmap.TRCTPointMapPackage", "com.thingclips.smart.rnplugin.trctnewtopbar.TRCTNewTopBarPackage", "com.thingclips.smart.rnplugin.trctspeakermanager.TRCTSpeakerManagerPackage", "com.thingclips.smart.rnplugin.rctvideomanager.RCTVideoManagerPackage", "com.thingclips.smart.rnplugin.trctmultilinechartview.TRCTMultiLineChartViewPackage", "com.thingclips.smart.rnplugin.trctcameraviewmotionmanager.TRCTCameraViewMotionManagerPackage", "com.thingclips.smart.rnplugin.trctencryptimagedownloadmanager.TRCTEncryptImageDownloadManagerPackage", "com.thingclips.smart.rnplugin.trctcurvechartview.TRCTCurveChartViewPackage", "com.thingclips.smart.rnplugin.trctroutegatewaymanager.TRCTRouteGatewayManagerPackage", "com.thingclips.smart.rnplugin.trctstandardgroupmanager.TRCTStandardGroupManagerPackage", "com.thingclips.smart.rnplugin.trctbleextdevicemanager.TRCTBleExtDeviceManagerPackage", "com.thingclips.smart.rnplugin.trctapmeventmanager.TRCTAPMEventManagerPackage", "com.thingclips.smart.rnplugin.trctpublicblelockmanager.TRCTPublicBLELockManagerPackage", "com.thingclips.smart.rnplugin.trctblefilepushmanager.TRCTBleFilePushManagerPackage", "com.thingclips.smart.rnplugin.trctpublicmanager.TRCTPublicManagerPackage", "com.thingclips.smart.rnplugin.trctscenepanelmanager.TRCTScenePanelManagerPackage", "com.thingclips.smart.rnplugin.trctusermanager.TRCTUserManagerPackage", "com.thingclips.smart.rnplugin.trctcameraaudiomanager.TRCTCameraAudioManagerPackage"], "camera": ["com.thingclips.smart.rnplugin.trctcameramanager.TRCTCameraManagerPackage", "com.thingclips.smart.rnplugin.trctcameramessagemediaplayermanager.TRCTCameraMessageMediaPlayerManagerPackage", "com.thingclips.smart.rnplugin.trctcameraplayer.TRCTCameraPlayerPackage", "com.thingclips.smart.rnplugin.trctthingcameraplayer.TRCTThingCameraPlayerPackage", "com.thingclips.smart.rnplugin.trctdoorbellmanager.TRCTDoorBellManagerPackage", "com.thingclips.smart.rnplugin.trctcameramessagemanager.TRCTCameraMessageManagerPackage", "com.thingclips.smart.rnplugin.trctmulticameramanager.TRCTMultiCameraManagerPackage", "com.thingclips.smart.rnplugin.trctcameratimelineviewmanager.TRCTCameraTimeLineViewManagerPackage"]}}