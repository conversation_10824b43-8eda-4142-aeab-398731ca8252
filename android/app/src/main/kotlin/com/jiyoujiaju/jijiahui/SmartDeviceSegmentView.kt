package com.jiyoujiaju.jijiahui

import com.jiyoujiaju.jijiahui.DeviceCardAdapter
import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.util.AttributeSet
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.HorizontalScrollView
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import android.graphics.Rect
import android.widget.Button
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.thingclips.smart.sdk.bean.DeviceBean
import com.thingclips.smart.home.sdk.ThingHomeSdk
import com.thingclips.smart.home.sdk.bean.HomeBean
import com.thingclips.smart.home.sdk.bean.RoomBean
import com.thingclips.smart.home.sdk.callback.IThingHomeResultCallback
import com.thingclips.smart.api.MicroContext
import com.thingclips.smart.panelcaller.api.AbsPanelCallerService

class SmartDeviceSegmentView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    private val creationParams: Map<String, Any?>? = null
) : FrameLayout(context, attrs, defStyleAttr) {

    private val TAG = "SmartDeviceSegmentView"

    private val Float.dp: Int
        get() = (this * resources.displayMetrics.density).toInt()

    private lateinit var viewPager: ViewPager2
    private lateinit var tabLayout: TabLayout
    private var pagerAdapter: DeviceSegmentPagerAdapter? = null
    
    // 动态数据
    private var homeId: Long = 0
    internal var rooms: List<RoomBean> = emptyList() // 改为internal，让Fragment可以访问
    private var tabTitles: List<String> = listOf("所有设备") // 默认只有"所有设备"
    private var tabData: List<TabData> = listOf(TabData("所有设备", null)) // Tab数据，包含设备列表

    // Tab数据类
    data class TabData(
        val title: String,
        val room: RoomBean? = null // null表示"所有设备"
    )

    init {
        Log.d(TAG, "SmartDeviceSegmentView 初始化，参数: $creationParams")
        // 从创建参数中获取homeId
        homeId = (creationParams?.get("homeId") as? Number)?.toLong() ?: 0L
        Log.d(TAG, "接收到的homeId: $homeId")
        
        if (homeId == 0L) {
            Log.e(TAG, "homeId为0，请检查传入参数是否正确")
        }
        
        initView()
        initData() // 请求数据
    }

    private fun initView() {
        // 主容器
        val container = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = LayoutParams(
                LayoutParams.MATCH_PARENT,
                LayoutParams.MATCH_PARENT
            )
        }

        // 1. 顶部导航栏
        container.addView(createNavigationBar())

        // 2. TabLayout
        tabLayout = TabLayout(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                48f.dp
            )
            tabMode = TabLayout.MODE_SCROLLABLE
            tabGravity = TabLayout.GRAVITY_FILL
            setSelectedTabIndicatorColor(Color.parseColor("#FF8C00")) // Orange色指示器
            setTabTextColors(Color.parseColor("#888888"), Color.parseColor("#FF8C00")) // Orange色选中文字
            setBackgroundColor(Color.WHITE)
            elevation = 4f.dp.toFloat()
        }
        container.addView(tabLayout)

        // 3. ViewPager2
        viewPager = ViewPager2(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                0,
                1f
            )
            // 设置预加载页面数量，提高滑动体验
            offscreenPageLimit = 2
        }
        container.addView(viewPager)

        addView(container)
        setBackgroundColor(Color.WHITE)
        
        Log.d(TAG, "SmartDeviceSegmentView UI 构建完成")
    }

    private fun initData() {
        requestTuyaRoomData()
    }

    //获取该家庭组下的房间列表，参考SmartHomeView
    private fun requestTuyaRoomData() {
        Log.d(TAG, "开始获取房间数据，homeId: $homeId")
        
        if (homeId == 0L) {
            Log.w(TAG, "homeId为0，无法获取房间数据")
            updateTabsUI()
            return
        }
        
        ThingHomeSdk.newHomeInstance(homeId).getHomeDetail(object : IThingHomeResultCallback {
            override fun onSuccess(bean: HomeBean) {
                Log.d(TAG, "获取房间数据成功，房间数量: ${bean.rooms.size}")
                // 打印房间详情
                bean.rooms.forEachIndexed { index, room ->
                    Log.d(TAG, "房间[$index]: 名称=${room.name}, ID=${room.roomId}, 设备数量=${room.deviceList.size}")
                }
                
                // 拿到房间数组，参考SmartHomeView
                rooms = bean.rooms
                
                // 构建Tab数据：第一个是"所有设备"，后面是各个房间
                val newTabData = mutableListOf<TabData>()
                newTabData.add(TabData("所有设备", null))
                
                rooms.forEach { room ->
                    Log.d(TAG, "添加房间Tab: ${room.name}")
                    newTabData.add(TabData(room.name, room))
                }
                
                tabData = newTabData
                tabTitles = tabData.map { it.title }
                
                Log.d(TAG, "最终Tab列表: $tabTitles")
                
                // 更新UI
                updateTabsUI()
            }
            
            override fun onError(errorCode: String, errorMsg: String) {
                Log.e(TAG, "获取房间列表失败：$errorCode / $errorMsg")
                // 失败时仍然显示默认的"所有设备"tab
                updateTabsUI()
            }
        })
    }
    
    /**
     * 更新Tabs UI
     */
    private fun updateTabsUI() {
        Log.d(TAG, "更新TabsUI，当前Tab数量: ${tabTitles.size}, Tab列表: $tabTitles")
        post {
            setupViewPager()
        }
    }
    
    /**
     * 刷新数据 - 外部调用接口
     */
    fun refreshData() {
        Log.d(TAG, "外部请求刷新数据")
        requestTuyaRoomData()
    }
    
    /**
     * 释放资源
     */
    fun cleanup() {
        Log.d(TAG, "cleanup() 释放资源")
        // 这里可以添加需要释放的资源
    }
    
    companion object {
        /**
         * 创建SmartDeviceSegmentView的辅助方法
         * @param context 上下文
         * @param homeId 家庭ID，从SmartHomeView传入
         */
        fun create(context: Context, homeId: Long): SmartDeviceSegmentView {
            val params = mapOf("homeId" to homeId)
            return SmartDeviceSegmentView(context, creationParams = params)
        }
    }

    private fun createNavigationBar(): LinearLayout {
        val navigationBar = LinearLayout(context).apply {
            orientation = LinearLayout.HORIZONTAL
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                56f.dp
            )
            gravity = Gravity.CENTER_VERTICAL
            setBackgroundColor(Color.WHITE)
            elevation = 4f.dp.toFloat()
            setPadding(16f.dp, 0, 16f.dp, 0)
        }

        // 返回按钮
        navigationBar.addView(ImageView(context).apply {
            setImageResource(android.R.drawable.ic_menu_revert)
            layoutParams = LinearLayout.LayoutParams(24f.dp, 24f.dp).apply {
                marginEnd = 16f.dp
            }
            scaleType = ImageView.ScaleType.CENTER_INSIDE
            setOnClickListener {
                Log.d(TAG, "返回按钮被点击")
                // 在这里处理返回逻辑
                val activity = context.findActivity()
                if (activity != null) {
                    activity.onBackPressed()
                }
            }
        })

        // 标题
        navigationBar.addView(TextView(context).apply {
            text = "设备列表"
            textSize = 18f
            setTextColor(Color.BLACK)
            paint.isFakeBoldText = true
            layoutParams = LinearLayout.LayoutParams(
                0,
                ViewGroup.LayoutParams.WRAP_CONTENT,
                1f
            )
            gravity = Gravity.CENTER_VERTICAL
        })

        return navigationBar
    }

    private fun setupViewPager() {
        Log.d(TAG, "setupViewPager - 设置ViewPager2和TabLayout联动")
        
        // 检查数据是否就绪
        if (tabData.isEmpty()) {
            Log.w(TAG, "tabData为空，跳过setupViewPager")
            return
        }

        // 获取FragmentActivity
        val activity = context.findActivity() as? FragmentActivity
        if (activity == null) {
            Log.e(TAG, "无法获取FragmentActivity，跳过setupViewPager")
            return
        }

        try {
            // 创建适配器
            pagerAdapter = DeviceSegmentPagerAdapter(activity, tabData, this)
            viewPager.adapter = pagerAdapter

            // 使用TabLayoutMediator连接TabLayout和ViewPager2，实现手势滑动联动
            TabLayoutMediator(tabLayout, viewPager) { tab, position ->
                tab.text = tabTitles[position]
            }.attach()

            Log.d(TAG, "ViewPager2设置完成，Tab数量: ${tabTitles.size}")
        } catch (e: Exception) {
            Log.e(TAG, "setupViewPager失败: ${e.message}", e)
        }
    }

    /**
     * 获取所有设备（用于"所有设备"页面）
     * 改为异步方式，通过回调返回结果
     */
    fun getAllDevices(callback: (List<DeviceBean>) -> Unit) {
        ThingHomeSdk.newHomeInstance(homeId).getHomeDetail(object : IThingHomeResultCallback {
            override fun onSuccess(bean: HomeBean) {
                val allDevices = bean.deviceList
                Log.d(TAG, "getAllDevices: 总设备数量 ${allDevices.size}")
                callback(allDevices)
            }
            override fun onError(errorCode: String, errorMsg: String) {
                Log.e(TAG, "获取所有设备失败：$errorCode / $errorMsg")
                callback(emptyList())
            }
        })
    }
}

// ViewPager2适配器
class DeviceSegmentPagerAdapter(
    fragmentActivity: FragmentActivity,
    private val tabData: List<SmartDeviceSegmentView.TabData>,
    private val segmentView: SmartDeviceSegmentView
) : FragmentStateAdapter(fragmentActivity) {

    override fun getItemCount(): Int = tabData.size

    override fun createFragment(position: Int): Fragment {
        return DeviceListFragment.newInstance(tabData[position], segmentView)
    }
}

// Fragment页面
class DeviceListFragment : Fragment() {
    companion object {
        private const val ARG_TITLE = "arg_title"
        private const val ARG_ROOM_ID = "arg_room_id"
        private const val ARG_IS_ALL_DEVICES = "arg_is_all_devices"

        fun newInstance(tabData: SmartDeviceSegmentView.TabData, segmentView: SmartDeviceSegmentView): DeviceListFragment {
            val fragment = DeviceListFragment()
            fragment.segmentView = segmentView
            val args = Bundle()
            args.putString(ARG_TITLE, tabData.title)
            args.putBoolean(ARG_IS_ALL_DEVICES, tabData.room == null)
            tabData.room?.let { room ->
                args.putLong(ARG_ROOM_ID, room.roomId)
                args.putString("ROOM_NAME", room.name) // 添加房间名称
            }
            fragment.arguments = args
            return fragment
        }
    }

    private var segmentView: SmartDeviceSegmentView? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val title = arguments?.getString(ARG_TITLE) ?: "未知"
        val isAllDevices = arguments?.getBoolean(ARG_IS_ALL_DEVICES, true) ?: true
        val roomId = arguments?.getLong(ARG_ROOM_ID, 0L) ?: 0L
        
        Log.d("DeviceListFragment", "onCreateView - title: $title, isAllDevices: $isAllDevices, roomId: $roomId")

        // 创建根布局 - 使用FrameLayout作为根容器
        val rootLayout = FrameLayout(requireContext()).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            setBackgroundColor(Color.parseColor("#F5F5F5"))
        }

        // 异步获取设备数据并更新UI
        if (isAllDevices) {
            // 获取所有设备
            segmentView?.getAllDevices { devices ->
                activity?.runOnUiThread {
                    updateDeviceUI(rootLayout, devices, title)
                }
            } ?: run {
                updateDeviceUI(rootLayout, emptyList(), title)
            }
        } else {
            // 获取房间设备
            val devices = try {
                getDevicesFromRoom(roomId)
            } catch (e: Exception) {
                Log.e("DeviceListFragment", "获取设备数据时出错: ${e.message}", e)
                emptyList()
            }
            updateDeviceUI(rootLayout, devices, title)
        }

        return rootLayout
    }

    /**
     * 更新设备UI显示
     */
    private fun updateDeviceUI(rootLayout: FrameLayout, devices: List<DeviceBean>, title: String) {
        Log.d("DeviceListFragment", "设备数量: ${devices.size}")

        // 清空之前的内容
        rootLayout.removeAllViews()

        if (devices.isEmpty()) {
            // 如果没有设备，显示空状态
            rootLayout.addView(TextView(requireContext()).apply {
                text = "${title}暂无设备"
                textSize = 16f
                setTextColor(Color.GRAY)
                gravity = Gravity.CENTER
                layoutParams = FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.MATCH_PARENT
                )
            })
        } else {
            // 创建RecyclerView显示设备
            val recyclerView = RecyclerView(requireContext()).apply {
                layoutParams = FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.MATCH_PARENT
                ).apply {
                    val margin = (16 * resources.displayMetrics.density).toInt()
                    setMargins(0, margin, 0, margin)
                }
                layoutManager = GridLayoutManager(requireContext(), 2)
                setHasFixedSize(true)

                // 添加间距装饰
                addItemDecoration(object : RecyclerView.ItemDecoration() {
                    override fun getItemOffsets(
                        outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
                    ) {
                        val position = parent.getChildAdapterPosition(view)
                        val spanCount = 2
                        val spacing = (12 * resources.displayMetrics.density).toInt()
                        val margin = (16 * resources.displayMetrics.density).toInt()
                        val column = position % spanCount

                        outRect.left = if (column == 0) margin else spacing / 2
                        outRect.right = if (column == spanCount - 1) margin else spacing / 2
                        outRect.top = spacing / 2
                        outRect.bottom = spacing / 2
                    }
                })
            }

            // 创建设备适配器
            val deviceCardAdapter = DeviceCardAdapter(requireContext(), devices).apply {
                onItemClick = { device: DeviceBean, pos: Int ->
                    Log.d("DeviceListFragment", "点击了设备：${device.name} ${device.devId}，位置：$pos")
                    try {
                        val service = MicroContext.getServiceManager().findServiceByInterface<AbsPanelCallerService>(
                            AbsPanelCallerService::class.java.name
                        )
                        if (isAdded && activity != null) {
                            service.goPanelWithCheckAndTip(requireActivity(), device.devId)
                        }
                    } catch (e: Exception) {
                        Log.e("DeviceListFragment", "打开设备面板失败: ${e.message}")
                    }
                }
            }

            recyclerView.adapter = deviceCardAdapter
            rootLayout.addView(recyclerView)
        }
    }

    /**
     * 从指定房间获取设备列表
     */
    private fun getDevicesFromRoom(roomId: Long): List<DeviceBean> {
        return try {
            val rooms = segmentView?.rooms ?: emptyList()
            val targetRoom = rooms.find { it.roomId == roomId }
            targetRoom?.deviceList ?: emptyList()
        } catch (e: Exception) {
            Log.e("DeviceListFragment", "从房间获取设备失败: ${e.message}", e)
            emptyList()
        }
    }
}

// 扩展函数，用于查找Activity
fun Context.findActivity(): android.app.Activity? {
    var context = this
    while (context is android.content.ContextWrapper) {
        if (context is android.app.Activity) {
            return context
        }
        context = context.baseContext
    }
    return null
}
