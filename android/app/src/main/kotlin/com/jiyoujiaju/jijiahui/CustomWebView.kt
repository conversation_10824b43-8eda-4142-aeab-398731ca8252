package com.jiyoujiaju.jijiahui

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.AttributeSet
import android.util.Log
import android.view.ViewGroup
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.ImageButton
import android.widget.ImageView

/**
 * 自定义 WebView，右上角浮动两个按钮：
 *  - 右侧 "+" 按钮尺寸随图标自适应
 *  - 左侧 project 按钮固定为 [LEFT_BTN_SIZE_DP] 大小
 */
class CustomWebView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : WebView(context, attrs) {

    companion object {
        private const val TAG = "CustomWebView"
        private const val MARGIN_DP = 16     // 距离屏幕边缘
        private const val GAP_DP = 20        // 两按钮间隔
        private const val LEFT_BTN_SIZE_DP = 44 // 左侧按钮大小（dp）
    }

    /** 右侧 "+" 按钮 */
    private lateinit var overlayButton: ImageButton
    /** 左侧 project 按钮 */
    private lateinit var leftButton: ImageButton
    /** 点击回调 */
    private var overlayButtonClickListener: (() -> Unit)? = null
    /** project按钮点击回调 */
    private var projectButtonClickListener: (() -> Unit)? = null

    /** 供外部注册点击回调 */
    fun setOnOverlayButtonClickListener(listener: () -> Unit) {
        overlayButtonClickListener = listener
    }

    /** 供外部注册project按钮点击回调 */
    fun setOnProjectButtonClickListener(listener: () -> Unit) {
        projectButtonClickListener = listener
    }

    init {
        settings.javaScriptEnabled = true
        settings.setSupportMultipleWindows(true)

        webViewClient = object : WebViewClient() {
            override fun shouldOverrideUrlLoading(
                view: WebView,
                request: WebResourceRequest
            ) = handleUrl(request.url.toString())

            @Suppress("OverridingDeprecatedMember")
            override fun shouldOverrideUrlLoading(view: WebView, url: String) = handleUrl(url)

            private fun handleUrl(url: String): Boolean = when {
                url.startsWith("http://") || url.startsWith("https://") -> false
                url.startsWith("baiduboxapp://") -> {
                    try {
                        val intent = Intent.parseUri(url, Intent.URI_INTENT_SCHEME)
                        context.startActivity(intent)
                    } catch (e: Exception) {
                        Log.w(TAG, "无法处理自定义 Scheme: $url", e)
                    }
                    true
                }
                else -> {
                    try {
                        context.startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(url)))
                    } catch (e: Exception) {
                        Log.w(TAG, "找不到处理此 URL 的应用: $url", e)
                    }
                    true
                }
            }
        }

        // WebView 首次布局完成后添加按钮
        post { addOverlayButtons() }
    }

    /** 创建并添加两个浮动按钮 */
    private fun addOverlayButtons() {
        val density = resources.displayMetrics.density

        // ---------- 右侧 "+" 按钮（自适应尺寸） ----------
        overlayButton = ImageButton(context).apply {
            setImageResource(R.drawable.add)
            background = null
            setOnClickListener { overlayButtonClickListener?.invoke() }
        }
        // 获取图标实际尺寸
        val unspecified = MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
        overlayButton.measure(unspecified, unspecified)
        val ovW = overlayButton.measuredWidth
        val ovH = overlayButton.measuredHeight
        addView(overlayButton, ViewGroup.LayoutParams(ovW, ovH))

        // ---------- 左侧 project 按钮（固定尺寸） ----------
        val leftSizePx = (LEFT_BTN_SIZE_DP * density).toInt()
        leftButton = ImageButton(context).apply {
            setImageResource(R.drawable.project)
            background = null
            scaleType = ImageView.ScaleType.CENTER_INSIDE // ✅ 正确枚举
            setOnClickListener { projectButtonClickListener?.invoke() }
        }
        addView(leftButton, ViewGroup.LayoutParams(leftSizePx, leftSizePx))
    }

    /** 布局两个按钮 */
    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        super.onLayout(changed, l, t, r, b)
        if (!::overlayButton.isInitialized || !::leftButton.isInitialized) return

        val density = resources.displayMetrics.density
        val margin = (MARGIN_DP * density).toInt()
        val gap = (GAP_DP * density).toInt()
        val extraOffset = (12 * density).toInt()

        val bw = overlayButton.measuredWidth
        val bh = overlayButton.measuredHeight
        val lw = leftButton.measuredWidth
        val lh = leftButton.measuredHeight
        val parentWidth = r - l

        // 右侧 "+"
        overlayButton.layout(
            parentWidth - margin - bw,
            margin + extraOffset,
            parentWidth - margin,
            margin + extraOffset + bh
        )

        // 左侧 project
        leftButton.layout(
            parentWidth - margin - bw - gap - lw ,
            margin + extraOffset,
            parentWidth - margin - bw,
            margin + extraOffset + lh
        )
    }

    /** 加载首页 */
    fun loadHomePage() {
        loadUrl("https://vr.justeasy.cn/view/xz165se6x8k14880-1657179172.html")
    }

    override fun onDetachedFromWindow() {
        removeAllViews()
        destroy()
        super.onDetachedFromWindow()
    }
}