package com.jiyoujiaju.jijiahui

import android.os.Bundle
import android.util.Log
import android.widget.FrameLayout
import androidx.appcompat.app.AppCompatActivity

class SmartDeviceSegmentActivity : AppCompatActivity() {

    private val TAG = "SmartDeviceSegmentActivity"
    
    companion object {
        const val EXTRA_HOME_ID = "extra_home_id"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "SmartDeviceSegmentActivity 创建")

        // 从Intent中获取homeId
        val homeId = intent.getLongExtra(EXTRA_HOME_ID, 0L)
        Log.d(TAG, "接收到的homeId: $homeId")

        // 创建一个容器
        val container = FrameLayout(this).apply {
            layoutParams = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT
            )
        }

        // 使用create方法创建SmartDeviceSegmentView并传递homeId
        val smartDeviceSegmentView = SmartDeviceSegmentView.create(this, homeId)
        container.addView(smartDeviceSegmentView)

        setContentView(container)
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "SmartDeviceSegmentActivity 销毁")
    }
} 