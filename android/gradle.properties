org.gradle.jvmargs=-Xmx12G -XX:MaxMetaspaceSize=6G -XX:ReservedCodeCacheSize=1G -XX:+HeapDumpOnOutOfMemoryError -Dhttps.protocols=TLSv1.2,TLSv1.3
org.gradle.java.installations.paths=/opt/homebrew/opt/openjdk@11/libexec/openjdk.jdk/Contents/Home
android.useAndroidX=true
android.enableJetifier=true
bizBomVersion=6.0.0
sdkVersion=6.2.2

# 网络配置
org.gradle.daemon=true
org.gradle.parallel=true
org.gradle.configureondemand=true

# 解决网络连接问题
systemProp.https.protocols=TLSv1.2,TLSv1.3
systemProp.http.keepAlive=true
systemProp.http.maxConnections=10
systemProp.http.maxConnectionsPerRoute=10

# R8 优化配置
android.enableR8.fullMode=true