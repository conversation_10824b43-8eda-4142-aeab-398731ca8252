import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';
import 'package:flutter_smarthome/core/models/area_model.dart';
import 'package:flutter_smarthome/shared/dialogs/area_picker_dialog.dart';

class EditCustomerInfoDialog extends StatefulWidget {
  final String? initialName;
  final String? initialPhone;
  final String? initialIdCard;
  final String? initialRegion;
  final String? initialAddress;
  final Function(String name, String phone, String idCard, String region, String address)? onSubmit;

  const EditCustomerInfoDialog({
    Key? key,
    this.initialName,
    this.initialPhone,
    this.initialIdCard,
    this.initialRegion,
    this.initialAddress,
    this.onSubmit,
  }) : super(key: key);

  static Future<void> show(
    BuildContext context, {
    String? initialName,
    String? initialPhone,
    String? initialIdCard,
    String? initialRegion,
    String? initialAddress,
    Function(String name, String phone, String idCard, String region, String address)? onSubmit,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: EditCustomerInfoDialog(
          initialName: initialName,
          initialPhone: initialPhone,
          initialIdCard: initialIdCard,
          initialRegion: initialRegion,
          initialAddress: initialAddress,
          onSubmit: onSubmit,
        ),
      ),
    );
  }

  @override
  State<EditCustomerInfoDialog> createState() => _EditCustomerInfoDialogState();
}

class _EditCustomerInfoDialogState extends State<EditCustomerInfoDialog> {
  late TextEditingController _nameController;
  late TextEditingController _phoneController;
  late TextEditingController _idCardController;
  late TextEditingController _regionController;
  late TextEditingController _addressController;

  String? _selectedRegion;
  List<Area> _areaList = [];

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.initialName ?? '');
    _phoneController = TextEditingController(text: widget.initialPhone ?? '');
    _idCardController = TextEditingController(text: widget.initialIdCard ?? '');
    _regionController = TextEditingController(text: widget.initialRegion ?? '');
    _addressController = TextEditingController(text: widget.initialAddress ?? '');
    _selectedRegion = widget.initialRegion;

    // 初始化时获取区域数据
    _getAreaData();
  }

  /// 获取脱敏后的手机号
  String _getMaskedPhone(String phone) {
    if (phone.length < 11) {
      return phone;
    }
    return '${phone.substring(0, 3)} **** ${phone.substring(7)}';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _idCardController.dispose();
    _regionController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(),
          _buildForm(),
          _buildSubmitButton(),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: HexColor('#F5F5F5'),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Text(
              '取消',
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey[600],
              ),
            ),
          ),
          Text(
            '编辑客户信息',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          SizedBox(width: 32.w), // 占位，保持标题居中
        ],
      ),
    );
  }

  Widget _buildForm() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        children: [
          _buildFormItem('客户姓名', _nameController, '请输入客户姓名'),
          _buildDivider(),
          _buildPhoneFormItem(),
          _buildDivider(),
          _buildFormItem('身份证号', _idCardController, '请输入身份证号'),
          _buildDivider(),
          _buildRegionFormItem(),
          _buildDivider(),
          _buildAddressFormItem(),
        ],
      ),
    );
  }

  Widget _buildFormItem(
    String label,
    TextEditingController controller,
    String hintText, {
    TextInputType? keyboardType,
  }) {
    return SizedBox(
      height: 44.h,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 100.w,
            height: 44.h,
            alignment: Alignment.centerLeft,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.black,
                height: 1.0, // 设置行高为1.0，确保文本垂直居中
              ),
            ),
          ),
          Expanded(
            child: Container(
              height: 44.h,
              alignment: Alignment.centerLeft,
              child: TextField(
                controller: controller,
                keyboardType: keyboardType,
                maxLines: 1,
                textAlignVertical: TextAlignVertical.center,
                decoration: InputDecoration(
                  hintText: hintText,
                  hintStyle: TextStyle(
                    color: HexColor('#999999'),
                    fontSize: 14.sp,
                    height: 1.0, // 设置提示文本行高
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(vertical: 0), // 明确设置垂直内边距
                  isDense: true,
                ),
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.black,
                  height: 1.0, // 设置输入文本行高
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressFormItem() {
    return SizedBox(
      height: 44.h,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 100.w,
            height: 44.h,
            alignment: Alignment.centerLeft,
            child: Text(
              '地址',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.black,
                height: 1.0, // 设置行高为1.0，确保文本垂直居中
              ),
            ),
          ),
          Expanded(
            child: Container(
              height: 44.h,
              alignment: Alignment.centerLeft,
              child: TextField(
                controller: _addressController,
                maxLines: 1,
                textAlignVertical: TextAlignVertical.center,
                decoration: InputDecoration(
                  hintText: '请输入地址',
                  hintStyle: TextStyle(
                    color: HexColor('#999999'),
                    fontSize: 14.sp,
                    height: 1.0, // 设置提示文本行高
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(vertical: 0), // 明确设置垂直内边距
                  isDense: true,
                ),
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.black,
                  height: 1.0, // 设置输入文本行高
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhoneFormItem() {
    final maskedPhone = _getMaskedPhone(widget.initialPhone ?? '');

    return SizedBox(
      height: 44.h,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 100.w,
            height: 44.h,
            alignment: Alignment.centerLeft,
            child: Text(
              '客户电话',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.black,
                height: 1.0, // 设置行高为1.0，确保文本垂直居中
              ),
            ),
          ),
          Expanded(
            child: Container(
              height: 44.h,
              alignment: Alignment.centerLeft,
              child: Text(
                maskedPhone,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: HexColor('#999999'),
                  height: 1.0, // 设置行高为1.0，确保文本垂直居中
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRegionFormItem() {
    return GestureDetector(
      onTap: _showRegionPicker,
      child: Container(
        height: 44.h,
        color: Colors.transparent, // 确保整行都可以点击
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 100.w,
              height: 44.h,
              alignment: Alignment.centerLeft,
              child: Text(
                '区域',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.black,
                  height: 1.0, // 设置行高为1.0，确保文本垂直居中
                ),
              ),
            ),
            Expanded(
              child: Container(
                height: 44.h,
                alignment: Alignment.centerLeft,
                child: Text(
                  _selectedRegion?.isNotEmpty == true ? _selectedRegion! : '请选择区域',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: _selectedRegion?.isNotEmpty == true
                        ? Colors.black
                        : HexColor('#999999'),
                    height: 1.0, // 设置行高为1.0，确保文本垂直居中
                  ),
                ),
              ),
            ),
            Container(
              height: 44.h,
              alignment: Alignment.center,
              child: Icon(
                Icons.arrow_forward_ios,
                size: 14.w,
                color: HexColor('#999999'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1.h,
      color: HexColor('#F5F5F5'),
    );
  }

  Widget _buildSubmitButton() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: GestureDetector(
        onTap: _handleSubmit,
        child: Container(
          width: double.infinity,
          height: 44.h,
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(8.r),
          ),
          alignment: Alignment.center,
          child: Text(
            '确定',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  void _showRegionPicker() async {
    if (_areaList.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('正在加载区域数据，请稍后重试')),
      );
      return;
    }

    final selectedArea = await AreaPickerDialog.show(
      context,
      areaList: _areaList,
      selectedAreaName: _selectedRegion,
    );

    if (selectedArea != null) {
      setState(() {
        _selectedRegion = selectedArea.originalName;
        _regionController.text = _selectedRegion!;
      });
    }
  }

  // 获取区域列表
  Future<void> _getAreaData() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/home/<USER>/area',
        queryParameters: {
          'parentId': '3101', // 上海市的id
        },
      );
      if (response != null) {
        setState(() {
          _areaList = (response as List<dynamic>)
              .map((e) => Area.fromJson(Map<String, dynamic>.from(e)))
              .toList();
        });
      }
    } catch (e) {
      debugPrint('获取区域数据失败: $e');
    }
  }

  void _handleSubmit() {
    final name = _nameController.text.trim();
    final phone = widget.initialPhone ?? ''; // 手机号不可编辑，使用初始值
    final idCard = _idCardController.text.trim();
    final region = _selectedRegion ?? '';
    final address = _addressController.text.trim();

    // 基本验证
    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入客户姓名')),
      );
      return;
    }

    // 调用回调函数，让父组件处理API调用
    if (widget.onSubmit != null) {
      widget.onSubmit!(name, phone, idCard, region, address);
      Navigator.pop(context); // 关闭对话框
    }
  }

}
