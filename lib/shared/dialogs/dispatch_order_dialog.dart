import 'package:flutter/material.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/shared/dialogs/dispatch_detail_dialog.dart';

class DestinationType {
  final String sourceId;
  final String dispatchType;
  final String dispatchTypeName;
  final String code;

  DestinationType({
    required this.sourceId,
    required this.dispatchType,
    required this.dispatchTypeName,
    required this.code,
  });

  factory DestinationType.fromJson(Map<String, dynamic> json) {
    return DestinationType(
      sourceId: json['sourceId'] ?? '',
      dispatchType: json['dispatchType']?.toString() ?? '',
      dispatchTypeName: json['dispatchTypeName'] ?? '',
      code: json['code'] ?? '',
    );
  }
}

class DispatchOrderDialog extends StatefulWidget {
  final String serviceId;
  final String sourceId;
  final String customerName;
  final String decorationAddress;
  final Function(DestinationType)? onSelected;

  const DispatchOrderDialog({
    super.key,
    required this.serviceId,
    required this.sourceId,
    required this.customerName,
    required this.decorationAddress,
    this.onSelected,
  });

  @override
  State<DispatchOrderDialog> createState() => _DispatchOrderDialogState();

  // 静态方法，用于显示底部推出框
  static Future<DestinationType?> show({
    required BuildContext context,
    required String serviceId,
    required String sourceId,
    required String customerName,
    required String decorationAddress,
    Function(DestinationType)? onSelected,
  }) {
    return showModalBottomSheet<DestinationType>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => DispatchOrderDialog(
        serviceId: serviceId,
        sourceId: sourceId,
        customerName: customerName,
        decorationAddress: decorationAddress,
        onSelected: onSelected,
      ),
    );
  }
}

class _DispatchOrderDialogState extends State<DispatchOrderDialog> {
  List<DestinationType> _dispatchOrderType = [];
  DestinationType? _selectedType;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _getDispatchOrderType();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部拖拽指示器
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 36,
            height: 4,
            decoration: BoxDecoration(
              color: const Color(0xFFE0E0E0),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // 标题栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const SizedBox(width: 24), // 占位，保持标题居中
                const Text(
                  '派单至',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF333333),
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    child: const Icon(
                      Icons.close,
                      size: 20,
                      color: Color(0xFF999999),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 内容区域
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 40),
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF007AFF)),
              ),
            )
          else
            Column(
              children: [
                // 从接口获取的选项
                ..._dispatchOrderType
                    .map((type) => _buildOptionItem(
                          type.dispatchTypeName,
                          () {
                            setState(() {
                              _selectedType = type;
                            });
                          },
                          _selectedType?.dispatchType == type.dispatchType,
                        ))
                    .toList(),

                const SizedBox(height: 20),

                // 底部指示器
                Container(
                  width: 60,
                  height: 4,
                  decoration: BoxDecoration(
                    color: const Color(0xFF333333),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // 底部安全区域
                SizedBox(height: MediaQuery.of(context).padding.bottom + 20),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildOptionItem(String title, VoidCallback onTap, bool isSelected) {
    return GestureDetector(
      onTap: () async {
        print('点击了选项: $title');
        onTap();
        print('选中的类型: ${_selectedType?.dispatchTypeName}');

        // 延迟一点时间显示选中效果
        await Future.delayed(const Duration(milliseconds: 200));

        if (_selectedType != null) {
          print('准备弹出派单详情对话框');
          // 弹出派单详情对话框
          try {
            final result = await DispatchDetailDialog.show(
              context,
              serviceId: widget.serviceId,
              sourceId: widget.sourceId,
              dispatchType: _selectedType!.dispatchType,
              code: _selectedType!.code,
              dispatchTypeName: _selectedType!.dispatchTypeName,
              customerName: widget.customerName,
              decorationAddress: widget.decorationAddress,
              onConfirm: (searchText) {
                // 处理搜索结果的逻辑可以在这里添加
                print('派单搜索内容: $searchText');
              },
            );
            print('派单详情对话框返回结果: $result');

            // 只有在派单成功时才执行回调并关闭当前对话框
            if (result == 'success' && mounted) {
              if (widget.onSelected != null) {
                widget.onSelected!(_selectedType!);
              }
              Navigator.of(context).pop(_selectedType);
            }
          } catch (e) {
            print('弹出派单详情对话框出错: $e');
          }
        } else {
          print('_selectedType 为空');
        }
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFFFFC276).withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: isSelected
              ? Border.all(color: const Color(0xFFFFC276), width: 1)
              : null,
        ),
        child: Text(
          title,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16,
            color:
                isSelected ? const Color(0xFFFFC276) : const Color(0xFF333333),
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  //获取派单类型
  Future<void> _getDispatchOrderType() async {
    final apiManager = ApiManager();
    try {
      final response = await apiManager.get('/api/signature/dispatch/type/list',
          queryParameters: {'serviceId': widget.serviceId});
      if (response != null && mounted) {
        final List<dynamic> dataList = response is List ? response : [];
        setState(() {
          _dispatchOrderType =
              dataList.map((item) => DestinationType.fromJson(item)).toList();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
