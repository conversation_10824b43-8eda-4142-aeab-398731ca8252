import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';
import 'package:flutter_smarthome/features/signature/signature_budget_page.dart';

class PaymentRatioDialog extends StatefulWidget {
  final double totalAmount; //合同总金额
  final String? contractType; //合同类型
  final String? companyName; //默认收款公司
  final String? companyId; //默认收款公司id
  final String? budgetId; //预算id
  final String? packageType; //装修类型
  final String? packageId; //装修id
  final String? esignContractId; //e签宝合同id
  final String? serviceId; //服务id
  final String? startDate; //合同开始日期
  final String? endDate; //合同结束日期
  final String? payType; //付款方式
  final List<Map<String, dynamic>>? paymentList; //付款方式
  final VoidCallback? onSigningCompleted;

  const PaymentRatioDialog({
    Key? key,
    required this.totalAmount,
    this.contractType,
    this.companyName,
    this.companyId,
    this.budgetId,
    this.packageType,
    this.packageId,
    this.esignContractId,
    this.serviceId,
    this.startDate,
    this.endDate,
    this.payType,
    this.paymentList,
    this.onSigningCompleted,
  }) : super(key: key);

  static Future<void> show(
    BuildContext context, {
    required double totalAmount,
    String? contractType,
    String? companyName,
    String? companyId,
    String? budgetId,
    String? packageType,
    String? packageId,
    String? esignContractId,
    String? serviceId,
    String? startDate,
    String? endDate,
    String? payType,
    List<Map<String, dynamic>>? paymentList,
    VoidCallback? onSigningCompleted,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: PaymentRatioDialog(
          totalAmount: totalAmount,
          contractType: contractType,
          companyName: companyName,
          companyId: companyId,
          budgetId: budgetId,
          packageType: packageType,
          packageId: packageId,
          esignContractId: esignContractId,
          serviceId: serviceId,
          startDate: startDate,
          endDate: endDate,
          payType: payType,
          paymentList: paymentList,
          onSigningCompleted: onSigningCompleted,
        ),
      ),
    );
  }

  @override
  State<PaymentRatioDialog> createState() => _PaymentRatioDialogState();
}

class _PaymentRatioDialogState extends State<PaymentRatioDialog> {
  // 收款公司选项
  List<Map<String, dynamic>> companyOptions = [];

  String _selectedCompany = '';
  String _contractStartDate = '';
  String _contractEndDate = '';

  // 付款比例选项
  int _selectedRatioIndex = 0; // 0: 首期65%, 1: 首期90%, 2: 自定义比例

  // 付款项目
  List<PaymentItem> _paymentItems = [];

  // 控制器
  final List<TextEditingController> _percentageControllers = [
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
  ];

  @override
  void initState() {
    super.initState();
    _fetchPaymentCompanies();
    _initializePaymentItems();
    _initializeDates();
    _initializePaymentRatio();
    _updatePaymentItems();
  }

  @override
  void dispose() {
    for (var controller in _percentageControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  // 格式化金额显示，添加千位分隔符
  String _formatAmount(double amount) {
    String amountStr = amount.toStringAsFixed(2);
    return amountStr.replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
  }

  void _initializePaymentItems() {
    _paymentItems = [
      PaymentItem(
        title: '合同签订当日',
        subtitle: '(对预算、设计方案认可)',
        amount: widget.totalAmount * 0.65,
        percentage: 65,
      ),
      PaymentItem(
        title: '施工过程',
        subtitle: '(水、电、管线施工过程验收)',
        amount: 0,
        percentage: 0,
      ),
      PaymentItem(
        title: '油漆工进场前',
        subtitle: '(工期过半)',
        amount: 0,
        percentage: 30,
      ),
      PaymentItem(
        title: '竣工验收当天',
        subtitle: '(竣工验收)',
        amount: 0,
        percentage: 5,
      ),
    ];

    // 初始化控制器的值
    for (int i = 0; i < _paymentItems.length; i++) {
      _percentageControllers[i].text = _paymentItems[i].percentage.toString();
    }
  }

  void _initializeDates() {
    // 如果传入了默认日期，则设置为默认值
    if (widget.startDate != null && widget.startDate!.isNotEmpty) {
      _contractStartDate = widget.startDate!;
    }
    if (widget.endDate != null && widget.endDate!.isNotEmpty) {
      _contractEndDate = widget.endDate!;
    }
  }

  void _initializePaymentRatio() {
    // 根据传入的 payType 设置默认选中的付款比例选项
    if (widget.payType != null) {
      switch (widget.payType) {
        case 'pay65':
          _selectedRatioIndex = 0; // 首期65%
          break;
        case 'pay90':
          _selectedRatioIndex = 1; // 首期90%
          break;
        case 'paySelf':
          _selectedRatioIndex = 2; // 自定义比例
          break;
        default:
          _selectedRatioIndex = 0; // 默认首期65%
      }
    }

    // 根据传入的 paymentList 设置百分比
    if (widget.paymentList != null && widget.paymentList!.isNotEmpty) {
      for (int i = 0;
          i < widget.paymentList!.length && i < _paymentItems.length;
          i++) {
        final paymentData = widget.paymentList![i];
        if (paymentData['proportion'] != null) {
          // proportion 是小数形式（如 0.65），需要转换为百分比
          double proportion = paymentData['proportion'].toDouble();
          int percentage = (proportion * 100).round();

          _paymentItems[i].percentage = percentage;
          _paymentItems[i].amount = widget.totalAmount * proportion;
          _percentageControllers[i].text = percentage.toString();
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTotalAmount(),
                  SizedBox(height: 20.h),
                  _buildCompanySelector(),
                  SizedBox(height: 20.h),
                  _buildDateSelector('合同开工日期', _contractStartDate,
                      (dateString) {
                    setState(() {
                      _contractStartDate = dateString;
                    });
                  }),
                  SizedBox(height: 16.h),
                  _buildDateSelector('合同完工日期', _contractEndDate, (dateString) {
                    setState(() {
                      _contractEndDate = dateString;
                    });
                  }),
                  SizedBox(height: 20.h),
                  _buildPaymentRatioSection(),
                  SizedBox(height: 16.h),
                  _buildPaymentItemsSection(),
                  SizedBox(height: 80.h), // 为底部按钮留出空间
                ],
              ),
            ),
          ),
          _buildBottomButton(),
        ],
      ),
    );
  }

  // 构建头部
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '请设置付款比例',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(
              Icons.close,
              size: 24.sp,
              color: Colors.black,
            ),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  // 构建合同总金额
  Widget _buildTotalAmount() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '合同总金额：',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.black,
            ),
          ),
          Text(
            '¥${_formatAmount(widget.totalAmount)}元',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: HexColor('#FF6B35'),
            ),
          ),
        ],
      ),
    );
  }

  // 构建收款公司选择器
  Widget _buildCompanySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '*',
              style: TextStyle(
                color: Colors.red,
                fontSize: 16.sp,
              ),
            ),
            Text(
              '收款公司：',
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.black,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        GestureDetector(
          onTap: _showCompanyPicker,
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: HexColor('#E0E0E0')),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    _selectedCompany.isNotEmpty ? _selectedCompany : '请选择收款公司',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: _selectedCompany.isNotEmpty
                          ? Colors.black
                          : HexColor('#999999'),
                    ),
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: HexColor('#999999'),
                  size: 20.sp,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 构建日期选择器
  Widget _buildDateSelector(
      String label, String selectedDate, Function(String) onDateSelected) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '*',
              style: TextStyle(
                color: Colors.red,
                fontSize: 16.sp,
              ),
            ),
            Text(
              label,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.black,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        GestureDetector(
          onTap: () => _showDatePicker(label, onDateSelected),
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: HexColor('#E0E0E0')),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  selectedDate.isNotEmpty ? selectedDate : '请选择',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: selectedDate.isNotEmpty
                        ? Colors.black
                        : HexColor('#999999'),
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_right,
                  color: Colors.grey[400],
                  size: 20.w,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 构建付款比例选择区域
  Widget _buildPaymentRatioSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '付款比例：',
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.black,
          ),
        ),
        SizedBox(height: 12.h),
        // 首期65%选项
        _buildRatioOption(
          index: 0,
          title: '首期65%',
          isSelected: _selectedRatioIndex == 0,
        ),
        SizedBox(height: 8.h),
        // 首期90%选项
        _buildRatioOption(
          index: 1,
          title: '首期90%',
          isSelected: _selectedRatioIndex == 1,
        ),
        SizedBox(height: 8.h),
        // 自定义比例选项
        _buildCustomRatioOption(),
      ],
    );
  }

  // 构建比例选项
  Widget _buildRatioOption({
    required int index,
    required String title,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedRatioIndex = index;
          _updatePaymentItems(isUserSelection: true);
        });
      },
      child: Row(
        children: [
          Container(
            width: 20.w,
            height: 20.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? HexColor('#1976D2') : HexColor('#CCCCCC'),
                width: 2,
              ),
            ),
            child: isSelected
                ? Center(
                    child: Container(
                      width: 10.w,
                      height: 10.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: HexColor('#1976D2'),
                      ),
                    ),
                  )
                : null,
          ),
          SizedBox(width: 8.w),
          Text(
            title,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  // 构建自定义比例选项
  Widget _buildCustomRatioOption() {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              _selectedRatioIndex = 2;
              _updatePaymentItems(isUserSelection: true);
            });
          },
          child: Container(
            width: 20.w,
            height: 20.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: _selectedRatioIndex == 2
                    ? HexColor('#1976D2')
                    : HexColor('#CCCCCC'),
                width: 2,
              ),
            ),
            child: _selectedRatioIndex == 2
                ? Center(
                    child: Container(
                      width: 10.w,
                      height: 10.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: HexColor('#1976D2'),
                      ),
                    ),
                  )
                : null,
          ),
        ),
        SizedBox(width: 8.w),
        Text(
          '自定义比例',
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  // 构建付款项目区域
  Widget _buildPaymentItemsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 四个付款项目
        for (int i = 0; i < _paymentItems.length; i++) ...[
          _buildPaymentItemCard(i),
          if (i < _paymentItems.length - 1) SizedBox(height: 8.h),
        ],
        SizedBox(height: 12.h),
        // 合计行
        _buildTotalRow(),
      ],
    );
  }

  // 构建付款项目卡片
  Widget _buildPaymentItemCard(int index) {
    final item = _paymentItems[index];
    // 第一个输入框根据选择的付款比例选项控制，其余都可输入
    final bool isEditable = index == 0 ? _selectedRatioIndex == 2 : true;

    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: HexColor('#E0E0E0')),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题行
          Row(
            children: [
              Container(
                width: 6.w,
                height: 6.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: HexColor('#1976D2'),
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.title,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                    Text(
                      item.subtitle,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: HexColor('#666666'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          // 付款比例
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '付款比例：',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: HexColor('#666666'),
                ),
              ),
              Row(
                children: [
                  Container(
                    width: 60.w,
                    height: 32.h,
                    decoration: BoxDecoration(
                      color: isEditable ? Colors.white : HexColor('#F5F5F5'),
                      border: Border.all(color: HexColor('#E0E0E0')),
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Center(
                      child: TextField(
                        controller: _percentageControllers[index],
                        enabled: isEditable,
                        keyboardType: TextInputType.number,
                        textAlign: TextAlign.center,
                        textAlignVertical: TextAlignVertical.center,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.black,
                          height: 1.0,
                        ),
                        decoration: InputDecoration(
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 4.w,
                            vertical: 0,
                          ),
                          isDense: true,
                          isCollapsed: true,
                        ),
                        onChanged: (value) {
                          _onPercentageChanged(index, value);
                        },
                      ),
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    '%',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 6.h),
          // 付款金额
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '付款金额：',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: HexColor('#666666'),
                ),
              ),
              Text(
                '${_formatAmount(item.amount)} 元',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: HexColor('#FF6B35'),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建合计行
  Widget _buildTotalRow() {
    double totalPercentage =
        _paymentItems.fold(0, (sum, item) => sum + item.percentage);
    double totalAmount =
        _paymentItems.fold(0, (sum, item) => sum + item.amount);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color:
            totalPercentage == 100 ? HexColor('#E8F5E8') : HexColor('#FFF2F2'),
        border: Border.all(
          color: totalPercentage == 100
              ? HexColor('#4CAF50')
              : HexColor('#F44336'),
          width: 1.w,
        ),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Text(
                '合计',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
              SizedBox(width: 16.w),
              Text(
                '比例: ${totalPercentage.toInt()} %',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: totalPercentage == 100
                      ? HexColor('#4CAF50')
                      : HexColor('#F44336'),
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (totalPercentage != 100) ...[
                SizedBox(width: 8.w),
                Text(
                  totalPercentage < 100
                      ? '(还需${100 - totalPercentage.toInt()}%)'
                      : '(超出${totalPercentage.toInt() - 100}%)',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: HexColor('#F44336'),
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ],
          ),
          Text(
            _formatAmount(totalAmount),
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  // 处理比例变化
  void _onPercentageChanged(int index, String value) {
    int percentage = int.tryParse(value) ?? 0;
    if (percentage < 0) percentage = 0;

    // 动态限制范围：计算其他项目的总和，确保当前输入不会导致总和超过100%
    int otherItemsTotal = 0;
    for (int i = 0; i < _paymentItems.length; i++) {
      if (i != index) {
        otherItemsTotal += _paymentItems[i].percentage;
      }
    }

    int maxAllowed = 100 - otherItemsTotal;
    if (percentage > maxAllowed) {
      percentage = maxAllowed;
    }

    setState(() {
      _paymentItems[index].percentage = percentage;
      _paymentItems[index].amount = widget.totalAmount * (percentage / 100);

      // 更新控制器显示正确的值
      if (_percentageControllers[index].text != percentage.toString()) {
        _percentageControllers[index].text = percentage.toString();
        _percentageControllers[index].selection = TextSelection.fromPosition(
          TextPosition(offset: _percentageControllers[index].text.length),
        );
      }
    });
  }

  // 检查是否满足所有条件
  bool get _isFormValid {
    // 检查日期是否选择
    if (_contractStartDate.isEmpty || _contractEndDate.isEmpty) return false;

    // 检查收款公司是否选择
    if (_selectedCompany.isEmpty) return false;

    // 检查比例总和是否为100%
    double totalPercentage =
        _paymentItems.fold(0, (sum, item) => sum + item.percentage);
    if (totalPercentage != 100) return false;

    return true;
  }

  // 构建底部按钮
  Widget _buildBottomButton() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 48.h,
          child: ElevatedButton(
            onPressed: _isFormValid ? _handleSubmit : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: _isFormValid ? Colors.black : Colors.grey[400],
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: Text(
              '去签约',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 显示公司选择器
  void _showCompanyPicker() {
    List<String> companyNames = companyOptions
        .map((option) => option['companyName'] ?? '')
        .toList()
        .cast<String>();

    int selectedIndex = companyNames.indexOf(_selectedCompany);
    // 如果没有找到匹配的公司，不默认选中第一个，而是设置为-1（无选中状态）
    if (selectedIndex == -1) {
      selectedIndex = 0; // 设置为第一个选项作为初始显示位置，但不表示选中
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 300.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
        ),
        child: Column(
          children: [
            // 拖拽指示器
            Container(
              width: 36.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 8.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),

            // 标题栏
            Container(
              height: 50.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey[200]!,
                    width: 1.h,
                  ),
                ),
              ),
              child: Row(
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      '取消',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16.sp,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      '选择收款公司',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      setState(() {
                        _selectedCompany = companyNames[selectedIndex];
                      });
                    },
                    child: Text(
                      '确定',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 选择器
            Expanded(
              child: CupertinoPicker(
                itemExtent: 44.h,
                scrollController:
                    FixedExtentScrollController(initialItem: selectedIndex),
                onSelectedItemChanged: (index) {
                  selectedIndex = index;
                },
                children: companyNames
                    .map(
                      (option) => Center(
                        child: Text(
                          option,
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示日期选择器
  void _showDatePicker(String title, Function(String) onDateSelected) {
    // 解析当前日期，如果没有则使用今天
    DateTime selectedDate = DateTime.now();

    // 根据标题判断是开工日期还是完工日期，并尝试解析已有的日期
    String currentDateString = '';
    if (title.contains('开工')) {
      currentDateString = _contractStartDate;
    } else if (title.contains('完工')) {
      currentDateString = _contractEndDate;
    }

    // 如果已有日期，尝试解析
    if (currentDateString.isNotEmpty) {
      try {
        List<String> dateParts = currentDateString.split('-');
        if (dateParts.length == 3) {
          int year = int.parse(dateParts[0]);
          int month = int.parse(dateParts[1]);
          int day = int.parse(dateParts[2]);
          selectedDate = DateTime(year, month, day);
        }
      } catch (e) {
        // 解析失败，使用今天的日期
        selectedDate = DateTime.now();
      }
    }

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 300.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
        ),
        child: Column(
          children: [
            // 拖拽指示器
            Container(
              width: 36.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 8.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),

            // 标题栏
            Container(
              height: 50.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey[200]!,
                    width: 1.h,
                  ),
                ),
              ),
              child: Row(
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      '取消',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16.sp,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      String formattedDate =
                          '${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}';
                      onDateSelected(formattedDate);
                    },
                    child: Text(
                      '确定',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 日期选择器
            Expanded(
              child: CupertinoDatePicker(
                mode: CupertinoDatePickerMode.date,
                initialDateTime: selectedDate,
                minimumDate: DateTime(2020),
                maximumDate: DateTime(2030),
                onDateTimeChanged: (DateTime newDate) {
                  selectedDate = newDate;
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  //获取收款公司
  Future<void> _fetchPaymentCompanies() async {
    final apiManager = ApiManager();
    try {
      final response = await apiManager
          .get('/api/signature/company/list', queryParameters: {});

      if (response != null && response is List) {
        setState(() {
          companyOptions = List<Map<String, dynamic>>.from(response);

          // 如果外部有值传进来就默认填充，然后去和列表里的匹配，匹配到就默认选中
          if (widget.companyName != null && widget.companyName!.isNotEmpty) {
            // 尝试在列表中找到匹配的公司
            final matchedCompany = companyOptions.firstWhere(
              (company) => company['companyName'] == widget.companyName,
              orElse: () => {},
            );
            // 只有找到匹配的公司时才设置选中状态
            if (matchedCompany.isNotEmpty) {
              _selectedCompany = matchedCompany['companyName'] ?? '';
            }
          }
          // 如果没有外部传入的公司名称，保持 _selectedCompany 为空字符串，不默认选中第一个
        });
      }
    } catch (e) {
      debugPrint('Failed to load payment companies: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('加载收款公司列表失败，请稍后再试')),
        );
      }
    }
  }

  // 更新付款项目
  void _updatePaymentItems({bool isUserSelection = false}) {
    setState(() {
      if (_selectedRatioIndex == 0 && isUserSelection) {
        // 首期65% - 设置默认比例：65, 30, 0, 5
        List<int> defaultPercentages = [65, 0, 30, 5];
        for (int i = 0;
            i < _paymentItems.length && i < defaultPercentages.length;
            i++) {
          _paymentItems[i].percentage = defaultPercentages[i];
          _paymentItems[i].amount =
              widget.totalAmount * (defaultPercentages[i] / 100);
          _percentageControllers[i].text = defaultPercentages[i].toString();
        }
      } else if (_selectedRatioIndex == 1 && isUserSelection) {
        // 首期90% - 设置默认比例：90, 0, 5, 5
        List<int> defaultPercentages = [90, 0, 5, 5];
        for (int i = 0;
            i < _paymentItems.length && i < defaultPercentages.length;
            i++) {
          _paymentItems[i].percentage = defaultPercentages[i];
          _paymentItems[i].amount =
              widget.totalAmount * (defaultPercentages[i] / 100);
          _percentageControllers[i].text = defaultPercentages[i].toString();
        }
      } else {
        // 自定义模式或初始化时 - 重新计算所有项目的金额
        for (int i = 0; i < _paymentItems.length; i++) {
          _paymentItems[i].amount =
              widget.totalAmount * (_paymentItems[i].percentage / 100);
        }
      }
    });
  }

  // 处理提交
  void _handleSubmit() {
    if (_contractStartDate.isEmpty || _contractEndDate.isEmpty) {
      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请选择合同开工和完工日期')),
      );
      return;
    }

    // 调用快速签约API
    _commitQuickSignRequest();
  }

  //快速签约
  Future<void> _commitQuickSignRequest() async {
    try {
      // 显示加载状态
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      final apiManager = ApiManager();

      // 找到选中公司的ID
      String selectedCompanyId = '';
      for (var company in companyOptions) {
        if (company['companyName'] == _selectedCompany) {
          selectedCompanyId = company['id']?.toString() ?? '';
          break;
        }
      }

      final response = await apiManager.post(
        '/api/signature/quick/sign/contract/commit',
        data: {
          'budgetId': widget.budgetId,
          'contractType': widget.contractType,
          'incomeCompany': selectedCompanyId,
          'contractConstructionStartDate': _contractStartDate,
          'contractConstructionEndDate': _contractEndDate,
          'contractPaymentList': _paymentItems
              .map((item) => {
                    'proportion': item.percentage / 100.0,
                    'type': (_paymentItems.indexOf(item) + 1).toString(),
                  })
              .toList(),
        },
      );
      if (response != null && mounted) {
        // 关闭加载对话框
        Navigator.pop(context);

        // 等待签约页面返回
        await Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => SignatureBudgetPageWidget(
                      budgetId: widget.budgetId,
                      contractType: widget.contractType,
                      packageType: widget.packageType,
                      packageId: widget.packageId,
                      serviceId: widget.serviceId,
                      esignContractId: widget.esignContractId,
                      onSigningCompleted: () {
                        // 签约完成后关闭当前弹窗并执行回调
                        if (mounted) {
                          Navigator.pop(context); // 关闭PaymentRatioDialog
                          if (widget.onSigningCompleted != null) {
                            widget.onSigningCompleted!();
                          }
                        }
                      },
                    )));
      } else {
        // 关闭加载对话框
        if (mounted) {
          Navigator.pop(context);
        }
      }
    } catch (e) {
      debugPrint('Failed to commit quick sign request: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('签约失败，请稍后再试')),
        );
      }
    }
  }
}

// 付款项目数据模型
class PaymentItem {
  final String title;
  final String subtitle;
  double amount;
  int percentage;

  PaymentItem({
    required this.title,
    required this.subtitle,
    required this.amount,
    required this.percentage,
  });
}
