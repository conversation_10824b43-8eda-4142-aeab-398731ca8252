import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';
import 'package:oktoast/oktoast.dart';

class ChannelMeetingDialog extends StatefulWidget {
  final String serviceId;
  final String customerName;
  final String decorationAddress;
  final Function({required DateTime selectedDate})? onConfirm;

  const ChannelMeetingDialog({
    super.key,
    required this.serviceId,
    required this.customerName,
    required this.decorationAddress,
    this.onConfirm,
  });

  static Future<void> show(
    BuildContext context, {
    required String serviceId,
    required String customerName,
    required String decorationAddress,
    Function({required DateTime selectedDate})? onConfirm,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: false,
      enableDrag: false,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: ChannelMeetingDialog(
          serviceId: serviceId,
          customerName: customerName,
          decorationAddress: decorationAddress,
          onConfirm: onConfirm,
        ),
      ),
    );
  }

  @override
  State<ChannelMeetingDialog> createState() => _ChannelMeetingDialogState();
}

class _ChannelMeetingDialogState extends State<ChannelMeetingDialog> {
  DateTime selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();

    // 调试信息：检查传入的参数
    print('ChannelMeetingDialog - 传入的客户姓名: ${widget.customerName}');
    print('ChannelMeetingDialog - 传入的装修地址: ${widget.decorationAddress}');
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 36.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 8.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            _buildHeader(),
            _buildContent(),
            _buildBottomButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: HexColor('#F5F5F5'),
            width: 1,
          ),
        ),
      ),
      child: Stack(
        children: [
          // 标题居中
          Center(
            child: Text(
              '渠道见面',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
          // 关闭按钮右对齐
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            child: Center(
              child: GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Icon(
                  Icons.close,
                  size: 24.w,
                  color: Colors.grey[600],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoRow('客户姓名：', widget.customerName),
          SizedBox(height: 16.h),
          _buildAddressRow('装修地址：', widget.decorationAddress),
          SizedBox(height: 16.h),
          _buildDatePicker(),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Container(
      height: 44.h,
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      decoration: BoxDecoration(
        color: HexColor('#F4F5F7'),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: HexColor('#333333'),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              textAlign: TextAlign.right,
              style: TextStyle(
                fontSize: 14.sp,
                color: HexColor('#666666'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressRow(String label, String address) {
    return Container(
      height: 44.h,
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      decoration: BoxDecoration(
        color: HexColor('#F4F5F7'),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: HexColor('#333333'),
              ),
            ),
          ),
          Expanded(
            child: Text(
              address,
              textAlign: TextAlign.right,
              style: TextStyle(
                fontSize: 14.sp,
                color: HexColor('#666666'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDatePicker() {
    return GestureDetector(
      onTap: () => _showDatePicker('选择对接日期', (selectedDateString) {
        setState(() {
          List<String> dateParts = selectedDateString.split('-');
          if (dateParts.length == 3) {
            int year = int.parse(dateParts[0]);
            int month = int.parse(dateParts[1]);
            int day = int.parse(dateParts[2]);
            selectedDate = DateTime(year, month, day);
          }
        });
      }),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4.r),
          border: Border.all(color: Colors.black),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(
              Icons.calendar_month,
              size: 20.w,
              color: Colors.black,
            ),
            SizedBox(width: 8.w),
            Text(
              '${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: EdgeInsets.all(20.w),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Container(
                height: 44.h,
                decoration: BoxDecoration(
                  color: HexColor('#F5F5F5'),
                  borderRadius: BorderRadius.circular(22.r),
                ),
                child: Center(
                  child: Text(
                    '取消',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: HexColor('#666666'),
                    ),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: GestureDetector(
              onTap: _submitChannelMeeting,
              child: Container(
                height: 44.h,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(22.r),
                ),
                child: Center(
                  child: Text(
                    '确认',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _onConfirm() {
    if (widget.onConfirm != null) {
      widget.onConfirm!(selectedDate: selectedDate);
    }
    Navigator.pop(context);
  }

  // 显示日期选择器
  void _showDatePicker(String title, Function(String) onDateSelected) {
    // 解析当前日期，如果没有则使用今天
    DateTime currentSelectedDate = selectedDate;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 300.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
        ),
        child: Column(
          children: [
            // 拖拽指示器
            Container(
              width: 36.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 8.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),

            // 标题栏
            Container(
              height: 50.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey[200]!,
                    width: 1.h,
                  ),
                ),
              ),
              child: Row(
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      '取消',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16.sp,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      String formattedDate =
                          '${currentSelectedDate.year}-${currentSelectedDate.month.toString().padLeft(2, '0')}-${currentSelectedDate.day.toString().padLeft(2, '0')}';
                      onDateSelected(formattedDate);
                    },
                    child: Text(
                      '确定',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 日期选择器
            Expanded(
              child: CupertinoDatePicker(
                mode: CupertinoDatePickerMode.date,
                initialDateTime: currentSelectedDate,
                minimumDate: DateTime(2020),
                maximumDate: DateTime(2030),
                onDateTimeChanged: (DateTime newDate) {
                  currentSelectedDate = newDate;
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  //提交渠道见面
  void _submitChannelMeeting() async {
    final apiManager = ApiManager();
    // 格式化日期为 YYYY-MM-DD 格式
    String formattedDate =
        '${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}';
    final response = await apiManager.post(
      '/api/signature/channel/commit',
      data: {
        'serviceId': widget.serviceId,
        'channelMeetingTime': formattedDate,
      },
    );
    if (response != null && mounted) {
      //回调并退出
      widget.onConfirm!(selectedDate: selectedDate);
      Navigator.pop(context);
      showToast('渠道见面提交成功', position: ToastPosition.center);
    } else {
      showToast('渠道见面提交失败', position: ToastPosition.center);
    }
  }
}
