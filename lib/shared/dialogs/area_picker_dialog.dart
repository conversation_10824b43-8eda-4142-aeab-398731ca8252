import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/models/area_model.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';

class AreaPickerDialog extends StatelessWidget {
  final List<Area> areaList;
  final String? selectedAreaName;
  final Function(Area)? onAreaSelected;

  const AreaPickerDialog({
    Key? key,
    required this.areaList,
    this.selectedAreaName,
    this.onAreaSelected,
  }) : super(key: key);

  static Future<Area?> show(
    BuildContext context, {
    required List<Area> areaList,
    String? selectedAreaName,
  }) {
    return showModalBottomSheet<Area>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AreaPickerDialog(
        areaList: areaList,
        selectedAreaName: selectedAreaName,
        onAreaSelected: (area) => Navigator.pop(context, area),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        children: [
          _buildHeader(context),
          Expanded(
            child: _buildAreaList(),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: HexColor('#F5F5F5'),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Text(
              '取消',
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey[600],
              ),
            ),
          ),
          Text(
            '选择区域',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          SizedBox(width: 32.w), // 占位，保持标题居中
        ],
      ),
    );
  }

  Widget _buildAreaList() {
    return ListView.separated(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      itemCount: areaList.length,
      separatorBuilder: (context, index) => Divider(
        height: 1.h,
        color: HexColor('#F5F5F5'),
      ),
      itemBuilder: (context, index) {
        final area = areaList[index];
        final isSelected = selectedAreaName == area.originalName;

        return GestureDetector(
          onTap: () => onAreaSelected?.call(area),
          child: SizedBox(
            height: 50.h,
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    area.originalName,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: isSelected ? Colors.blue : Colors.black,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check,
                    color: Colors.blue,
                    size: 20.w,
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
