import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';

class DispatchDetailDialog extends StatefulWidget {
  final String serviceId;
  final String sourceId;
  final String code; //岗位code
  final String dispatchType;
  final String dispatchTypeName;
  final String customerName;
  final String decorationAddress;
  final Function(String)? onConfirm;

  const DispatchDetailDialog({
    super.key,
    required this.serviceId,
    required this.sourceId,
    required this.code,
    required this.dispatchType,
    required this.dispatchTypeName,
    required this.customerName,
    required this.decorationAddress,
    this.onConfirm,
  });

  static Future<String?> show(
    BuildContext context, {
    required String serviceId,
    required String sourceId,
    required String code,
    required String dispatchType,
    required String dispatchTypeName,
    required String customerName,
    required String decorationAddress,
    Function(String)? onConfirm,
  }) {
    return showModalBottomSheet<String>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: true, // 允许点击空白区域关闭
      enableDrag: true,
      builder: (context) {
        print('DispatchDetailDialog builder 被调用');
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: DispatchDetailDialog(
            serviceId: serviceId,
            sourceId: sourceId,
            code: code,
            dispatchType: dispatchType,
            dispatchTypeName: dispatchTypeName,
            customerName: customerName,
            decorationAddress: decorationAddress,
            onConfirm: onConfirm,
          ),
        );
      },
    );
  }

  @override
  State<DispatchDetailDialog> createState() => _DispatchDetailDialogState();
}

class _DispatchDetailDialogState extends State<DispatchDetailDialog> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _shopList = [];
  List<Map<String, dynamic>> _filteredShopList = [];
  List<Map<String, dynamic>> _channelUserList = []; // 渠道客服列表
  List<Map<String, dynamic>> _filteredChannelUserList = []; // 过滤后的渠道客服列表
  List<Map<String, dynamic>> _customerManagerList = []; // 客服经理列表
  List<Map<String, dynamic>> _filteredCustomerManagerList = []; // 过滤后的客服经理列表
  List<Map<String, dynamic>> _designerList = []; // 设计师列表
  List<Map<String, dynamic>> _filteredDesignerList = []; // 过滤后的设计师列表
  bool _isLoadingShops = false;
  bool _isLoadingChannelUsers = false; // 渠道客服加载状态
  bool _isLoadingCustomerManagers = false; // 客服经理加载状态
  bool _isLoadingDesigners = false; // 设计师加载状态
  bool _isShopSelected = false; // 标记是否已选择门店
  bool _isChannelUserSelected = false; // 标记是否已选择渠道客服
  bool _isCustomerManagerSelected = false; // 标记是否已选择客服经理
  bool _isDesignerSelected = false; // 标记是否已选择设计师
  String _selectedShopValue = ''; // 保存选中门店的value值
  String _selectedChannelUserValue = ''; // 保存选中渠道客服的value值
  String _selectedCustomerManagerValue = ''; // 保存选中客服经理的value值
  String _selectedDesignerValue = ''; // 保存选中设计师的value值

  @override
  void initState() {
    super.initState();
    // 根据 dispatchType 调用不同的搜索接口
    if (widget.dispatchType == "1") {
      _getChannelUserList();
    } else if (widget.dispatchType == "2") {
      _getShopList();
    } else if (widget.dispatchType == "3") {
      _getCustomerManagerList();
    } else if (widget.dispatchType == "4") {
      _getDesignerList();
    }
    _searchController.addListener(_onSearchTextChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchTextChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchTextChanged() {
    final searchText = _searchController.text.toLowerCase();
    setState(() {
      if (searchText.isEmpty) {
        _filteredShopList = [];
        _filteredChannelUserList = [];
        _filteredCustomerManagerList = [];
        _filteredDesignerList = [];
        _isShopSelected = false;
        _isChannelUserSelected = false;
        _isCustomerManagerSelected = false;
        _isDesignerSelected = false;
        _selectedShopValue = ''; // 清空选中的门店值
        _selectedChannelUserValue = ''; // 清空选中的渠道客服值
        _selectedCustomerManagerValue = ''; // 清空选中的客服经理值
        _selectedDesignerValue = ''; // 清空选中的设计师值
      } else if (widget.dispatchType == "1" && !_isChannelUserSelected) {
        // 渠道客服搜索
        _filteredChannelUserList = _channelUserList.where((user) {
          final realName = user['realName']?.toString().toLowerCase() ?? '';
          return realName.contains(searchText);
        }).toList();
      } else if (widget.dispatchType == "2" && !_isShopSelected) {
        // 门店搜索
        _filteredShopList = _shopList.where((shop) {
          final label = shop['label']?.toString().toLowerCase() ?? '';
          return label.contains(searchText);
        }).toList();
      } else if (widget.dispatchType == "3" && !_isCustomerManagerSelected) {
        // 客服经理搜索
        _filteredCustomerManagerList = _customerManagerList.where((manager) {
          final realName = manager['realName']?.toString().toLowerCase() ?? '';
          return realName.contains(searchText);
        }).toList();
      } else if (widget.dispatchType == "4" && !_isDesignerSelected) {
        // 设计师搜索
        _filteredDesignerList = _designerList.where((designer) {
          final realName = designer['realName']?.toString().toLowerCase() ?? '';
          return realName.contains(searchText);
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击整个对话框背景收起键盘
        FocusScope.of(context).unfocus();
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16.r),
            topRight: Radius.circular(16.r),
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 36.w,
                height: 4.h,
                margin: EdgeInsets.only(top: 8.h),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2.r),
                ),
              ),
              _buildHeader(),
              _buildContent(),
              _buildBottomButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: HexColor('#F5F5F5'),
            width: 1,
          ),
        ),
      ),
      child: Stack(
        children: [
          // 标题居中
          Center(
            child: Text(
              '派单${widget.dispatchTypeName}',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
          // 关闭按钮右对齐
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            child: Center(
              child: GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Icon(
                  Icons.close,
                  size: 24.w,
                  color: Colors.grey[600],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    // 获取键盘高度
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    // 计算可用高度：屏幕高度 - 键盘高度 - 状态栏高度 - 一些安全边距
    final availableHeight = MediaQuery.of(context).size.height -
        keyboardHeight -
        MediaQuery.of(context).padding.top -
        100.h; // 预留100h作为安全边距

    // 设置最大高度，但不超过屏幕的70%
    final maxHeight = math.min(
      availableHeight * 0.8, // 可用高度的80%
      MediaQuery.of(context).size.height * 0.7, // 或屏幕高度的70%
    );

    return Container(
      constraints: BoxConstraints(
        maxHeight: maxHeight,
      ),
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(20.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildInfoRow('客户姓名：', widget.customerName),
              SizedBox(height: 16.h),
              _buildAddressRow('装修地址：', widget.decorationAddress),
              // 当 dispatchType 为 "1"、"2"、"3" 或 "4" 时显示搜索功能
              if (widget.dispatchType == "1" ||
                  widget.dispatchType == "2" ||
                  widget.dispatchType == "3" ||
                  widget.dispatchType == "4") ...[
                SizedBox(height: 24.h),
                _buildSearchField(),
                if (_searchController.text.isNotEmpty) ...[
                  SizedBox(height: 16.h),
                  if (widget.dispatchType == "1" && !_isChannelUserSelected)
                    _buildChannelUserList()
                  else if (widget.dispatchType == "2" && !_isShopSelected)
                    _buildShopList()
                  else if (widget.dispatchType == "3" &&
                      !_isCustomerManagerSelected)
                    _buildCustomerManagerList()
                  else if (widget.dispatchType == "4" && !_isDesignerSelected)
                    _buildDesignerList(),
                ],
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Container(
      height: 44.h,
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      decoration: BoxDecoration(
        color: HexColor('#F4F5F7'),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: HexColor('#333333'),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              textAlign: TextAlign.right,
              style: TextStyle(
                fontSize: 14.sp,
                color: HexColor('#666666'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressRow(String label, String address) {
    return Container(
      height: 44.h,
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      decoration: BoxDecoration(
        color: HexColor('#F4F5F7'),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: HexColor('#333333'),
              ),
            ),
          ),
          Expanded(
            child: Text(
              address,
              textAlign: TextAlign.right,
              style: TextStyle(
                fontSize: 14.sp,
                color: HexColor('#666666'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchField() {
    return Container(
      decoration: BoxDecoration(
        color: HexColor('#F8F8F8'),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: TextField(
        controller: _searchController,
        onChanged: (value) {
          // 用户手动输入时重置选择状态
          if (_isShopSelected ||
              _isChannelUserSelected ||
              _isCustomerManagerSelected ||
              _isDesignerSelected) {
            setState(() {
              _isShopSelected = false;
              _isChannelUserSelected = false;
              _isCustomerManagerSelected = false;
              _isDesignerSelected = false;
              _selectedShopValue = ''; // 清空选中的门店值
              _selectedChannelUserValue = ''; // 清空选中的渠道客服值
              _selectedCustomerManagerValue = ''; // 清空选中的客服经理值
              _selectedDesignerValue = ''; // 清空选中的设计师值
            });
          }
        },
        decoration: InputDecoration(
          hintText: widget.dispatchType == "1"
              ? '搜索客服姓名'
              : widget.dispatchType == "2"
                  ? '搜索门店名称'
                  : widget.dispatchType == "3"
                      ? '搜索客服经理姓名'
                      : '搜索设计师姓名',
          hintStyle: TextStyle(
            fontSize: 14.sp,
            color: HexColor('#999999'),
          ),
          suffixIcon: _searchController.text.isNotEmpty
              ? GestureDetector(
                  onTap: () {
                    setState(() {
                      _searchController.clear();
                    });
                  },
                  child: Icon(
                    Icons.clear,
                    size: 20.w,
                    color: HexColor('#999999'),
                  ),
                )
              : Icon(
                  Icons.search,
                  size: 20.w,
                  color: HexColor('#999999'),
                ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 12.w,
            vertical: 12.h,
          ),
        ),
        style: TextStyle(
          fontSize: 14.sp,
          color: HexColor('#333333'),
        ),
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: EdgeInsets.all(20.w),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Container(
                height: 44.h,
                decoration: BoxDecoration(
                  color: HexColor('#F5F5F5'),
                  borderRadius: BorderRadius.circular(22.r),
                ),
                child: Center(
                  child: Text(
                    '取消',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: HexColor('#666666'),
                    ),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: GestureDetector(
              onTap: _onConfirm,
              child: Container(
                height: 44.h,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(22.r),
                ),
                child: Center(
                  child: Text(
                    '确认',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShopList() {
    // 如果正在加载或者没有搜索结果，显示提示
    if (_isLoadingShops) {
      return Container(
        height: 60.h,
        alignment: Alignment.center,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 16.w,
              height: 16.w,
              child: CircularProgressIndicator(
                color: HexColor('#FFB26D'),
                strokeWidth: 2,
              ),
            ),
            SizedBox(width: 8.w),
            Text(
              '加载中...',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14.sp,
              ),
            ),
          ],
        ),
      );
    }

    if (_filteredShopList.isEmpty) {
      return Container(
        height: 60.h,
        alignment: Alignment.center,
        child: Text(
          '暂无匹配的门店',
          style: TextStyle(
            color: Colors.grey[500],
            fontSize: 14.sp,
          ),
        ),
      );
    }

    // 动态计算搜索列表的最大高度
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final maxListHeight = keyboardHeight > 0
        ? 150.h // 键盘弹出时减小高度
        : 200.h; // 键盘收起时正常高度

    return Container(
      constraints: BoxConstraints(
        maxHeight: maxListHeight,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        itemCount: _filteredShopList.length,
        itemBuilder: (context, index) {
          // 添加边界检查，防止访问超出范围的索引
          if (index >= _filteredShopList.length) {
            return const SizedBox.shrink();
          }

          final shop = _filteredShopList[index];
          final isDisabled = shop['disabled'] == true;

          return InkWell(
            onTap: isDisabled
                ? null
                : () {
                    _onShopSelected(shop);
                  },
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 12.h,
              ),
              decoration: BoxDecoration(
                border: index < _filteredShopList.length - 1
                    ? Border(
                        bottom: BorderSide(
                          color: Colors.grey[100]!,
                          width: 1,
                        ),
                      )
                    : null,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      shop['label']?.toString() ?? '',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: isDisabled ? Colors.grey[400] : Colors.black87,
                      ),
                    ),
                  ),
                  if (isDisabled)
                    Text(
                      '不可选',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[400],
                      ),
                    ),
                  // 移除了右箭头图标
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildChannelUserList() {
    // 如果正在加载或者没有搜索结果，显示提示
    if (_isLoadingChannelUsers) {
      return Container(
        height: 60.h,
        alignment: Alignment.center,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 16.w,
              height: 16.w,
              child: CircularProgressIndicator(
                color: HexColor('#FFB26D'),
                strokeWidth: 2,
              ),
            ),
            SizedBox(width: 8.w),
            Text(
              '加载中...',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14.sp,
              ),
            ),
          ],
        ),
      );
    }

    if (_filteredChannelUserList.isEmpty) {
      return Container(
        height: 60.h,
        alignment: Alignment.center,
        child: Text(
          '暂无匹配的客服',
          style: TextStyle(
            color: Colors.grey[500],
            fontSize: 14.sp,
          ),
        ),
      );
    }

    // 动态计算搜索列表的最大高度
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final maxListHeight = keyboardHeight > 0
        ? 150.h // 键盘弹出时减小高度
        : 200.h; // 键盘收起时正常高度

    return Container(
      constraints: BoxConstraints(
        maxHeight: maxListHeight,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        itemCount: _filteredChannelUserList.length,
        itemBuilder: (context, index) {
          // 添加边界检查，防止访问超出范围的索引
          if (index >= _filteredChannelUserList.length) {
            return const SizedBox.shrink();
          }

          final user = _filteredChannelUserList[index];

          return InkWell(
            onTap: () {
              _onChannelUserSelected(user);
            },
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 12.h,
              ),
              decoration: BoxDecoration(
                border: index < _filteredChannelUserList.length - 1
                    ? Border(
                        bottom: BorderSide(
                          color: Colors.grey[100]!,
                          width: 1,
                        ),
                      )
                    : null,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      user['realName']?.toString() ?? '',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCustomerManagerList() {
    // 如果正在加载或者没有搜索结果，显示提示
    if (_isLoadingCustomerManagers) {
      return Container(
        height: 60.h,
        alignment: Alignment.center,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 16.w,
              height: 16.w,
              child: CircularProgressIndicator(
                color: HexColor('#FFB26D'),
                strokeWidth: 2,
              ),
            ),
            SizedBox(width: 8.w),
            Text(
              '加载中...',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14.sp,
              ),
            ),
          ],
        ),
      );
    }

    if (_filteredCustomerManagerList.isEmpty) {
      return Container(
        height: 60.h,
        alignment: Alignment.center,
        child: Text(
          '暂无匹配的客服经理',
          style: TextStyle(
            color: Colors.grey[500],
            fontSize: 14.sp,
          ),
        ),
      );
    }

    // 动态计算搜索列表的最大高度
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final maxListHeight = keyboardHeight > 0
        ? 150.h // 键盘弹出时减小高度
        : 200.h; // 键盘收起时正常高度

    return Container(
      constraints: BoxConstraints(
        maxHeight: maxListHeight,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        itemCount: _filteredCustomerManagerList.length,
        itemBuilder: (context, index) {
          // 添加边界检查，防止访问超出范围的索引
          if (index >= _filteredCustomerManagerList.length) {
            return const SizedBox.shrink();
          }

          final manager = _filteredCustomerManagerList[index];

          return InkWell(
            onTap: () {
              _onCustomerManagerSelected(manager);
            },
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 12.h,
              ),
              decoration: BoxDecoration(
                border: index < _filteredCustomerManagerList.length - 1
                    ? Border(
                        bottom: BorderSide(
                          color: Colors.grey[100]!,
                          width: 1,
                        ),
                      )
                    : null,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      manager['realName']?.toString() ?? '',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDesignerList() {
    // 如果正在加载或者没有搜索结果，显示提示
    if (_isLoadingDesigners) {
      return Container(
        height: 60.h,
        alignment: Alignment.center,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 16.w,
              height: 16.w,
              child: CircularProgressIndicator(
                color: HexColor('#FFB26D'),
                strokeWidth: 2,
              ),
            ),
            SizedBox(width: 8.w),
            Text(
              '加载中...',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14.sp,
              ),
            ),
          ],
        ),
      );
    }

    if (_filteredDesignerList.isEmpty) {
      return Container(
        height: 60.h,
        alignment: Alignment.center,
        child: Text(
          '暂无匹配的设计师',
          style: TextStyle(
            color: Colors.grey[500],
            fontSize: 14.sp,
          ),
        ),
      );
    }

    // 动态计算搜索列表的最大高度
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    final maxListHeight = keyboardHeight > 0
        ? 150.h // 键盘弹出时减小高度
        : 200.h; // 键盘收起时正常高度

    return Container(
      constraints: BoxConstraints(
        maxHeight: maxListHeight,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        itemCount: _filteredDesignerList.length,
        itemBuilder: (context, index) {
          // 添加边界检查，防止访问超出范围的索引
          if (index >= _filteredDesignerList.length) {
            return const SizedBox.shrink();
          }

          final designer = _filteredDesignerList[index];

          return InkWell(
            onTap: () {
              _onDesignerSelected(designer);
            },
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 16.w,
                vertical: 12.h,
              ),
              decoration: BoxDecoration(
                border: index < _filteredDesignerList.length - 1
                    ? Border(
                        bottom: BorderSide(
                          color: Colors.grey[100]!,
                          width: 1,
                        ),
                      )
                    : null,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      designer['realName']?.toString() ?? '',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _onShopSelected(Map<String, dynamic> shop) {
    // 选择门店后的处理逻辑
    final shopLabel = shop['label']?.toString() ?? '';
    final shopValue = shop['value']?.toString() ?? '';

    // 先移除监听器，避免在设置文本时触发搜索
    _searchController.removeListener(_onSearchTextChanged);

    // 将选中的门店名称填充到搜索框，并清空联想列表
    setState(() {
      _searchController.text = shopLabel;
      _filteredShopList = []; // 清空联想列表，隐藏下拉框
      _isShopSelected = true; // 标记已选择门店
      _selectedShopValue = shopValue; // 保存门店的value值
    });

    // 重新添加监听器
    _searchController.addListener(_onSearchTextChanged);

    // 收起键盘
    FocusScope.of(context).unfocus();

    // 注意：这里不调用 onConfirm 和 Navigator.pop，只是填充文本
    // 用户需要点击"确认"按钮才会真正提交并关闭对话框
  }

  void _onChannelUserSelected(Map<String, dynamic> user) {
    // 选择渠道客服后的处理逻辑
    final userName = user['realName']?.toString() ?? '';
    final userId = user['userId']?.toString() ?? '';

    // 先移除监听器，避免在设置文本时触发搜索
    _searchController.removeListener(_onSearchTextChanged);

    // 将选中的客服名称填充到搜索框，并清空联想列表
    setState(() {
      _searchController.text = userName;
      _filteredChannelUserList = []; // 清空联想列表，隐藏下拉框
      _isChannelUserSelected = true; // 标记已选择渠道客服
      _selectedChannelUserValue = userId; // 保存客服的userId值
    });

    // 重新添加监听器
    _searchController.addListener(_onSearchTextChanged);

    // 收起键盘
    FocusScope.of(context).unfocus();

    // 注意：这里不调用 onConfirm 和 Navigator.pop，只是填充文本
    // 用户需要点击"确认"按钮才会真正提交并关闭对话框
  }

  void _onCustomerManagerSelected(Map<String, dynamic> manager) {
    // 选择客服经理后的处理逻辑
    final managerName = manager['realName']?.toString() ?? '';
    final managerId = manager['userId']?.toString() ?? '';

    // 先移除监听器，避免在设置文本时触发搜索
    _searchController.removeListener(_onSearchTextChanged);

    // 将选中的客服经理名称填充到搜索框，并清空联想列表
    setState(() {
      _searchController.text = managerName;
      _filteredCustomerManagerList = []; // 清空联想列表，隐藏下拉框
      _isCustomerManagerSelected = true; // 标记已选择客服经理
      _selectedCustomerManagerValue = managerId; // 保存客服经理的userId值
    });

    // 重新添加监听器
    _searchController.addListener(_onSearchTextChanged);

    // 收起键盘
    FocusScope.of(context).unfocus();

    // 注意：这里不调用 onConfirm 和 Navigator.pop，只是填充文本
    // 用户需要点击"确认"按钮才会真正提交并关闭对话框
  }

  void _onDesignerSelected(Map<String, dynamic> designer) {
    // 选择设计师后的处理逻辑
    final designerName = designer['realName']?.toString() ?? '';
    final designerId = designer['userId']?.toString() ?? '';

    // 先移除监听器，避免在设置文本时触发搜索
    _searchController.removeListener(_onSearchTextChanged);

    // 将选中的设计师名称填充到搜索框，并清空联想列表
    setState(() {
      _searchController.text = designerName;
      _filteredDesignerList = []; // 清空联想列表，隐藏下拉框
      _isDesignerSelected = true; // 标记已选择设计师
      _selectedDesignerValue = designerId; // 保存设计师的userId值
    });

    // 重新添加监听器
    _searchController.addListener(_onSearchTextChanged);

    // 收起键盘
    FocusScope.of(context).unfocus();

    // 注意：这里不调用 onConfirm 和 Navigator.pop，只是填充文本
    // 用户需要点击"确认"按钮才会真正提交并关闭对话框
  }

  void _onConfirm() async {
    // 对于渠道客服类型，需要确保已选择客服
    if (widget.dispatchType == '1') {
      if (_selectedChannelUserValue.isEmpty) {
        // 可以在这里添加提示用户选择客服的逻辑
        return;
      }
    }
    // 对于门店类型，需要确保已选择门店
    else if (widget.dispatchType == '2') {
      if (_selectedShopValue.isEmpty) {
        // 可以在这里添加提示用户选择门店的逻辑
        return;
      }
    }
    // 对于客服经理类型，需要确保已选择客服经理
    else if (widget.dispatchType == '3') {
      if (_selectedCustomerManagerValue.isEmpty) {
        // 可以在这里添加提示用户选择客服经理的逻辑
        return;
      }
    }
    // 对于设计师类型，需要确保已选择设计师
    else if (widget.dispatchType == '4') {
      if (_selectedDesignerValue.isEmpty) {
        // 可以在这里添加提示用户选择设计师的逻辑
        return;
      }
    }

    // 调用派单提交
    await _submitDispatch();
  }

  //门店-模糊搜索列表
  Future<void> _getShopList() async {
    if (!mounted) return;

    setState(() {
      _isLoadingShops = true;
    });

    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
          '/api/signature/dispatch/store/list',
          queryParameters: {'isSearch': '0'});

      if (response != null && mounted) {
        setState(() {
          _shopList = List<Map<String, dynamic>>.from(response);
          _filteredShopList = []; // 初始化为空列表，默认不显示
          _isLoadingShops = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingShops = false;
        });
      }
      print('获取门店列表失败: $e');
    }
  }

  //渠道客服-模糊搜索列表
  Future<void> _getChannelUserList() async {
    if (!mounted) return;

    setState(() {
      _isLoadingChannelUsers = true;
    });

    try {
      final apiManager = ApiManager();
      final response = await apiManager.get('/api/signature/channel/user/list',
          queryParameters: {'sourceId': widget.sourceId});

      if (response != null && mounted) {
        setState(() {
          // 根据您提供的数据结构，response已经是data层的数组数据
          // 需要将userId和realName转换为统一的格式
          _channelUserList =
              List<Map<String, dynamic>>.from(response.map((user) => {
                    'userId': user['userId']?.toString() ?? '',
                    'realName': user['realName']?.toString() ?? '',
                  }));
          _filteredChannelUserList = []; // 初始化为空列表，默认不显示
          _isLoadingChannelUsers = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingChannelUsers = false;
        });
      }
      print('获取渠道客服列表失败: $e');
    }
  }

  //客服经理-模糊搜索列表
  Future<void> _getCustomerManagerList() async {
    if (!mounted) return;
    setState(() {
      _isLoadingCustomerManagers = true;
    });
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
          '/api/signature/customer/manager/list',
          queryParameters: {'serviceId': widget.serviceId});
      if (response != null && mounted) {
        setState(() {
          // 根据您提供的数据结构，response已经是data层的数组数据
          // 需要将userId和realName转换为统一的格式
          _customerManagerList =
              List<Map<String, dynamic>>.from(response.map((manager) => {
                    'userId': manager['userId']?.toString() ?? '',
                    'realName': manager['realName']?.toString() ?? '',
                  }));
          _filteredCustomerManagerList = []; // 初始化为空列表，默认不显示
          _isLoadingCustomerManagers = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingCustomerManagers = false;
        });
      }
      print('获取客服经理列表失败: $e');
    }
  }

  //设计师-模糊搜索
  Future<void> _getDesignerList() async {
    if (!mounted) return;
    setState(() {
      _isLoadingDesigners = true;
    });
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get('/api/signature/designer/list',
          queryParameters: {'serviceId': widget.serviceId});
      if (response != null && mounted) {
        setState(() {
          // 根据您提供的数据结构，response已经是data层的数组数据
          _designerList =
              List<Map<String, dynamic>>.from(response.map((designer) => {
                    'userId': designer['userId']?.toString() ?? '',
                    'realName': designer['realName']?.toString() ?? '',
                  }));
          _filteredDesignerList = []; // 初始化为空列表，默认不显示
          _isLoadingDesigners = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingDesigners = false;
        });
      }
      print('获取设计师列表失败: $e');
    }
  }

  //派单提交
  Future<void> _submitDispatch() async {
    try {
      final apiManager = ApiManager();

      // 根据派单类型确定bizId的值
      String bizId = '';
      if (widget.dispatchType == '1') {
        bizId = _selectedChannelUserValue; // 渠道客服使用userId
      } else if (widget.dispatchType == '2') {
        bizId = _selectedShopValue; // 门店使用门店的value值
      } else if (widget.dispatchType == '3') {
        bizId = _selectedCustomerManagerValue; // 客服经理使用userId
      } else if (widget.dispatchType == '4') {
        bizId = _selectedDesignerValue; // 设计师使用userId
      }

      final response = await apiManager.post(
        '/api/signature/dispatch/commit',
        data: {
          'serviceId': widget.serviceId,
          'dispatchType': widget.dispatchType,
          'bizId': bizId,
        },
      );
      if (response != null && mounted) {
        // 成功后先调用回调，再关闭对话框
        if (widget.onConfirm != null) {
          widget.onConfirm!(_searchController.text.trim());
        }
        Navigator.pop(context, 'success');
      }
    } catch (e) {
      print('派单提交失败: $e');
    }
  }
}
