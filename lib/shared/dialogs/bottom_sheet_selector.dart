import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';

class BottomSheetSelector extends StatefulWidget {
  final List<String> options;
  final int? initialSelectedIndex;
  final Function(int) onSelected;
  final String? title;

  const BottomSheetSelector({
    Key? key,
    required this.options,
    required this.onSelected,
    this.initialSelectedIndex,
    this.title,
  }) : super(key: key);

  static Future<void> show({
    required BuildContext context,
    required List<String> options,
    required Function(int) onSelected,
    int? initialSelectedIndex,
    String? title,
  }) {
    return showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      // 设置为 true 以确保底部 sheet 延伸到安全区域下方
      isScrollControlled: true,
      builder: (context) => BottomSheetSelector(
        options: options,
        onSelected: onSelected,
        initialSelectedIndex: initialSelectedIndex,
        title: title,
      ),
    );
  }

  @override
  State<BottomSheetSelector> createState() => _BottomSheetSelectorState();
}

class _BottomSheetSelectorState extends State<BottomSheetSelector> {
  @override
  Widget build(BuildContext context) {
    // 获取底部安全区域的高度
    final bottomPadding = MediaQuery.of(context).padding.bottom;

    return Container(
      padding: const EdgeInsets.only(top: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Padding(
            padding: EdgeInsets.only(
              left: 16.w,
              right: 16.w,
              top: 8.h,
              bottom: 4.h, // 减小底部间距
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 左侧空白占位，保持标题居中
                SizedBox(width: 24.w),
                if (widget.title != null)
                  Text(
                    widget.title!,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                // 右上角关闭按钮
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    width: 24.w,
                    height: 24.w,
                    alignment: Alignment.center,
                    child: Icon(
                      Icons.close,
                      size: 20.w,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Options
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: widget.options.length,
            itemBuilder: (context, index) {
              final isSelected = widget.initialSelectedIndex == index;
              return InkWell(
                onTap: () {
                  // 直接选中并关闭弹窗，不需要确定按钮
                  widget.onSelected(index);
                  Navigator.pop(context);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                  color: Colors.white,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Center(
                          child: Text(
                            widget.options[index],
                            style: TextStyle(
                              color: isSelected
                                  ? HexColor('#FFB26D')
                                  : Colors.black,
                              fontSize: 16.sp,
                            ),
                          ),
                        ),
                      ),
                      // if (isSelected)
                      //   const Icon(
                      //     Icons.check,
                      //     color: Colors.blue,
                      //   ),
                    ],
                  ),
                ),
              );
            },
          ),
          // 底部安全区域填充
          SizedBox(height: bottomPadding)
        ],
      ),
    );
  }
}
