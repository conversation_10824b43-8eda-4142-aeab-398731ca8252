import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:oktoast/oktoast.dart';

/// 账户类型选择对话框
class AccountTypeDialog extends StatefulWidget {
  final String? initialValue; // 初始选中值：'private' 或 'public'
  final Function(String, {String? operatorName, String? operatorPhone})?
      onConfirm; // 确认回调

  const AccountTypeDialog({
    Key? key,
    this.initialValue,
    this.onConfirm,
  }) : super(key: key);

  @override
  State<AccountTypeDialog> createState() => _AccountTypeDialogState();
}

class _AccountTypeDialogState extends State<AccountTypeDialog> {
  String selectedType = 'private'; // 默认选择对私
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.initialValue != null) {
      selectedType = widget.initialValue!;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // 点击空白区域收起键盘
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Center(
        child: Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            width: double.infinity,
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
              minHeight: 200.h,
            ),
            margin: EdgeInsets.only(
              // 当键盘弹起时，调整底部边距避免遮挡
              bottom: MediaQuery.of(context).viewInsets.bottom * 0.1,
            ),
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom * 0.1,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 标题区域
                  Container(
                    padding: EdgeInsets.only(
                      top: 20.h,
                      bottom: 12.h,
                      left: 24.w,
                      right: 12.w,
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: Text(
                            '请确认',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.w600,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            // 先收起键盘，再关闭弹窗
                            FocusScope.of(context).unfocus();
                            Navigator.of(context).pop();
                          },
                          child: Icon(
                            Icons.close,
                            size: 24.w,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // 账户标签
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    alignment: Alignment.centerLeft,
                    child: Text(
                      '账户',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                  ),

                  SizedBox(height: 12.h),

                  // 选项区域
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    child: Row(
                      children: [
                        // 对私选项
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                selectedType = 'private';
                              });
                            },
                            child: Row(
                              children: [
                                Container(
                                  width: 20.w,
                                  height: 20.w,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: selectedType == 'private'
                                          ? Colors.black
                                          : Colors.grey[400]!,
                                      width: 2.w,
                                    ),
                                    color: selectedType == 'private'
                                        ? Colors.black
                                        : Colors.transparent,
                                  ),
                                  child: selectedType == 'private'
                                      ? Icon(
                                          Icons.check,
                                          size: 14.w,
                                          color: Colors.white,
                                        )
                                      : null,
                                ),
                                SizedBox(width: 8.w),
                                Text(
                                  '对私',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        SizedBox(width: 24.w),

                        // 对公选项
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                selectedType = 'public';
                              });
                            },
                            child: Row(
                              children: [
                                Container(
                                  width: 20.w,
                                  height: 20.w,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: selectedType == 'public'
                                          ? Colors.black
                                          : Colors.grey[400]!,
                                      width: 2.w,
                                    ),
                                    color: selectedType == 'public'
                                        ? Colors.black
                                        : Colors.transparent,
                                  ),
                                  child: selectedType == 'public'
                                      ? Icon(
                                          Icons.check,
                                          size: 14.w,
                                          color: Colors.white,
                                        )
                                      : null,
                                ),
                                SizedBox(width: 8.w),
                                Text(
                                  '对公',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 12.h),

                  // 对公输入框区域（仅在选择对公时显示）
                  if (selectedType == 'public') ...[
                    SizedBox(height: 8.h),

                    // 经办人姓名和电话并排
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 24.w),
                      child: Row(
                        children: [
                          // 经办人姓名
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '姓名',
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black87,
                                  ),
                                ),
                                SizedBox(height: 4.h),
                                TextField(
                                  controller: _nameController,
                                  textInputAction: TextInputAction.next,
                                  decoration: InputDecoration(
                                    hintText: '请输入姓名',
                                    hintStyle: TextStyle(
                                      fontSize: 14.sp,
                                      color: Colors.grey[400],
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8.r),
                                      borderSide: BorderSide(
                                        color: Colors.grey[300]!,
                                        width: 1.w,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8.r),
                                      borderSide: BorderSide(
                                        color: Colors.grey[300]!,
                                        width: 1.w,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8.r),
                                      borderSide: BorderSide(
                                        color: Colors.black,
                                        width: 1.w,
                                      ),
                                    ),
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12.w,
                                      vertical: 12.h,
                                    ),
                                    fillColor: Colors.white,
                                    filled: true,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          SizedBox(width: 12.w),

                          // 经办人电话
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '电话',
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black87,
                                  ),
                                ),
                                SizedBox(height: 4.h),
                                TextField(
                                  controller: _phoneController,
                                  keyboardType: TextInputType.phone,
                                  textInputAction: TextInputAction.done,
                                  onSubmitted: (value) {
                                    // 当用户点击完成按钮时，收起键盘
                                    FocusScope.of(context).unfocus();
                                  },
                                  decoration: InputDecoration(
                                    hintText: '请输入电话',
                                    hintStyle: TextStyle(
                                      fontSize: 14.sp,
                                      color: Colors.grey[400],
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8.r),
                                      borderSide: BorderSide(
                                        color: Colors.grey[300]!,
                                        width: 1.w,
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8.r),
                                      borderSide: BorderSide(
                                        color: Colors.grey[300]!,
                                        width: 1.w,
                                      ),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8.r),
                                      borderSide: BorderSide(
                                        color: Colors.black,
                                        width: 1.w,
                                      ),
                                    ),
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: 12.w,
                                      vertical: 12.h,
                                    ),
                                    fillColor: Colors.white,
                                    filled: true,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(height: 12.h),
                  ],

                  // 提示文字
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    child: RichText(
                      text: TextSpan(
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[600],
                          height: 1.4,
                        ),
                        children: [
                          const TextSpan(text: '提示：确保登记的'),
                          TextSpan(
                            text: selectedType == 'private'
                                ? '客户姓名与客户的真实姓名一致'
                                : '客户姓名与对公公司名称一致',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                          const TextSpan(text: ',如若不一致可进入后台修改，修改成功后，请重新拟合同'),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: 16.h),

                  // 按钮区域
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    child: Row(
                      children: [
                        // 取消按钮
                        Expanded(
                          child: Container(
                            height: 44.h,
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: Colors.grey[300]!,
                                width: 1.w,
                              ),
                              borderRadius: BorderRadius.circular(22.r),
                            ),
                            child: Center(
                              child: TextButton(
                                onPressed: () => Navigator.of(context).pop(),
                                style: TextButton.styleFrom(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(22.r),
                                  ),
                                  padding: EdgeInsets.zero,
                                ),
                                child: Text(
                                  '取消',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: Colors.grey[700],
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                        SizedBox(width: 12.w),

                        // 确定按钮
                        Expanded(
                          child: Container(
                            height: 44.h,
                            decoration: BoxDecoration(
                              color: Colors.black,
                              borderRadius: BorderRadius.circular(22.r),
                            ),
                            child: Center(
                              child: TextButton(
                                onPressed: () {
                                  // 如果是对公模式，先验证必填字段
                                  if (selectedType == 'public') {
                                    String name = _nameController.text.trim();
                                    String phone = _phoneController.text.trim();

                                    if (name.isEmpty && phone.isEmpty) {
                                      showToast('请填写姓名和电话',
                                          position: ToastPosition.center);
                                      return;
                                    } else if (name.isEmpty) {
                                      showToast('请填写姓名',
                                          position: ToastPosition.center);
                                      return;
                                    } else if (phone.isEmpty) {
                                      showToast('请填写电话',
                                          position: ToastPosition.center);
                                      return;
                                    }
                                  }

                                  Navigator.of(context).pop();
                                  if (widget.onConfirm != null) {
                                    if (selectedType == 'public') {
                                      widget.onConfirm!(
                                        selectedType,
                                        operatorName: _nameController.text,
                                        operatorPhone: _phoneController.text,
                                      );
                                    } else {
                                      widget.onConfirm!(selectedType);
                                    }
                                  }
                                },
                                style: TextButton.styleFrom(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(22.r),
                                  ),
                                  padding: EdgeInsets.zero,
                                ),
                                child: Text(
                                  '确定',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 16.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/// 显示账户类型选择对话框的便捷方法
Future<Map<String, dynamic>?> showAccountTypeDialog(
  BuildContext context, {
  String? initialValue,
}) async {
  Map<String, dynamic>? result;

  await showDialog<String>(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return AccountTypeDialog(
        initialValue: initialValue,
        onConfirm: (selectedType,
            {String? operatorName, String? operatorPhone}) {
          result = {
            'type': selectedType,
            if (operatorName != null) 'operatorName': operatorName,
            if (operatorPhone != null) 'operatorPhone': operatorPhone,
          };
        },
      );
    },
  );

  return result;
}
