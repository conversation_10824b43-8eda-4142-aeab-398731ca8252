// base_tabbar_controller.dart
import 'package:flashy_tab_bar2/flashy_tab_bar2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smarthome/core/utils/user_manager.dart';
import 'package:flutter_smarthome/features/signature/my_signature_list.dart';
import 'package:flutter_smarthome/features/smart_home/controllers/native_page.dart';
import 'package:flutter_smarthome/features/shopping/controllers/shopping_home.dart';
import 'package:flutter_smarthome/features/discover/controllers/discover_home.dart';
import 'package:flutter_smarthome/features/personal/controllers/personal_home.dart';
import 'package:permission_handler/permission_handler.dart';

class BaseTabBarController extends StatefulWidget {
  const BaseTabBarController({super.key});

  @override
  _BaseTabBarControllerState createState() => _BaseTabBarControllerState();
}

class _BaseTabBarControllerState extends State<BaseTabBarController> {
  int _selectedIndex = 0;
  bool _isInitialized = false;
  bool _wasEmployee = false; // 追踪之前是否是员工模式

  @override
  void initState() {
    super.initState();
    checkPermission();
    UserManager.instance.notifier.addListener(_onUserChanged);
    _initializeUserState();
  }

  void _initializeUserState() async {
    await Future.delayed(Duration.zero);
    await UserManager.instance.loadUser();

    if (mounted) {
      setState(() {
        _isInitialized = true;

        // 初始化员工状态追踪
        final currentUser = UserManager.instance.user;
        final postId = currentUser?.postId;
        _wasEmployee = postId != null && postId.isNotEmpty && postId != '0';

        // 添加初始化调试信息
        print(
            'BaseTabBarController初始化: 用户=${currentUser != null ? "已登录" : "未登录"}, postId=$postId, isEmployee=$_wasEmployee');
      });
    }
  }

  @override
  void dispose() {
    UserManager.instance.notifier.removeListener(_onUserChanged);
    super.dispose();
  }

  void _onUserChanged() {
    if (mounted) {
      setState(() {
        _isInitialized = true;

        // 获取当前用户状态
        final currentUser = UserManager.instance.user;
        final postId = currentUser?.postId;
        final isEmployee = postId != null && postId.isNotEmpty && postId != '0';
        final shouldShowShopping = !isEmployee;

        // 计算新的tab总数
        final newTabCount = shouldShowShopping ? 4 : 3;

        // 检测用户模式是否发生了变化
        final modeChanged = _wasEmployee != isEmployee;

        // 如果当前选中的tab索引超出了新的tab范围，重置到第一个tab
        if (_selectedIndex >= newTabCount) {
          _selectedIndex = 0;
        }

        // 如果用户模式发生了变化，为了安全起见重置到第一个tab
        // 特别是从员工模式切换到游客/未登录模式时
        if (modeChanged) {
          _selectedIndex = 0;
          print(
              '用户模式发生变化: 从${_wasEmployee ? "员工" : "游客"}模式切换到${isEmployee ? "员工" : "游客"}模式，重置到第一个tab');
        }

        // 更新员工状态追踪
        _wasEmployee = isEmployee;

        // 添加调试信息
        print(
            '用户状态更新: 当前用户=${currentUser != null ? "已登录" : "未登录"}, postId=$postId, isEmployee=$isEmployee, tabCount=$newTabCount, selectedIndex=$_selectedIndex');
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final currentUser = UserManager.instance.user;
    final postId = currentUser?.postId;
    final shouldShowSignature =
        postId != null && postId.isNotEmpty && postId != '0';
    // 未登录用户(null)或普通用户(postId == '0')都显示4个tab，只有员工才显示3个tab
    final isEmployee = postId != null && postId.isNotEmpty && postId != '0';
    final shouldShowShopping = !isEmployee; // 非员工都显示商城tab

    // 在build时也检查用户状态是否发生了变化，作为额外的保护机制
    if (_wasEmployee != isEmployee) {
      // 如果发现状态不一致，立即更新
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _wasEmployee = isEmployee;
            _selectedIndex = 0; // 重置到第一个tab
            print(
                'Build时检测到用户状态不一致，强制更新: isEmployee=$isEmployee, 重置selectedIndex=0');
          });
        }
      });
    }

    final List<Widget> tabItems = [
      const DiscoverHomeWidget(),
      shouldShowSignature
          ? const MySignatureListWidget()
          : const NativePageWidget(),
      if (shouldShowShopping) const ShoppingHomeWidget(), // 非员工都显示商城
      const PersonalHomeWidget(),
    ];
    return Scaffold(
      body: IndexedStack(
        index: _selectedIndex,
        children: tabItems,
      ),
      bottomNavigationBar: FlashyTabBar(
        animationCurve: Curves.linear,
        selectedIndex: _selectedIndex,
        iconSize: 30,
        showElevation: false,
        onItemSelected: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: [
          FlashyTabBarItem(
            icon: Image.asset(
              'assets/images/tabbar/icon_tabbar_first.png',
              width: 24,
              height: 24,
            ),
            title: const Text(
              '发现',
              style: TextStyle(color: Colors.black),
            ),
          ),
          shouldShowSignature
              ? FlashyTabBarItem(
                  icon: Image.asset(
                    'assets/images/tabbar/icon_tabbar_signature.png',
                    width: 24,
                    height: 24,
                  ),
                  title: const Text(
                    '我的订单',
                    style: TextStyle(
                      color: Colors.black,
                    ),
                  ),
                )
              : FlashyTabBarItem(
                  icon: Image.asset(
                    'assets/images/tabbar/icon_tabbar_second.png',
                    width: 24,
                    height: 24,
                  ),
                  title: const Text(
                    '爱家',
                    style: TextStyle(color: Colors.black),
                  ),
                ),
          if (shouldShowShopping) // 非员工都显示商城标签
            FlashyTabBarItem(
              icon: Image.asset(
                'assets/images/tabbar/icon_tabbar_third.png',
                width: 24,
                height: 24,
              ),
              title: const Text(
                '惊喜',
                style: TextStyle(color: Colors.black),
              ),
            ),
          FlashyTabBarItem(
            icon: Image.asset(
              'assets/images/tabbar/icon_tabbar_four.png',
              width: 24,
              height: 24,
            ),
            title: const Text(
              '我的',
              style: TextStyle(color: Colors.black),
            ),
          ),
        ],
      ),
    );
  }

  void checkPermission() async {
    // 先申请“使用期间”权限
    PermissionStatus inUseStatus = await Permission.locationWhenInUse.status;
    if (!inUseStatus.isGranted) {
      inUseStatus = await Permission.locationWhenInUse.request();
    }
    // 如果“使用期间”权限已通过，再申请“始终”权限
    if (inUseStatus.isGranted) {
      PermissionStatus alwaysStatus = await Permission.locationAlways.status;
      if (!alwaysStatus.isGranted) {
        alwaysStatus = await Permission.locationAlways.request();
      }
      if (!alwaysStatus.isGranted) {
        // openAppSettings();
      }
    } else {
      // 若“使用期间”权限未通过，则直接跳转到设置
      // openAppSettings();
    }
  }

  //申请权限
  void requestPermission(Permission permission) async {
    PermissionStatus status = await permission.request();
    print('权限状态$status');
    if (!status.isGranted) {
      openAppSettings();
    }
  }
}
