import 'package:flutter/material.dart';
import 'package:flutter_smarthome/core/services/firebase_service.dart';

/// Firebase 测试页面
/// 用于测试 Firebase Crashlytics 功能是否正常工作
class FirebaseTestPage extends StatefulWidget {
  const FirebaseTestPage({super.key});

  @override
  State<FirebaseTestPage> createState() => _FirebaseTestPageState();
}

class _FirebaseTestPageState extends State<FirebaseTestPage> {
  String _logMessage = '';

  @override
  void initState() {
    super.initState();
  }

  /// 测试崩溃报告
  void _testCrashlytics() {
    FirebaseService.instance.recordError(
      Exception('这是一个测试异常'),
      StackTrace.current,
      reason: '用户点击了测试崩溃按钮',
    );
    _showSnackBar('崩溃报告已发送');
  }

  /// 测试自定义日志
  void _testCustomLog() {
    final message = '测试日志 - ${DateTime.now()}';
    FirebaseService.instance.log(message);
    setState(() {
      _logMessage = message;
    });
    _showSnackBar('自定义日志已记录');
  }

  /// 显示提示信息
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firebase Crashlytics 测试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 测试按钮
            const Text(
              'Crashlytics 功能测试:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            ElevatedButton(
              onPressed: _testCrashlytics,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('测试 Crashlytics'),
            ),
            const SizedBox(height: 8),

            ElevatedButton(
              onPressed: _testCustomLog,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
              ),
              child: const Text('测试自定义日志'),
            ),
            const SizedBox(height: 8),

            if (_logMessage.isNotEmpty) ...[
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '最新日志:',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(_logMessage),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
