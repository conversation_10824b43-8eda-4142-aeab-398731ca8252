import 'package:flutter/material.dart';
import 'network_debug_manager.dart';
import 'network_debug_detail_page.dart';

/// 全局调试浮窗管理器
class GlobalDebugOverlay {
  static GlobalDebugOverlay? _instance;
  static GlobalDebugOverlay get instance {
    _instance ??= GlobalDebugOverlay._internal();
    return _instance!;
  }

  GlobalDebugOverlay._internal();

  OverlayEntry? _buttonOverlay;

  void showButton(BuildContext context) {
    if (_buttonOverlay != null) return;

    _buttonOverlay = OverlayEntry(
      builder: (context) => _GlobalDraggableButton(
        onTap: () => showPage(context),
      ),
    );

    Overlay.of(context).insert(_buttonOverlay!);
  }

  void showPage(BuildContext context) {
    // 使用 Navigator 来打开完整的页面，使用 rootNavigator 确保能找到 Navigator
    Navigator.of(context, rootNavigator: true).push(
      MaterialPageRoute(
        builder: (context) => const NetworkDebugPage(),
      ),
    );
  }

  void hideButton() {
    _buttonOverlay?.remove();
    _buttonOverlay = null;
  }
}

/// 全局可拖拽调试按钮
class _GlobalDraggableButton extends StatefulWidget {
  final VoidCallback onTap;

  const _GlobalDraggableButton({
    required this.onTap,
  });

  @override
  State<_GlobalDraggableButton> createState() => _GlobalDraggableButtonState();
}

class _GlobalDraggableButtonState extends State<_GlobalDraggableButton> {
  final NetworkDebugManager _debugManager = NetworkDebugManager();
  Offset _position = const Offset(20, 100);
  bool _isVisible = true;

  @override
  void initState() {
    super.initState();
    _debugManager.addListener(_updateUI);
  }

  @override
  void dispose() {
    _debugManager.removeListener(_updateUI);
    super.dispose();
  }

  void _updateUI() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isVisible) {
      return const SizedBox.shrink();
    }

    final statistics = _debugManager.getStatistics();
    final hasError = statistics['error']! > 0;

    return Positioned(
      left: _position.dx,
      top: _position.dy,
      child: Material(
        type: MaterialType.transparency,
        child: GestureDetector(
          onPanUpdate: (details) {
            setState(() {
              _position += details.delta;

              // 确保按钮不会移出屏幕
              final screenSize = MediaQuery.of(context).size;
              _position = Offset(
                _position.dx.clamp(0, screenSize.width - 60),
                _position.dy.clamp(0, screenSize.height - 60),
              );
            });
          },
          onTap: widget.onTap,
          onLongPress: () {
            // 长按隐藏按钮
            setState(() {
              _isVisible = false;
            });

            // 3秒后自动显示
            Future.delayed(const Duration(seconds: 3), () {
              if (mounted) {
                setState(() {
                  _isVisible = true;
                });
              }
            });
          },
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: hasError ? Colors.red : Colors.blue,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Stack(
              children: [
                const Center(
                  child: Icon(
                    Icons.network_check,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                if (statistics['total']! > 0)
                  Positioned(
                    right: 4,
                    top: 4,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        '${statistics['total']}',
                        style: TextStyle(
                          color: hasError ? Colors.red : Colors.blue,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 网络调试浮窗组件
class NetworkDebugOverlay extends StatefulWidget {
  final Widget child;

  const NetworkDebugOverlay({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<NetworkDebugOverlay> createState() => _NetworkDebugOverlayState();
}

class _NetworkDebugOverlayState extends State<NetworkDebugOverlay> {
  @override
  void initState() {
    super.initState();
    // 延迟初始化，确保 Overlay 已经准备好
    WidgetsBinding.instance.addPostFrameCallback((_) {
      GlobalDebugOverlay.instance.showButton(context);
    });
  }

  @override
  void dispose() {
    // 当组件销毁时，不需要隐藏按钮，因为我们希望调试按钮在整个应用生命周期中保持可见
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 只返回子组件，调试按钮由 GlobalDebugOverlay 管理
    return widget.child;
  }
}

/// 请求筛选类型枚举
enum RequestFilterType {
  all('全部'),
  success('成功'),
  error('错误'),
  pending('进行中');

  const RequestFilterType(this.label);
  final String label;
}

/// 网络调试主页面
class NetworkDebugPage extends StatefulWidget {
  const NetworkDebugPage({Key? key}) : super(key: key);

  @override
  State<NetworkDebugPage> createState() => _NetworkDebugPageState();
}

class _NetworkDebugPageState extends State<NetworkDebugPage>
    with SingleTickerProviderStateMixin {
  final NetworkDebugManager _debugManager = NetworkDebugManager();
  late TabController _tabController;
  RequestFilterType _currentFilter = RequestFilterType.all;

  @override
  void initState() {
    super.initState();
    _debugManager.addListener(_updateUI);
    _tabController =
        TabController(length: RequestFilterType.values.length, vsync: this);
    _tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    _debugManager.removeListener(_updateUI);
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  void _updateUI() {
    if (mounted) {
      setState(() {});
    }
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      setState(() {
        _currentFilter = RequestFilterType.values[_tabController.index];
      });
    }
  }

  /// 根据筛选条件过滤请求列表
  List<NetworkDebugItem> _getFilteredRequests() {
    final allRequests = _debugManager.requests;

    switch (_currentFilter) {
      case RequestFilterType.all:
        return allRequests;
      case RequestFilterType.success:
        return allRequests.where((request) => request.isSuccess).toList();
      case RequestFilterType.error:
        return allRequests.where((request) => request.isError).toList();
      case RequestFilterType.pending:
        return allRequests
            .where((request) => !request.isSuccess && !request.isError)
            .toList();
    }
  }

  @override
  Widget build(BuildContext context) {
    final filteredRequests = _getFilteredRequests();
    final statistics = _debugManager.getStatistics();

    return Scaffold(
      appBar: AppBar(
        title: const Text('网络调试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: () {
              _debugManager.clear();
            },
            tooltip: '清空记录',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: RequestFilterType.values.map((filter) {
            int count = 0;
            switch (filter) {
              case RequestFilterType.all:
                count = statistics['total']!;
                break;
              case RequestFilterType.success:
                count = statistics['success']!;
                break;
              case RequestFilterType.error:
                count = statistics['error']!;
                break;
              case RequestFilterType.pending:
                count = statistics['pending']!;
                break;
            }
            return Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(filter.label),
                  const SizedBox(width: 4),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      count.toString(),
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
      body: Column(
        children: [
          // 请求列表
          Expanded(
            child: filteredRequests.isEmpty
                ? Center(
                    child: Text(
                      _currentFilter == RequestFilterType.all
                          ? '暂无网络请求记录'
                          : '暂无${_currentFilter.label}请求',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  )
                : ListView.builder(
                    itemCount: filteredRequests.length,
                    itemBuilder: (context, index) {
                      final request = filteredRequests[index];
                      return _buildRequestItem(request);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequestItem(NetworkDebugItem request) {
    Color statusColor;
    if (request.isSuccess) {
      statusColor = Colors.green;
    } else if (request.isError) {
      statusColor = Colors.red;
    } else {
      statusColor = Colors.orange;
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: ListTile(
        leading: Container(
          width: 8,
          height: 40,
          decoration: BoxDecoration(
            color: statusColor,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        title: Text(
          '${request.method} ${request.url}',
          style: const TextStyle(fontSize: 14),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '时间: ${_formatTime(request.timestamp)}',
              style: const TextStyle(fontSize: 12),
            ),
            if (request.statusCode != null)
              Text(
                '状态: ${request.statusCode}',
                style: TextStyle(fontSize: 12, color: statusColor),
              ),
            if (request.duration != null)
              Text(
                '耗时: ${request.duration!.inMilliseconds}ms',
                style: const TextStyle(fontSize: 12),
              ),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => NetworkDebugDetailPage(request: request),
            ),
          );
        },
      ),
    );
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:'
        '${time.minute.toString().padLeft(2, '0')}:'
        '${time.second.toString().padLeft(2, '0')}';
  }
}
