import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// 网络请求调试数据模型
class NetworkDebugItem {
  final String id;
  final DateTime timestamp;
  final String method;
  final String url;
  final Map<String, dynamic> headers;
  final dynamic requestData;
  final int? statusCode;
  final dynamic responseData;
  final String? error;
  final Duration? duration;

  NetworkDebugItem({
    required this.id,
    required this.timestamp,
    required this.method,
    required this.url,
    required this.headers,
    this.requestData,
    this.statusCode,
    this.responseData,
    this.error,
    this.duration,
  });

  NetworkDebugItem copyWith({
    int? statusCode,
    dynamic responseData,
    String? error,
    Duration? duration,
  }) {
    return NetworkDebugItem(
      id: id,
      timestamp: timestamp,
      method: method,
      url: url,
      headers: headers,
      requestData: requestData,
      statusCode: statusCode ?? this.statusCode,
      responseData: responseData ?? this.responseData,
      error: error ?? this.error,
      duration: duration ?? this.duration,
    );
  }

  bool get isSuccess =>
      statusCode != null && statusCode! >= 200 && statusCode! < 300;
  bool get isError =>
      error != null || (statusCode != null && statusCode! >= 400);
}

/// 网络调试管理器 - 单例模式
class NetworkDebugManager {
  static final NetworkDebugManager _instance = NetworkDebugManager._internal();
  factory NetworkDebugManager() => _instance;
  NetworkDebugManager._internal();

  // 存储网络请求记录，最多保存100条
  final List<NetworkDebugItem> _requests = [];
  static const int _maxRequests = 100;

  // 监听器列表
  final List<VoidCallback> _listeners = [];

  /// 获取所有请求记录
  List<NetworkDebugItem> get requests => List.unmodifiable(_requests);

  /// 添加监听器
  void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  /// 移除监听器
  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  /// 通知监听器
  void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }

  /// 记录请求开始
  String logRequest(RequestOptions options) {
    // 暂时先不管是不是 debug 环境，都打开调试工具
    // if (!kDebugMode) return '';

    final id = DateTime.now().millisecondsSinceEpoch.toString();
    final item = NetworkDebugItem(
      id: id,
      timestamp: DateTime.now(),
      method: options.method,
      url: '${options.baseUrl}${options.path}',
      headers: Map<String, dynamic>.from(options.headers),
      requestData: options.data,
    );

    _addRequest(item);
    return id;
  }

  /// 记录响应成功
  void logResponse(String requestId, Response response, Duration duration) {
    // 暂时先不管是不是 debug 环境，都打开调试工具
    // if (!kDebugMode) return;

    _updateRequest(
        requestId,
        (item) => item.copyWith(
              statusCode: response.statusCode,
              responseData: response.data,
              duration: duration,
            ));
  }

  /// 记录请求错误
  void logError(String requestId, DioException error, Duration duration) {
    // 暂时先不管是不是 debug 环境，都打开调试工具
    // if (!kDebugMode) return;

    _updateRequest(
        requestId,
        (item) => item.copyWith(
              statusCode: error.response?.statusCode,
              responseData: error.response?.data,
              error: error.message ?? error.toString(),
              duration: duration,
            ));
  }

  /// 添加请求记录
  void _addRequest(NetworkDebugItem item) {
    _requests.insert(0, item); // 最新的在前面

    // 保持最大数量限制
    if (_requests.length > _maxRequests) {
      _requests.removeRange(_maxRequests, _requests.length);
    }

    _notifyListeners();
  }

  /// 更新请求记录
  void _updateRequest(
      String requestId, NetworkDebugItem Function(NetworkDebugItem) updater) {
    final index = _requests.indexWhere((item) => item.id == requestId);
    if (index != -1) {
      _requests[index] = updater(_requests[index]);
      _notifyListeners();
    }
  }

  /// 清空所有记录
  void clear() {
    _requests.clear();
    _notifyListeners();
  }

  /// 获取请求统计信息
  Map<String, int> getStatistics() {
    final total = _requests.length;
    final success = _requests.where((item) => item.isSuccess).length;
    final error = _requests.where((item) => item.isError).length;
    final pending = total - success - error;

    return {
      'total': total,
      'success': success,
      'error': error,
      'pending': pending,
    };
  }
}
