import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import 'network_debug_manager.dart';

/// 网络请求详情页面
class NetworkDebugDetailPage extends StatefulWidget {
  final NetworkDebugItem request;

  const NetworkDebugDetailPage({
    Key? key,
    required this.request,
  }) : super(key: key);

  @override
  State<NetworkDebugDetailPage> createState() => _NetworkDebugDetailPageState();
}

class _NetworkDebugDetailPageState extends State<NetworkDebugDetailPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.request.method} 详情'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.copy),
            onPressed: _copyAllInfo,
            tooltip: '复制全部信息',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: '概览'),
            Tab(text: '请求'),
            Tab(text: '响应'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildRequestTab(),
          _buildResponseTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    final request = widget.request;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard('基本信息', [
            _buildInfoRow('请求方法', request.method),
            _buildInfoRow('请求URL', request.url),
            _buildInfoRow('请求时间', _formatDateTime(request.timestamp)),
            if (request.statusCode != null)
              _buildInfoRow('状态码', '${request.statusCode}'),
            if (request.duration != null)
              _buildInfoRow('耗时', '${request.duration!.inMilliseconds}ms'),
          ]),
          const SizedBox(height: 16),
          _buildInfoCard('状态', [
            _buildStatusRow(),
          ]),
          if (request.error != null) ...[
            const SizedBox(height: 16),
            _buildInfoCard('错误信息', [
              _buildInfoRow('错误', request.error!),
            ]),
          ],
        ],
      ),
    );
  }

  Widget _buildRequestTab() {
    final request = widget.request;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard('请求头', [
            ...request.headers.entries.map(
              (entry) => _buildInfoRow(entry.key, entry.value.toString()),
            ),
          ]),
          if (request.requestData != null) ...[
            const SizedBox(height: 16),
            _buildDataCard('请求体', request.requestData),
          ],
        ],
      ),
    );
  }

  Widget _buildResponseTab() {
    final request = widget.request;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (request.statusCode != null)
            _buildInfoCard('响应信息', [
              _buildInfoRow('状态码', '${request.statusCode}'),
              if (request.duration != null)
                _buildInfoRow('响应时间', '${request.duration!.inMilliseconds}ms'),
            ]),
          if (request.responseData != null) ...[
            const SizedBox(height: 16),
            _buildDataCard('响应体', request.responseData),
          ],
          if (request.error != null) ...[
            const SizedBox(height: 16),
            _buildInfoCard('错误信息', [
              _buildInfoRow('错误', request.error!),
            ]),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoCard(String title, List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildDataCard(String title, dynamic data) {
    String formattedData;
    try {
      if (data is String) {
        // 尝试解析JSON字符串
        final decoded = jsonDecode(data);
        formattedData = const JsonEncoder.withIndent('  ').convert(decoded);
      } else {
        formattedData = const JsonEncoder.withIndent('  ').convert(data);
      }
    } catch (e) {
      formattedData = data.toString();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.copy, size: 20),
                  onPressed: () => _copyToClipboard(formattedData),
                  tooltip: '复制',
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: SelectableText(
                formattedData,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: SelectableText(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRow() {
    final request = widget.request;
    Color statusColor;
    String statusText;
    
    if (request.isSuccess) {
      statusColor = Colors.green;
      statusText = '成功';
    } else if (request.isError) {
      statusColor = Colors.red;
      statusText = '失败';
    } else {
      statusColor = Colors.orange;
      statusText = '进行中';
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          const SizedBox(
            width: 80,
            child: Text(
              '状态:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: statusColor),
            ),
            child: Text(
              statusText,
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('已复制到剪贴板')),
    );
  }

  void _copyAllInfo() {
    final request = widget.request;
    final buffer = StringBuffer();
    
    buffer.writeln('=== 网络请求详情 ===');
    buffer.writeln('请求方法: ${request.method}');
    buffer.writeln('请求URL: ${request.url}');
    buffer.writeln('请求时间: ${_formatDateTime(request.timestamp)}');
    if (request.statusCode != null) {
      buffer.writeln('状态码: ${request.statusCode}');
    }
    if (request.duration != null) {
      buffer.writeln('耗时: ${request.duration!.inMilliseconds}ms');
    }
    
    buffer.writeln('\n=== 请求头 ===');
    request.headers.forEach((key, value) {
      buffer.writeln('$key: $value');
    });
    
    if (request.requestData != null) {
      buffer.writeln('\n=== 请求体 ===');
      try {
        buffer.writeln(const JsonEncoder.withIndent('  ').convert(request.requestData));
      } catch (e) {
        buffer.writeln(request.requestData.toString());
      }
    }
    
    if (request.responseData != null) {
      buffer.writeln('\n=== 响应体 ===');
      try {
        buffer.writeln(const JsonEncoder.withIndent('  ').convert(request.responseData));
      } catch (e) {
        buffer.writeln(request.responseData.toString());
      }
    }
    
    if (request.error != null) {
      buffer.writeln('\n=== 错误信息 ===');
      buffer.writeln(request.error);
    }
    
    _copyToClipboard(buffer.toString());
  }
}
