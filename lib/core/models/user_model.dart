import 'base_model.dart';

class UserModel extends BaseModel {
  String? mobile; // 手机号
  String? password; // 密码
  String? nickname; // 昵称
  String? name; // 姓名
  String? sex; // 性别 0：未知  1：男  2：女
  String? avatar; // 头像（https Url）
  String? tuyaPwd; // 涂鸦密码
  String? terminalId; // 终端 ID
  String? accessToken; // 鉴权
  String? refreshToken; // 刷新 Token
  String? city; // 城市
  String? profile; // 简介
  String? postId; // 职位 ID

  // 构造函数
  UserModel({
    this.mobile,
    this.password,
    this.nickname,
    this.name,
    this.sex,
    this.avatar,
    this.tuyaPwd,
    this.terminalId,
    this.accessToken,
    this.refreshToken,
    this.city,
    this.profile,
    this.postId,
  });

  // 从 JSON 创建一个 UserModel 实例
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      mobile: BaseModel.safeString(json, 'mobile'),
      password: BaseModel.safeString(json, 'password'),
      nickname: BaseModel.safeString(json, 'nickname'),
      name: BaseModel.safeString(json, 'name'),
      sex: BaseModel.safeString(json, 'sex'),
      avatar: BaseModel.safeString(json, 'avatar'),
      tuyaPwd: BaseModel.safeString(json, 'tuyaPwd'),
      terminalId: BaseModel.safeString(json, 'terminalId'),
      accessToken: BaseModel.safeString(json, 'accessToken'),
      refreshToken: BaseModel.safeString(json, 'refreshToken'),
      city: BaseModel.safeString(json, 'city'),
      profile: BaseModel.safeString(json, 'profile'),
      postId: BaseModel.safeString(json, 'postId'),
    );
  }

  // 将 UserModel 实例转换为 JSON
  @override
  Map<String, dynamic> toJson() {
    return {
      'mobile': mobile,
      'password': password,
      'nickname': nickname,
      'name': name,
      'sex': sex,
      'avatar': avatar,
      'tuyaPwd': tuyaPwd,
      'terminalId': terminalId,
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'city': city,
      'profile': profile,
      'postId': postId,
    };
  }

  // 创建UserModel的副本
  @override
  UserModel copyWith({
    String? mobile,
    String? password,
    String? nickname,
    String? name,
    String? sex,
    String? avatar,
    String? tuyaPwd,
    String? terminalId,
    String? accessToken,
    String? refreshToken,
    String? city,
    String? profile,
    String? postId,
  }) {
    return UserModel(
      mobile: mobile ?? this.mobile,
      password: password ?? this.password,
      nickname: nickname ?? this.nickname,
      name: name ?? this.name,
      sex: sex ?? this.sex,
      avatar: avatar ?? this.avatar,
      tuyaPwd: tuyaPwd ?? this.tuyaPwd,
      terminalId: terminalId ?? this.terminalId,
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      city: city ?? this.city,
      profile: profile ?? this.profile,
      postId: postId ?? this.postId,
    );
  }
}
