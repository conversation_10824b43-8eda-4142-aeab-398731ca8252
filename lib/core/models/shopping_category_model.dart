import 'base_model.dart';

/// 子分类模型
class ChildCommodityCategoryModel extends BaseModel {
  final String? categoryId;
  final String? categoryName;
  final String? categoryIcon;

  ChildCommodityCategoryModel({
    this.categoryId,
    this.categoryName,
    this.categoryIcon,
  });

  factory ChildCommodityCategoryModel.fromJson(Map<String, dynamic> json) {
    return ChildCommodityCategoryModel(
      categoryId: BaseModel.safeString(json, 'categoryId'),
      categoryName: BaseModel.safeString(json, 'categoryName'),
      categoryIcon: BaseModel.safeString(json, 'categoryIcon'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'categoryId': categoryId,
      'categoryName': categoryName,
      'categoryIcon': categoryIcon,
    };
  }

  @override
  ChildCommodityCategoryModel copyWith({
    String? categoryId,
    String? categoryName,
    String? categoryIcon,
  }) {
    return ChildCommodityCategoryModel(
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      categoryIcon: categoryIcon ?? this.categoryIcon,
    );
  }

  static List<ChildCommodityCategoryModel> listFromJson(List<dynamic>? list) {
    return BaseModel.fromJsonList<ChildCommodityCategoryModel>(
      list,
      (json) => ChildCommodityCategoryModel.fromJson(json),
    );
  }
}

/// 一级分类模型
class ShoppingCategoryModel extends BaseModel {
  final String? categoryId;
  final String? categoryName;
  final List<ChildCommodityCategoryModel>? childCommodityCategoryList;

  ShoppingCategoryModel({
    this.categoryId,
    this.categoryName,
    this.childCommodityCategoryList,
  });

  factory ShoppingCategoryModel.fromJson(Map<String, dynamic> json) {
    return ShoppingCategoryModel(
      categoryId: BaseModel.safeString(json, 'categoryId'),
      categoryName: BaseModel.safeString(json, 'categoryName'),
      childCommodityCategoryList: ChildCommodityCategoryModel.listFromJson(
        BaseModel.safeList(json, 'childCommodityCategoryList'),
      ),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'categoryId': categoryId,
      'categoryName': categoryName,
      'childCommodityCategoryList':
          childCommodityCategoryList?.map((e) => e.toJson()).toList(),
    };
  }

  @override
  ShoppingCategoryModel copyWith({
    String? categoryId,
    String? categoryName,
    List<ChildCommodityCategoryModel>? childCommodityCategoryList,
  }) {
    return ShoppingCategoryModel(
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      childCommodityCategoryList:
          childCommodityCategoryList ?? this.childCommodityCategoryList,
    );
  }

  static List<ShoppingCategoryModel> listFromJson(List<dynamic>? list) {
    return BaseModel.fromJsonList<ShoppingCategoryModel>(
      list,
      (json) => ShoppingCategoryModel.fromJson(json),
    );
  }
}
