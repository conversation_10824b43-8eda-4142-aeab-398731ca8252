import 'base_model.dart';

/// 报价明细整体模型
class QuotePriceDetailModel extends BaseModel {
  final QuickPriceResult? quickPriceResult; // 总价信息
  final List<MaterialCategory>? material;   // 材料清单

  QuotePriceDetailModel({
    this.quickPriceResult,
    this.material,
  });

  /// JSON 解析
  factory QuotePriceDetailModel.fromJson(Map<String, dynamic> json) {
    // 解析材料列表
    List<MaterialCategory> materialList = [];
    final materialData = json['material'];
    if (materialData is List) {
      for (var item in materialData) {
        if (item is Map<String, dynamic>) {
          materialList.add(MaterialCategory.fromJson(item));
        }
      }
    }

    return QuotePriceDetailModel(
      quickPriceResult: json['quickPriceResult'] is Map<String, dynamic>
          ? QuickPriceResult.fromJson(
              json['quickPriceResult'] as Map<String, dynamic>)
          : null,
      material: materialList,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'quickPriceResult': quickPriceResult?.toJson(),
      'material': material?.map((e) => e.toJson()).toList(),
    };
  }

  @override
  QuotePriceDetailModel copyWith({
    QuickPriceResult? quickPriceResult,
    List<MaterialCategory>? material,
  }) {
    return QuotePriceDetailModel(
      quickPriceResult: quickPriceResult ?? this.quickPriceResult,
      material: material ?? this.material,
    );
  }
}

/// 快速报价结果
class QuickPriceResult extends BaseModel {
  final double? totalPrice; // 总价 (单位：万)

  QuickPriceResult({this.totalPrice});

  factory QuickPriceResult.fromJson(Map<String, dynamic> json) {
    return QuickPriceResult(
      totalPrice: BaseModel.safeDouble(json, 'totalPrice'),
    );
  }

  @override
  Map<String, dynamic> toJson() => {
        'totalPrice': totalPrice,
      };

  @override
  QuickPriceResult copyWith({double? totalPrice}) {
    return QuickPriceResult(
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }
}

/// 材料大类 (例如 主材/辅材...)
class MaterialCategory extends BaseModel {
  final String? budgetDisplay; // 分类名称
  final List<MaterialItem>? items; // 具体条目

  MaterialCategory({this.budgetDisplay, this.items});

  factory MaterialCategory.fromJson(Map<String, dynamic> json) {
    List<MaterialItem> itemList = [];
    final itemsData = json['items'];
    if (itemsData is List) {
      for (var itm in itemsData) {
        if (itm is Map<String, dynamic>) {
          itemList.add(MaterialItem.fromJson(itm));
        }
      }
    }

    return MaterialCategory(
      budgetDisplay: BaseModel.safeString(json, 'budgetDisplay'),
      items: itemList,
    );
  }

  @override
  Map<String, dynamic> toJson() => {
        'budgetDisplay': budgetDisplay,
        'items': items?.map((e) => e.toJson()).toList(),
      };

  @override
  MaterialCategory copyWith({
    String? budgetDisplay,
    List<MaterialItem>? items,
  }) {
    return MaterialCategory(
      budgetDisplay: budgetDisplay ?? this.budgetDisplay,
      items: items ?? this.items,
    );
  }
}

/// 材料条目
class MaterialItem extends BaseModel {
  final String? skuPic;
  final String? materialName;
  final String? brandName;
  final String? sku;
  final double? quotaUsage;

  MaterialItem({
    this.skuPic,
    this.materialName,
    this.brandName,
    this.sku,
    this.quotaUsage,
  });

  factory MaterialItem.fromJson(Map<String, dynamic> json) {
    return MaterialItem(
      skuPic: BaseModel.safeString(json, 'skuPic'),
      materialName: BaseModel.safeString(json, 'materialName'),
      brandName: BaseModel.safeString(json, 'brandName'),
      sku: BaseModel.safeString(json, 'sku'),
      quotaUsage: BaseModel.safeDouble(json, 'quotaUsage'),
    );
  }

  @override
  Map<String, dynamic> toJson() => {
        'skuPic': skuPic,
        'materialName': materialName,
        'brandName': brandName,
        'sku': sku,
        'quotaUsage': quotaUsage,
      };

  @override
  MaterialItem copyWith({
    String? skuPic,
    String? materialName,
    String? brandName,
    String? sku,
    double? quotaUsage,
  }) {
    return MaterialItem(
      skuPic: skuPic ?? this.skuPic,
      materialName: materialName ?? this.materialName,
      brandName: brandName ?? this.brandName,
      sku: sku ?? this.sku,
      quotaUsage: quotaUsage ?? this.quotaUsage,
    );
  }
} 