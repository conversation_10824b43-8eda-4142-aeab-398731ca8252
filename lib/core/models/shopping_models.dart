import 'base_model.dart';

/// 商品详情模型
class ShoppingDetail extends BaseModel {
  String? id;
  String? name;
  String? mainPic;
  List<String>? picUrls;
  String? commodityInfo; // HTML内容
  String? businessId;
  String? businessName;
  String? businessLogo;
  double? minPrice;
  String? minPointPrice;
  String? isCollection; // 收藏ID，如果已收藏
  int? collectionCount;
  String? commodityPropertyId;
  List<CommodityProperty>? commodityPropertyList;

  ShoppingDetail({
    this.id,
    this.name,
    this.mainPic,
    this.picUrls,
    this.commodityInfo,
    this.businessId,
    this.businessName,
    this.businessLogo,
    this.minPrice,
    this.minPointPrice,
    this.isCollection,
    this.collectionCount,
    this.commodityPropertyId,
    this.commodityPropertyList,
  });

  factory ShoppingDetail.fromJson(Map<String, dynamic> json) {
    return ShoppingDetail(
      id: BaseModel.safeString(json, 'id'),
      name: BaseModel.safeString(json, 'name'),
      mainPic: BaseModel.safeString(json, 'mainPic'),
      picUrls: BaseModel.safeStringList(json, 'picUrls'),
      commodityInfo: BaseModel.safeString(json, 'commodityInfo'),
      businessId: BaseModel.safeString(json, 'businessId'),
      businessName: BaseModel.safeString(json, 'businessName'),
      businessLogo: BaseModel.safeString(json, 'businessLogo'),
      minPrice: BaseModel.safeDouble(json, 'minPrice'),
      minPointPrice: BaseModel.safeString(json, 'minPointPrice'),
      isCollection: BaseModel.safeString(json, 'isCollection'),
      collectionCount: BaseModel.safeInt(json, 'collectionCount'),
      commodityPropertyId: BaseModel.safeString(json, 'commodityPropertyId'),
      commodityPropertyList: BaseModel.fromJsonList(
        BaseModel.safeList(json, 'commodityPropertyList'),
        CommodityProperty.fromJson,
      ),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'mainPic': mainPic,
      'picUrls': picUrls,
      'commodityInfo': commodityInfo,
      'businessId': businessId,
      'businessName': businessName,
      'businessLogo': businessLogo,
      'minPrice': minPrice,
      'minPointPrice': minPointPrice,
      'isCollection': isCollection,
      'collectionCount': collectionCount,
      'commodityPropertyId': commodityPropertyId,
      'commodityPropertyList':
          commodityPropertyList?.map((e) => e.toJson()).toList(),
    };
  }

  @override
  ShoppingDetail copyWith({
    String? id,
    String? name,
    String? mainPic,
    List<String>? picUrls,
    String? commodityInfo,
    String? businessId,
    String? businessName,
    String? businessLogo,
    double? minPrice,
    String? minPointPrice,
    String? isCollection,
    int? collectionCount,
    String? commodityPropertyId,
    List<CommodityProperty>? commodityPropertyList,
  }) {
    return ShoppingDetail(
      id: id ?? this.id,
      name: name ?? this.name,
      mainPic: mainPic ?? this.mainPic,
      picUrls: picUrls ?? this.picUrls,
      commodityInfo: commodityInfo ?? this.commodityInfo,
      businessId: businessId ?? this.businessId,
      businessName: businessName ?? this.businessName,
      businessLogo: businessLogo ?? this.businessLogo,
      minPrice: minPrice ?? this.minPrice,
      minPointPrice: minPointPrice ?? this.minPointPrice,
      isCollection: isCollection ?? this.isCollection,
      collectionCount: collectionCount ?? this.collectionCount,
      commodityPropertyId: commodityPropertyId ?? this.commodityPropertyId,
      commodityPropertyList:
          commodityPropertyList ?? this.commodityPropertyList,
    );
  }
}

/// 商品属性模型
class CommodityProperty extends BaseModel {
  String? id;
  String? name;
  String? value;
  double? price;
  String? image;

  CommodityProperty({
    this.id,
    this.name,
    this.value,
    this.price,
    this.image,
  });

  factory CommodityProperty.fromJson(Map<String, dynamic> json) {
    return CommodityProperty(
      id: BaseModel.safeString(json, 'id'),
      name: BaseModel.safeString(json, 'name'),
      value: BaseModel.safeString(json, 'value'),
      price: BaseModel.safeDouble(json, 'price'),
      image: BaseModel.safeString(json, 'image'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'value': value,
      'price': price,
      'image': image,
    };
  }

  @override
  CommodityProperty copyWith({
    String? id,
    String? name,
    String? value,
    double? price,
    String? image,
  }) {
    return CommodityProperty(
      id: id ?? this.id,
      name: name ?? this.name,
      value: value ?? this.value,
      price: price ?? this.price,
      image: image ?? this.image,
    );
  }
}

/// 购物车项目模型
class ShoppingCartItem extends BaseModel {
  String? id; // 购物车ID (对应原来的cartId)
  String? commodityId;
  String? commodityPropertyId;
  String? name; // 商品名称
  String? mainPic; // 商品主图
  String? commodityProperty; // 商品属性字符串
  String? businessName;
  String? businessLogo;
  int? count; // 数量
  double? salesPrice; // 销售价格
  String? pointPrice; // 积分价格
  String? commoditySkuStatus; // "0": 失效, "1": 有效
  bool? selected;

  ShoppingCartItem({
    this.id,
    this.commodityId,
    this.commodityPropertyId,
    this.name,
    this.mainPic,
    this.commodityProperty,
    this.businessName,
    this.businessLogo,
    this.count,
    this.salesPrice,
    this.pointPrice,
    this.commoditySkuStatus,
    this.selected = false,
  });

  factory ShoppingCartItem.fromJson(Map<String, dynamic> json) {
    return ShoppingCartItem(
      id: BaseModel.safeString(json, 'id'),
      commodityId: BaseModel.safeString(json, 'commodityId'),
      commodityPropertyId: BaseModel.safeString(json, 'commodityPropertyId'),
      name: BaseModel.safeString(json, 'name'),
      mainPic: BaseModel.safeString(json, 'mainPic'),
      commodityProperty: BaseModel.safeString(json, 'commodityProperty'),
      businessName: BaseModel.safeString(json, 'businessName'),
      businessLogo: BaseModel.safeString(json, 'businessLogo'),
      count: BaseModel.safeInt(json, 'count'),
      salesPrice: BaseModel.safeDouble(json, 'salesPrice'),
      pointPrice: BaseModel.safeString(json, 'pointPrice'),
      commoditySkuStatus: BaseModel.safeString(json, 'commoditySkuStatus'),
      selected: false,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'commodityId': commodityId,
      'commodityPropertyId': commodityPropertyId,
      'name': name,
      'mainPic': mainPic,
      'commodityProperty': commodityProperty,
      'businessName': businessName,
      'businessLogo': businessLogo,
      'count': count,
      'salesPrice': salesPrice,
      'pointPrice': pointPrice,
      'commoditySkuStatus': commoditySkuStatus,
    };
  }

  @override
  ShoppingCartItem copyWith({
    String? id,
    String? commodityId,
    String? commodityPropertyId,
    String? name,
    String? mainPic,
    String? commodityProperty,
    String? businessName,
    String? businessLogo,
    int? count,
    double? salesPrice,
    String? pointPrice,
    String? commoditySkuStatus,
    bool? selected,
  }) {
    return ShoppingCartItem(
      id: id ?? this.id,
      commodityId: commodityId ?? this.commodityId,
      commodityPropertyId: commodityPropertyId ?? this.commodityPropertyId,
      name: name ?? this.name,
      mainPic: mainPic ?? this.mainPic,
      commodityProperty: commodityProperty ?? this.commodityProperty,
      businessName: businessName ?? this.businessName,
      businessLogo: businessLogo ?? this.businessLogo,
      count: count ?? this.count,
      salesPrice: salesPrice ?? this.salesPrice,
      pointPrice: pointPrice ?? this.pointPrice,
      commoditySkuStatus: commoditySkuStatus ?? this.commoditySkuStatus,
      selected: selected ?? this.selected,
    );
  }

  /// 是否有效
  bool get isValid => commoditySkuStatus == "1";

  /// 是否失效
  bool get isInvalid => commoditySkuStatus == "0";
}
