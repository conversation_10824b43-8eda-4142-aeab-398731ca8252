class CaseModel {
  final dynamic id;
  final String caseTitle;
  final String caseIntro;
  final List<dynamic> caseMainPic;
  final String createTime;
  final String? householdType; // 户型
  final String? designStyle; // 设计风格
  final String? avatar; // 设计师头像
  final String? realName; // 设计师姓名

  CaseModel({
    required this.id,
    required this.caseTitle,
    required this.caseIntro,
    required this.caseMainPic,
    required this.createTime,
    this.householdType,
    this.designStyle,
    this.avatar,
    this.realName,
  });

  factory CaseModel.fromJson(Map<String, dynamic> json) {
    return CaseModel(
      id: json['id'],
      caseTitle: json['caseTitle'] ?? '',
      caseIntro: json['caseIntro'] ?? '',
      caseMainPic: (json['caseMainPic'] as List?) ?? [],
      createTime: json['createTime'] ?? '',
      householdType: json['householdType'],
      designStyle: json['designStyle'],
      avatar: json['avatar'],
      realName: json['realName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'caseTitle': caseTitle,
      'caseIntro': caseIntro,
      'caseMainPic': caseMainPic,
      'createTime': createTime,
      'householdType': householdType,
      'designStyle': designStyle,
      'avatar': avatar,
      'realName': realName,
    };
  }

  CaseModel copyWith({
    dynamic id,
    String? caseTitle,
    String? caseIntro,
    List<dynamic>? caseMainPic,
    String? createTime,
    String? householdType,
    String? designStyle,
    String? avatar,
    String? realName,
  }) {
    return CaseModel(
      id: id ?? this.id,
      caseTitle: caseTitle ?? this.caseTitle,
      caseIntro: caseIntro ?? this.caseIntro,
      caseMainPic: caseMainPic ?? this.caseMainPic,
      createTime: createTime ?? this.createTime,
      householdType: householdType ?? this.householdType,
      designStyle: designStyle ?? this.designStyle,
      avatar: avatar ?? this.avatar,
      realName: realName ?? this.realName,
    );
  }
}
