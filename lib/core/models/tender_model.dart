import 'base_model.dart';

/// 招标/投标信息模型
class TenderModel extends BaseModel {
  String? name; // 业主姓名或设计师姓名
  String? phone; // 联系电话
  String? decorateType; // 装修类型
  String? city; // 城市
  String? region; // 区域/区县

  TenderModel({
    this.name,
    this.phone,
    this.decorateType,
    this.city,
    this.region,
  });

  /// 通过 JSON 创建实例
  factory TenderModel.fromJson(Map<String, dynamic> json) {
    return TenderModel(
      name: BaseModel.safeString(json, 'name'),
      phone: BaseModel.safeString(json, 'phone'),
      decorateType: BaseModel.safeString(json, 'decorateType'),
      city: BaseModel.safeString(json, 'city'),
      region: BaseModel.safeString(json, 'region'),
    );
  }

  /// 转换为 JSON
  @override
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phone': phone,
      'decorateType': decorateType,
      'city': city,
      'region': region,
    };
  }

  /// 复制当前对象
  @override
  TenderModel copyWith() {
    return TenderModel(
      name: name,
      phone: phone,
      decorateType: decorateType,
      city: city,
      region: region,
    );
  }

  /// 提供字段可选替换的复制方法
  TenderModel copyWithFields({
    String? name,
    String? phone,
    String? decorateType,
    String? city,
    String? region,
  }) {
    return TenderModel(
      name: name ?? this.name,
      phone: phone ?? this.phone,
      decorateType: decorateType ?? this.decorateType,
      city: city ?? this.city,
      region: region ?? this.region,
    );
  }

  /// 便于列表解析
  static List<TenderModel> listFromJson(List<dynamic>? list) {
    return BaseModel.fromJsonList<TenderModel>(
      list,
      (json) => TenderModel.fromJson(json),
    );
  }
}
