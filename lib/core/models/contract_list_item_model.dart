import 'base_model.dart';

/// 合同列表项模型，对应接口 `/api/furnish/logs/contract/list` 的返回元素
class ContractListItemModel extends BaseModel {
  final String? contractId;
  final String? contractTypeDisPlay;
  final String? packageName;
  final String? contractNo;

  ContractListItemModel({
    this.contractId,
    this.contractTypeDisPlay,
    this.packageName,
    this.contractNo,
  });

  factory ContractListItemModel.fromJson(Map<String, dynamic> json) {
    return ContractListItemModel(
      contractId: BaseModel.safeString(json, 'contractId'),
      contractTypeDisPlay: BaseModel.safeString(json, 'contractTypeDisPlay'),
      packageName: BaseModel.safeString(json, 'packageName'),
      contractNo: BaseModel.safeString(json, 'contractNo'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'contractId': contractId,
      'contractTypeDisPlay': contractTypeDisPlay,
      'packageName': packageName,
      'contractNo': contractNo,
    };
  }

  @override
  ContractListItemModel copyWith({
    String? contractId,
    String? contractTypeDisPlay,
    String? packageName,
    String? contractNo,
  }) {
    return ContractListItemModel(
      contractId: contractId ?? this.contractId,
      contractTypeDisPlay: contractTypeDisPlay ?? this.contractTypeDisPlay,
      packageName: packageName ?? this.packageName,
      contractNo: contractNo ?? this.contractNo,
    );
  }

  /// 工具方法：将列表 JSON 转换为模型列表
  static List<ContractListItemModel> listFromJson(List<dynamic>? list) {
    return BaseModel.fromJsonList<ContractListItemModel>(
      list,
      (json) => ContractListItemModel.fromJson(json),
    );
  }
}
