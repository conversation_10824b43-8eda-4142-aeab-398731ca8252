import 'base_model.dart';

/// 合同详情模型，用于替代 Map 结构的 `_contractDetail`
class ContractDetailModel extends BaseModel {
  final ContractInfo contractInfo;
  final List<PayItem> payList;

  ContractDetailModel({
    required this.contractInfo,
    required this.payList,
  });

  factory ContractDetailModel.fromJson(Map<String, dynamic> json) {
    return ContractDetailModel(
      contractInfo: ContractInfo.fromJson(
        BaseModel.safeMap(json, 'contractInfo') ?? <String, dynamic>{},
      ),
      payList: (json['payList'] as List<dynamic>? ?? [])
          .whereType<Map<String, dynamic>>()
          .map(PayItem.fromJson)
          .toList(),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'contractInfo': contractInfo.toJson(),
      'payList': payList.map((e) => e.toJson()).toList(),
    };
  }

  @override
  ContractDetailModel copyWith({
    ContractInfo? contractInfo,
    List<PayItem>? payList,
  }) {
    return ContractDetailModel(
      contractInfo: contractInfo ?? this.contractInfo,
      payList: payList ?? this.payList,
    );
  }
}

/// 合同基本信息
class ContractInfo extends BaseModel {
  final String? contractTypeDisPlay;
  final String? packageName;
  final String? contractNo;
  final double? directPrice;
  final double? taxPrice;
  final double? managePrice;
  final double? discountPrice;
  final double? contractPrice;

  ContractInfo({
    this.contractTypeDisPlay,
    this.packageName,
    this.contractNo,
    this.directPrice,
    this.taxPrice,
    this.managePrice,
    this.discountPrice,
    this.contractPrice,
  });

  factory ContractInfo.fromJson(Map<String, dynamic> json) {
    return ContractInfo(
      contractTypeDisPlay: BaseModel.safeString(json, 'contractTypeDisPlay'),
      packageName: BaseModel.safeString(json, 'packageName'),
      contractNo: BaseModel.safeString(json, 'contractNo'),
      directPrice: BaseModel.safeDouble(json, 'directPrice'),
      taxPrice: BaseModel.safeDouble(json, 'taxPrice'),
      managePrice: BaseModel.safeDouble(json, 'managePrice'),
      discountPrice: BaseModel.safeDouble(json, 'discountPrice'),
      contractPrice: BaseModel.safeDouble(json, 'contractPrice'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'contractTypeDisPlay': contractTypeDisPlay,
      'packageName': packageName,
      'contractNo': contractNo,
      'directPrice': directPrice,
      'taxPrice': taxPrice,
      'managePrice': managePrice,
      'discountPrice': discountPrice,
      'contractPrice': contractPrice,
    };
  }

  @override
  ContractInfo copyWith({
    String? contractTypeDisPlay,
    String? packageName,
    String? contractNo,
    double? directPrice,
    double? taxPrice,
    double? managePrice,
    double? discountPrice,
    double? contractPrice,
  }) {
    return ContractInfo(
      contractTypeDisPlay: contractTypeDisPlay ?? this.contractTypeDisPlay,
      packageName: packageName ?? this.packageName,
      contractNo: contractNo ?? this.contractNo,
      directPrice: directPrice ?? this.directPrice,
      taxPrice: taxPrice ?? this.taxPrice,
      managePrice: managePrice ?? this.managePrice,
      discountPrice: discountPrice ?? this.discountPrice,
      contractPrice: contractPrice ?? this.contractPrice,
    );
  }
}

/// 分期付款信息
class PayItem extends BaseModel {
  final double? price;
  final String? type;

  PayItem({
    this.price,
    this.type,
  });

  factory PayItem.fromJson(Map<String, dynamic> json) {
    return PayItem(
      price: BaseModel.safeDouble(json, 'price'),
      type: BaseModel.safeString(json, 'type'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'price': price,
      'type': type,
    };
  }

  @override
  PayItem copyWith({
    double? price,
    String? type,
  }) {
    return PayItem(
      price: price ?? this.price,
      type: type ?? this.type,
    );
  }
}
