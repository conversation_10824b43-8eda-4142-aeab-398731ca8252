import 'base_model.dart';

class ProductItem extends BaseModel {
  String id;
  String imageUrl;
  String title;
  String? shopIcon;
  String shop;
  double price;
  String points;

  ProductItem({
    required this.id,
    required this.imageUrl,
    required this.title,
    this.shopIcon,
    required this.shop,
    required this.price,
    required this.points,
  });

  // 从 JSON 创建 ProductItem 实例
  factory ProductItem.fromJson(Map<String, dynamic> json) {
    return ProductItem(
      id: BaseModel.safeString(json, 'id') ?? '',
      imageUrl: BaseModel.safeString(json, 'mainPic') ??
          BaseModel.safeString(json, 'picUrls') ??
          '',
      title: BaseModel.safeString(json, 'name') ?? '',
      shopIcon: BaseModel.safeString(json, 'businessLogo'),
      shop: BaseModel.safeString(json, 'businessName') ?? '',
      price: BaseModel.safeDouble(json, 'minPrice') ??
          BaseModel.safeDouble(json, 'salesPrice') ??
          0.0,
      points: BaseModel.safeString(json, 'minPointPrice') ??
          BaseModel.safeString(json, 'pointPrice') ??
          '0',
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'mainPic': imageUrl,
      'name': title,
      'businessLogo': shopIcon,
      'businessName': shop,
      'minPrice': price,
      'minPointPrice': points,
    };
  }

  @override
  ProductItem copyWith({
    String? id,
    String? imageUrl,
    String? title,
    String? shopIcon,
    String? shop,
    double? price,
    String? points,
  }) {
    return ProductItem(
      id: id ?? this.id,
      imageUrl: imageUrl ?? this.imageUrl,
      title: title ?? this.title,
      shopIcon: shopIcon ?? this.shopIcon,
      shop: shop ?? this.shop,
      price: price ?? this.price,
      points: points ?? this.points,
    );
  }
}
