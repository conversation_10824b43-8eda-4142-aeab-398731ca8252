class DecorationParamModel {
  final String? roomTypeDisplay;
  final dynamic landArea;
  final dynamic wallArea;
  final dynamic perimeter;
  final dynamic floorHeight;

  DecorationParamModel({
    this.roomTypeDisplay,
    this.landArea,
    this.wallArea,
    this.perimeter,
    this.floorHeight,
  });

  factory DecorationParamModel.fromJson(Map<String, dynamic> json) {
    return DecorationParamModel(
      roomTypeDisplay: json['roomTypeDisplay']?.toString(),
      landArea: json['landArea'],
      wallArea: json['wallArea'],
      perimeter: json['perimeter'],
      floorHeight: json['floorHeight'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'roomTypeDisplay': roomTypeDisplay,
      'landArea': landArea,
      'wallArea': wallArea,
      'perimeter': perimeter,
      'floorHeight': floorHeight,
    };
  }
}
