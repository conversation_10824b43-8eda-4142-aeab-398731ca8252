/// 项目清单中的单个材料/工项模型
class DecorationProjectItemModel {
  final String? materialPic;
  final String? materialName;
  final String? sku;
  final String? unit;
  final String? brandName;
  final num? number;

  DecorationProjectItemModel({
    this.materialPic,
    this.materialName,
    this.sku,
    this.unit,
    this.brandName,
    this.number,
  });

  factory DecorationProjectItemModel.fromJson(Map<String, dynamic> json) {
    return DecorationProjectItemModel(
      materialPic: json['materialPic']?.toString(),
      materialName: json['materialName']?.toString(),
      sku: json['sku']?.toString(),
      unit: json['unit']?.toString(),
      brandName: json['brandName']?.toString(),
      number: json['number'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'materialPic': materialPic,
      'materialName': materialName,
      'sku': sku,
      'unit': unit,
      'brandName': brandName,
      'number': number,
    };
  }
}

/// 每个房间（或类型）对应的项目清单模型
class DecorationProjectRoomModel {
  final String? roomName;
  final num? landArea;
  final List<DecorationProjectItemModel> rows;

  DecorationProjectRoomModel({
    this.roomName,
    this.landArea,
    required this.rows,
  });

  factory DecorationProjectRoomModel.fromJson(Map<String, dynamic> json) {
    return DecorationProjectRoomModel(
      roomName: json['roomName']?.toString(),
      landArea: json['landArea'],
      rows: (json['rows'] as List<dynamic>? ?? [])
          .map((e) =>
              DecorationProjectItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'roomName': roomName,
      'landArea': landArea,
      'rows': rows.map((e) => e.toJson()).toList(),
    };
  }
}
