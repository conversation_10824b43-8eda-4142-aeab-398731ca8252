import 'base_model.dart';

/// 我的项目列表-单个项目模型
class CustomerProjectModel extends BaseModel {
  final String? projectId;
  final String? address;
  final int? bedroomNumber;
  final int? livingRoomNumber;
  final double? area;

  CustomerProjectModel({
    this.projectId,
    this.address,
    this.bedroomNumber,
    this.livingRoomNumber,
    this.area,
  });

  factory CustomerProjectModel.fromJson(Map<String, dynamic> json) {
    return CustomerProjectModel(
      projectId: BaseModel.safeString(json, 'projectId'),
      address: BaseModel.safeString(json, 'address'),
      bedroomNumber: BaseModel.safeInt(json, 'bedroomNumber'),
      livingRoomNumber: BaseModel.safeInt(json, 'livingRoomNumber'),
      area: BaseModel.safeDouble(json, 'area'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'projectId': projectId,
      'address': address,
      'bedroomNumber': bedroomNumber,
      'livingRoomNumber': livingRoomNumber,
      'area': area,
    };
  }

  @override
  CustomerProjectModel copyWith({
    String? projectId,
    String? address,
    int? bedroomNumber,
    int? livingRoomNumber,
    double? area,
  }) {
    return CustomerProjectModel(
      projectId: projectId ?? this.projectId,
      address: address ?? this.address,
      bedroomNumber: bedroomNumber ?? this.bedroomNumber,
      livingRoomNumber: livingRoomNumber ?? this.livingRoomNumber,
      area: area ?? this.area,
    );
  }
} 