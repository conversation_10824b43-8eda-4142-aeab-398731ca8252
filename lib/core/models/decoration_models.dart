import 'base_model.dart';

/// 装修类型枚举
enum RenovationType {
  fullRenovation("整装"),
  renovation("翻新"),
  softFurnishing("软装");

  final String label;
  const RenovationType(this.label);
}

/// 房间类型枚举
enum RoomType {
  bedroom("卧室"),
  livingRoom("客厅"),
  kitchen("厨房"),
  bathroom("卫生间"),
  wallRefresh("墙面刷新"),
  restaurant("餐厅");

  final String label;
  const RoomType(this.label);
}

/// 案例详情模型
class CaseDetail extends BaseModel {
  String? id;
  String? caseTitle; // 案例标题 (对应API字段)
  String? caseIntro; // 案例简介 (对应API字段)
  List<String>? caseMainPic; // 案例主图列表 (对应API字段)
  String? designerName;
  String? designerId;
  String? avatar; // 设计师头像 (对应API字段)
  List<String>? excelStyle; // 擅长风格列表 (对应API字段)
  List<String>? designStyle; // 设计风格列表 (对应API字段)
  String? area; // 建筑面积
  String? householdType; // 户型 (对应API字段)
  int? caseNumber; // 案例数量 (对应API字段)
  String? location;
  String? createTime;
  String? caseInfo; // 案例信息HTML (对应API字段)
  int? viewCount;
  int? likeCount;
  bool? isLiked;
  bool? isCollected;

  CaseDetail({
    this.id,
    this.caseTitle,
    this.caseIntro,
    this.caseMainPic,
    this.designerName,
    this.designerId,
    this.avatar,
    this.excelStyle,
    this.designStyle,
    this.area,
    this.householdType,
    this.caseNumber,
    this.location,
    this.createTime,
    this.caseInfo,
    this.viewCount,
    this.likeCount,
    this.isLiked,
    this.isCollected,
  });

  factory CaseDetail.fromJson(Map<String, dynamic> json) {
    return CaseDetail(
      id: BaseModel.safeString(json, 'id'),
      caseTitle: BaseModel.safeString(json, 'caseTitle'),
      caseIntro: BaseModel.safeString(json, 'caseIntro'),
      caseMainPic: BaseModel.safeStringList(json, 'caseMainPic'),
      designerName: BaseModel.safeString(json, 'designerName'),
      designerId: BaseModel.safeString(json, 'designerId'),
      avatar: BaseModel.safeString(json, 'avatar'),
      excelStyle: BaseModel.safeStringList(json, 'excelStyle'),
      designStyle: BaseModel.safeStringList(json, 'designStyle'),
      area: BaseModel.safeString(json, 'area'),
      householdType: BaseModel.safeString(json, 'householdType'),
      caseNumber: BaseModel.safeInt(json, 'caseNumber'),
      location: BaseModel.safeString(json, 'location'),
      createTime: BaseModel.safeString(json, 'createTime'),
      caseInfo: BaseModel.safeString(json, 'caseInfo'),
      viewCount: BaseModel.safeInt(json, 'viewCount'),
      likeCount: BaseModel.safeInt(json, 'likeCount'),
      isLiked: BaseModel.safeBool(json, 'isLiked'),
      isCollected: BaseModel.safeBool(json, 'isCollected'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'caseTitle': caseTitle,
      'caseIntro': caseIntro,
      'caseMainPic': caseMainPic,
      'designerName': designerName,
      'designerId': designerId,
      'avatar': avatar,
      'excelStyle': excelStyle,
      'designStyle': designStyle,
      'area': area,
      'householdType': householdType,
      'caseNumber': caseNumber,
      'location': location,
      'createTime': createTime,
      'caseInfo': caseInfo,
      'viewCount': viewCount,
      'likeCount': likeCount,
      'isLiked': isLiked,
      'isCollected': isCollected,
    };
  }

  @override
  CaseDetail copyWith({
    String? id,
    String? caseTitle,
    String? caseIntro,
    List<String>? caseMainPic,
    String? designerName,
    String? designerId,
    String? avatar,
    List<String>? excelStyle,
    List<String>? designStyle,
    String? area,
    String? householdType,
    int? caseNumber,
    String? location,
    String? createTime,
    String? caseInfo,
    int? viewCount,
    int? likeCount,
    bool? isLiked,
    bool? isCollected,
  }) {
    return CaseDetail(
      id: id ?? this.id,
      caseTitle: caseTitle ?? this.caseTitle,
      caseIntro: caseIntro ?? this.caseIntro,
      caseMainPic: caseMainPic ?? this.caseMainPic,
      designerName: designerName ?? this.designerName,
      designerId: designerId ?? this.designerId,
      avatar: avatar ?? this.avatar,
      excelStyle: excelStyle ?? this.excelStyle,
      designStyle: designStyle ?? this.designStyle,
      area: area ?? this.area,
      householdType: householdType ?? this.householdType,
      caseNumber: caseNumber ?? this.caseNumber,
      location: location ?? this.location,
      createTime: createTime ?? this.createTime,
      caseInfo: caseInfo ?? this.caseInfo,
      viewCount: viewCount ?? this.viewCount,
      likeCount: likeCount ?? this.likeCount,
      isLiked: isLiked ?? this.isLiked,
      isCollected: isCollected ?? this.isCollected,
    );
  }
}

/// 报价详情模型
class QuoteDetail extends BaseModel {
  String? packageId;
  String? packageName;
  String? packageType;
  double? totalPrice;
  double? area;
  int? bedroomCount;
  int? livingRoomCount;
  int? bathroomCount;
  int? kitchenCount;
  List<QuoteItem>? items;
  String? description;
  List<String>? images;

  QuoteDetail({
    this.packageId,
    this.packageName,
    this.packageType,
    this.totalPrice,
    this.area,
    this.bedroomCount,
    this.livingRoomCount,
    this.bathroomCount,
    this.kitchenCount,
    this.items,
    this.description,
    this.images,
  });

  factory QuoteDetail.fromJson(Map<String, dynamic> json) {
    return QuoteDetail(
      packageId: BaseModel.safeString(json, 'packageId'),
      packageName: BaseModel.safeString(json, 'packageName'),
      packageType: BaseModel.safeString(json, 'packageType'),
      totalPrice: BaseModel.safeDouble(json, 'totalPrice'),
      area: BaseModel.safeDouble(json, 'area'),
      bedroomCount: BaseModel.safeInt(json, 'bedroomCount'),
      livingRoomCount: BaseModel.safeInt(json, 'livingRoomCount'),
      bathroomCount: BaseModel.safeInt(json, 'bathroomCount'),
      kitchenCount: BaseModel.safeInt(json, 'kitchenCount'),
      items: BaseModel.fromJsonList(
        BaseModel.safeList(json, 'items'),
        QuoteItem.fromJson,
      ),
      description: BaseModel.safeString(json, 'description'),
      images: BaseModel.safeStringList(json, 'images'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'packageId': packageId,
      'packageName': packageName,
      'packageType': packageType,
      'totalPrice': totalPrice,
      'area': area,
      'bedroomCount': bedroomCount,
      'livingRoomCount': livingRoomCount,
      'bathroomCount': bathroomCount,
      'kitchenCount': kitchenCount,
      'items': items?.map((e) => e.toJson()).toList(),
      'description': description,
      'images': images,
    };
  }

  @override
  QuoteDetail copyWith({
    String? packageId,
    String? packageName,
    String? packageType,
    double? totalPrice,
    double? area,
    int? bedroomCount,
    int? livingRoomCount,
    int? bathroomCount,
    int? kitchenCount,
    List<QuoteItem>? items,
    String? description,
    List<String>? images,
  }) {
    return QuoteDetail(
      packageId: packageId ?? this.packageId,
      packageName: packageName ?? this.packageName,
      packageType: packageType ?? this.packageType,
      totalPrice: totalPrice ?? this.totalPrice,
      area: area ?? this.area,
      bedroomCount: bedroomCount ?? this.bedroomCount,
      livingRoomCount: livingRoomCount ?? this.livingRoomCount,
      bathroomCount: bathroomCount ?? this.bathroomCount,
      kitchenCount: kitchenCount ?? this.kitchenCount,
      items: items ?? this.items,
      description: description ?? this.description,
      images: images ?? this.images,
    );
  }
}

/// 报价项目模型
class QuoteItem extends BaseModel {
  String? id;
  String? name;
  String? category;
  String? unit;
  double? quantity;
  double? unitPrice;
  double? totalPrice;
  String? description;
  String? image;

  QuoteItem({
    this.id,
    this.name,
    this.category,
    this.unit,
    this.quantity,
    this.unitPrice,
    this.totalPrice,
    this.description,
    this.image,
  });

  factory QuoteItem.fromJson(Map<String, dynamic> json) {
    return QuoteItem(
      id: BaseModel.safeString(json, 'id'),
      name: BaseModel.safeString(json, 'name'),
      category: BaseModel.safeString(json, 'category'),
      unit: BaseModel.safeString(json, 'unit'),
      quantity: BaseModel.safeDouble(json, 'quantity'),
      unitPrice: BaseModel.safeDouble(json, 'unitPrice'),
      totalPrice: BaseModel.safeDouble(json, 'totalPrice'),
      description: BaseModel.safeString(json, 'description'),
      image: BaseModel.safeString(json, 'image'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'unit': unit,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
      'description': description,
      'image': image,
    };
  }

  @override
  QuoteItem copyWith({
    String? id,
    String? name,
    String? category,
    String? unit,
    double? quantity,
    double? unitPrice,
    double? totalPrice,
    String? description,
    String? image,
  }) {
    return QuoteItem(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      unit: unit ?? this.unit,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      description: description ?? this.description,
      image: image ?? this.image,
    );
  }
}

/// 装修日志合同详情模型
class ContractDetail extends BaseModel {
  String? contractId;
  String? contractNumber;
  String? contractName;
  String? contractStatus;
  String? contractStatusName;
  String? projectName;
  String? projectAddress;
  double? contractAmount;
  String? startDate;
  String? endDate;
  String? createTime;
  String? signTime;
  List<ContractItem>? items;
  String? description;
  List<String>? attachments;

  ContractDetail({
    this.contractId,
    this.contractNumber,
    this.contractName,
    this.contractStatus,
    this.contractStatusName,
    this.projectName,
    this.projectAddress,
    this.contractAmount,
    this.startDate,
    this.endDate,
    this.createTime,
    this.signTime,
    this.items,
    this.description,
    this.attachments,
  });

  factory ContractDetail.fromJson(Map<String, dynamic> json) {
    return ContractDetail(
      contractId: BaseModel.safeString(json, 'contractId'),
      contractNumber: BaseModel.safeString(json, 'contractNumber'),
      contractName: BaseModel.safeString(json, 'contractName'),
      contractStatus: BaseModel.safeString(json, 'contractStatus'),
      contractStatusName: BaseModel.safeString(json, 'contractStatusName'),
      projectName: BaseModel.safeString(json, 'projectName'),
      projectAddress: BaseModel.safeString(json, 'projectAddress'),
      contractAmount: BaseModel.safeDouble(json, 'contractAmount'),
      startDate: BaseModel.safeString(json, 'startDate'),
      endDate: BaseModel.safeString(json, 'endDate'),
      createTime: BaseModel.safeString(json, 'createTime'),
      signTime: BaseModel.safeString(json, 'signTime'),
      items: BaseModel.fromJsonList(
        BaseModel.safeList(json, 'items'),
        ContractItem.fromJson,
      ),
      description: BaseModel.safeString(json, 'description'),
      attachments: BaseModel.safeStringList(json, 'attachments'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'contractId': contractId,
      'contractNumber': contractNumber,
      'contractName': contractName,
      'contractStatus': contractStatus,
      'contractStatusName': contractStatusName,
      'projectName': projectName,
      'projectAddress': projectAddress,
      'contractAmount': contractAmount,
      'startDate': startDate,
      'endDate': endDate,
      'createTime': createTime,
      'signTime': signTime,
      'items': items?.map((e) => e.toJson()).toList(),
      'description': description,
      'attachments': attachments,
    };
  }

  @override
  ContractDetail copyWith({
    String? contractId,
    String? contractNumber,
    String? contractName,
    String? contractStatus,
    String? contractStatusName,
    String? projectName,
    String? projectAddress,
    double? contractAmount,
    String? startDate,
    String? endDate,
    String? createTime,
    String? signTime,
    List<ContractItem>? items,
    String? description,
    List<String>? attachments,
  }) {
    return ContractDetail(
      contractId: contractId ?? this.contractId,
      contractNumber: contractNumber ?? this.contractNumber,
      contractName: contractName ?? this.contractName,
      contractStatus: contractStatus ?? this.contractStatus,
      contractStatusName: contractStatusName ?? this.contractStatusName,
      projectName: projectName ?? this.projectName,
      projectAddress: projectAddress ?? this.projectAddress,
      contractAmount: contractAmount ?? this.contractAmount,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      createTime: createTime ?? this.createTime,
      signTime: signTime ?? this.signTime,
      items: items ?? this.items,
      description: description ?? this.description,
      attachments: attachments ?? this.attachments,
    );
  }
}

/// 合同项目模型
class ContractItem extends BaseModel {
  String? id;
  String? name;
  String? category;
  String? specification;
  String? unit;
  double? quantity;
  double? unitPrice;
  double? totalPrice;
  String? remark;

  ContractItem({
    this.id,
    this.name,
    this.category,
    this.specification,
    this.unit,
    this.quantity,
    this.unitPrice,
    this.totalPrice,
    this.remark,
  });

  factory ContractItem.fromJson(Map<String, dynamic> json) {
    return ContractItem(
      id: BaseModel.safeString(json, 'id'),
      name: BaseModel.safeString(json, 'name'),
      category: BaseModel.safeString(json, 'category'),
      specification: BaseModel.safeString(json, 'specification'),
      unit: BaseModel.safeString(json, 'unit'),
      quantity: BaseModel.safeDouble(json, 'quantity'),
      unitPrice: BaseModel.safeDouble(json, 'unitPrice'),
      totalPrice: BaseModel.safeDouble(json, 'totalPrice'),
      remark: BaseModel.safeString(json, 'remark'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'specification': specification,
      'unit': unit,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
      'remark': remark,
    };
  }

  @override
  ContractItem copyWith({
    String? id,
    String? name,
    String? category,
    String? specification,
    String? unit,
    double? quantity,
    double? unitPrice,
    double? totalPrice,
    String? remark,
  }) {
    return ContractItem(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      specification: specification ?? this.specification,
      unit: unit ?? this.unit,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      remark: remark ?? this.remark,
    );
  }
}

/// 设计师信息模型
class DesignerInfo extends BaseModel {
  String? id;
  String? userId;
  String? realName; // 真实姓名 (对应API字段)
  String? avatar; // 头像 (对应API字段)
  String? personalProfile; // 个人简介 (对应API字段)
  String? personalBackground; // 个人背景图 (对应API字段)
  List<String>? excelStyle; // 擅长风格列表 (对应API字段)
  int? workingYears; // 从业年限 (对应API字段)
  String? workExperience; // 工作经验
  String? education; // 教育背景
  String? certification; // 认证信息
  int? caseCount; // 案例数量
  int? followCount; // 关注数量
  double? rating; // 评分
  String? city; // 所在城市
  String? createTime;
  String? updateTime;

  DesignerInfo({
    this.id,
    this.userId,
    this.realName,
    this.avatar,
    this.personalProfile,
    this.personalBackground,
    this.excelStyle,
    this.workingYears,
    this.workExperience,
    this.education,
    this.certification,
    this.caseCount,
    this.followCount,
    this.rating,
    this.city,
    this.createTime,
    this.updateTime,
  });

  factory DesignerInfo.fromJson(Map<String, dynamic> json) {
    return DesignerInfo(
      id: BaseModel.safeString(json, 'id'),
      userId: BaseModel.safeString(json, 'userId'),
      realName: BaseModel.safeString(json, 'realName'),
      avatar: BaseModel.safeString(json, 'avatar'),
      personalProfile: BaseModel.safeString(json, 'personalProfile'),
      personalBackground: BaseModel.safeString(json, 'personalBackground'),
      excelStyle: BaseModel.safeStringList(json, 'excelStyle'),
      workingYears: BaseModel.safeInt(json, 'workingYears'),
      workExperience: BaseModel.safeString(json, 'workExperience'),
      education: BaseModel.safeString(json, 'education'),
      certification: BaseModel.safeString(json, 'certification'),
      caseCount: BaseModel.safeInt(json, 'caseCount'),
      followCount: BaseModel.safeInt(json, 'followCount'),
      rating: BaseModel.safeDouble(json, 'rating'),
      city: BaseModel.safeString(json, 'city'),
      createTime: BaseModel.safeString(json, 'createTime'),
      updateTime: BaseModel.safeString(json, 'updateTime'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'realName': realName,
      'avatar': avatar,
      'personalProfile': personalProfile,
      'personalBackground': personalBackground,
      'excelStyle': excelStyle,
      'workingYears': workingYears,
      'workExperience': workExperience,
      'education': education,
      'certification': certification,
      'caseCount': caseCount,
      'followCount': followCount,
      'rating': rating,
      'city': city,
      'createTime': createTime,
      'updateTime': updateTime,
    };
  }

  @override
  DesignerInfo copyWith({
    String? id,
    String? userId,
    String? realName,
    String? avatar,
    String? personalProfile,
    String? personalBackground,
    List<String>? excelStyle,
    int? workingYears,
    String? workExperience,
    String? education,
    String? certification,
    int? caseCount,
    int? followCount,
    double? rating,
    String? city,
    String? createTime,
    String? updateTime,
  }) {
    return DesignerInfo(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      realName: realName ?? this.realName,
      avatar: avatar ?? this.avatar,
      personalProfile: personalProfile ?? this.personalProfile,
      personalBackground: personalBackground ?? this.personalBackground,
      excelStyle: excelStyle ?? this.excelStyle,
      workingYears: workingYears ?? this.workingYears,
      workExperience: workExperience ?? this.workExperience,
      education: education ?? this.education,
      certification: certification ?? this.certification,
      caseCount: caseCount ?? this.caseCount,
      followCount: followCount ?? this.followCount,
      rating: rating ?? this.rating,
      city: city ?? this.city,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  /// 获取完整的风格文本
  String get styleText {
    return excelStyle?.join('、') ?? '';
  }
}

/// 搜索结果项目模型
class SearchResultItem extends BaseModel {
  String? id;
  String? name;
  String? title;
  String? imageUrl;
  String? description;
  int? type; // 搜索类型：1-设计师，2-案例，3-商品，4-产品，6-店铺
  Map<String, dynamic>? extraData; // 存储不同类型的额外数据

  SearchResultItem({
    this.id,
    this.name,
    this.title,
    this.imageUrl,
    this.description,
    this.type,
    this.extraData,
  });

  factory SearchResultItem.fromJson(Map<String, dynamic> json) {
    return SearchResultItem(
      id: BaseModel.safeString(json, 'id'),
      name: BaseModel.safeString(json, 'name'),
      title: BaseModel.safeString(json, 'title'),
      imageUrl: BaseModel.safeString(json, 'imageUrl'),
      description: BaseModel.safeString(json, 'description'),
      type: BaseModel.safeInt(json, 'type'),
      extraData: json, // 保存完整数据
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return extraData ??
        {
          'id': id,
          'name': name,
          'title': title,
          'imageUrl': imageUrl,
          'description': description,
          'type': type,
        };
  }

  @override
  SearchResultItem copyWith({
    String? id,
    String? name,
    String? title,
    String? imageUrl,
    String? description,
    int? type,
    Map<String, dynamic>? extraData,
  }) {
    return SearchResultItem(
      id: id ?? this.id,
      name: name ?? this.name,
      title: title ?? this.title,
      imageUrl: imageUrl ?? this.imageUrl,
      description: description ?? this.description,
      type: type ?? this.type,
      extraData: extraData ?? this.extraData,
    );
  }
}
