import 'base_model.dart';

/// 签名服务模型
class SignatureModel extends BaseModel {
  String? serviceId;
  String? sourceId;
  String? customerName;
  String? customerPhone;
  String? projectAddress;
  String? serviceStatus;
  String? serviceStatusDisplay;
  String? decorateType;
  String? decorateTypeDisplay;
  int? area;
  String? roomType;
  String? roomTypeDisplay;
  String? buttJointStatus;
  String? channelMeet;

  SignatureModel({
    this.serviceId,
    this.sourceId,
    this.customerName,
    this.customerPhone,
    this.projectAddress,
    this.serviceStatus,
    this.serviceStatusDisplay,
    this.decorateType,
    this.decorateTypeDisplay,
    this.area,
    this.roomType,
    this.roomTypeDisplay,
    this.buttJointStatus,
    this.channelMeet,
  });

  /// 使用 [json] 创建 [SignatureModel] 实例
  factory SignatureModel.fromJson(Map<String, dynamic> json) {
    return SignatureModel(
      serviceId: BaseModel.safeString(json, 'serviceId'),
      sourceId: BaseModel.safeString(json, 'sourceId'),
      customerName: BaseModel.safeString(json, 'customerName'),
      customerPhone: BaseModel.safeString(json, 'customerPhone'),
      projectAddress: BaseModel.safeString(json, 'projectAddress'),
      serviceStatus: BaseModel.safeString(json, 'serviceStatus'),
      serviceStatusDisplay: BaseModel.safeString(json, 'serviceStatusDisplay'),
      decorateType: BaseModel.safeString(json, 'decorateType'),
      decorateTypeDisplay: BaseModel.safeString(json, 'decorateTypeDisplay'),
      area: BaseModel.safeInt(json, 'area'),
      roomType: BaseModel.safeString(json, 'roomType'),
      roomTypeDisplay: BaseModel.safeString(json, 'roomTypeDisplay'),
      buttJointStatus: BaseModel.safeString(json, 'buttJointStatus'),
      channelMeet: BaseModel.safeString(json, 'channelMeet'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'serviceId': serviceId,
      'sourceId': sourceId,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'projectAddress': projectAddress,
      'serviceStatus': serviceStatus,
      'serviceStatusDisplay': serviceStatusDisplay,
      'decorateType': decorateType,
      'decorateTypeDisplay': decorateTypeDisplay,
      'area': area,
      'roomType': roomType,
      'roomTypeDisplay': roomTypeDisplay,
      'buttJointStatus': buttJointStatus,
      'channelMeet': channelMeet,
    };
  }

  @override
  SignatureModel copyWith({
    String? serviceId,
    String? sourceId,
    String? customerName,
    String? customerPhone,
    String? projectAddress,
    String? serviceStatus,
    String? serviceStatusDisplay,
    String? decorateType,
    String? decorateTypeDisplay,
    int? area,
    String? roomType,
    String? roomTypeDisplay,
    String? buttJointStatus,
    String? channelMeet,
  }) {
    return SignatureModel(
      serviceId: serviceId ?? this.serviceId,
      sourceId: sourceId ?? this.sourceId,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      projectAddress: projectAddress ?? this.projectAddress,
      serviceStatus: serviceStatus ?? this.serviceStatus,
      serviceStatusDisplay: serviceStatusDisplay ?? this.serviceStatusDisplay,
      decorateType: decorateType ?? this.decorateType,
      decorateTypeDisplay: decorateTypeDisplay ?? this.decorateTypeDisplay,
      area: area ?? this.area,
      roomType: roomType ?? this.roomType,
      roomTypeDisplay: roomTypeDisplay ?? this.roomTypeDisplay,
      buttJointStatus: buttJointStatus ?? this.buttJointStatus,
      channelMeet: channelMeet ?? this.channelMeet,
    );
  }

  /// 获取脱敏后的手机号
  String get maskedPhone {
    if (customerPhone == null || customerPhone!.length < 11) {
      return customerPhone ?? '';
    }
    return '${customerPhone!.substring(0, 3)} **** ${customerPhone!.substring(7)}';
  }

  /// 获取脱敏后的地址
  String get maskedAddress {
    if (projectAddress == null || projectAddress!.length < 8) {
      return projectAddress ?? '';
    }
    return '${projectAddress!.substring(0, projectAddress!.length - 4)}****';
  }
}

/// 签名服务响应模型
class SignatureResponseModel extends BaseModel {
  List<SignatureModel>? rows;
  int? total;
  int? pageNum;
  int? pageSize;
  int? pageTotal;

  SignatureResponseModel({
    this.rows,
    this.total,
    this.pageNum,
    this.pageSize,
    this.pageTotal,
  });

  /// 使用 [json] 创建 [SignatureResponseModel] 实例
  factory SignatureResponseModel.fromJson(Map<String, dynamic> json) {
    return SignatureResponseModel(
      rows: BaseModel.safeList(json, 'rows')
          ?.map((e) => SignatureModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: BaseModel.safeInt(json, 'total'),
      pageNum: BaseModel.safeInt(json, 'pageNum'),
      pageSize: BaseModel.safeInt(json, 'pageSize'),
      pageTotal: BaseModel.safeInt(json, 'pageTotal'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'rows': rows?.map((e) => e.toJson()).toList(),
      'total': total,
      'pageNum': pageNum,
      'pageSize': pageSize,
      'pageTotal': pageTotal,
    };
  }

  @override
  SignatureResponseModel copyWith({
    List<SignatureModel>? rows,
    int? total,
    int? pageNum,
    int? pageSize,
    int? pageTotal,
  }) {
    return SignatureResponseModel(
      rows: rows ?? this.rows,
      total: total ?? this.total,
      pageNum: pageNum ?? this.pageNum,
      pageSize: pageSize ?? this.pageSize,
      pageTotal: pageTotal ?? this.pageTotal,
    );
  }
}
