import 'base_model.dart';

/// 设计师案例模型
class DesignerCaseModel extends BaseModel {
  final String id; // 案例ID
  final String? caseTitle; // 案例标题
  final List<String> caseMainPic; // 案例主图列表
  final List<String> excelStyle; // 风格标签列表
  final String? householdType; // 户型
  final String? caseIntro; // 案例简介

  DesignerCaseModel({
    required this.id,
    this.caseTitle,
    required this.caseMainPic,
    required this.excelStyle,
    this.householdType,
    this.caseIntro,
  });

  factory DesignerCaseModel.fromJson(Map<String, dynamic> json) {
    return DesignerCaseModel(
      id: BaseModel.safeString(json, 'id') ?? '',
      caseTitle: BaseModel.safeString(json, 'caseTitle'),
      caseMainPic: BaseModel.safeStringList(json, 'caseMainPic') ?? [],
      excelStyle: BaseModel.safeStringList(json, 'excelStyle') ?? [],
      householdType: BaseModel.safeString(json, 'householdType'),
      caseIntro: BaseModel.safeString(json, 'caseIntro'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'caseTitle': caseTitle,
      'caseMainPic': caseMainPic,
      'excelStyle': excelStyle,
      'householdType': householdType,
      'caseIntro': caseIntro,
    };
  }

  @override
  DesignerCaseModel copyWith({
    String? id,
    String? caseTitle,
    List<String>? caseMainPic,
    List<String>? excelStyle,
    String? householdType,
    String? caseIntro,
  }) {
    return DesignerCaseModel(
      id: id ?? this.id,
      caseTitle: caseTitle ?? this.caseTitle,
      caseMainPic: caseMainPic ?? this.caseMainPic,
      excelStyle: excelStyle ?? this.excelStyle,
      householdType: householdType ?? this.householdType,
      caseIntro: caseIntro ?? this.caseIntro,
    );
  }

  /// 便于列表解析
  static List<DesignerCaseModel> listFromJson(List<dynamic>? list) {
    return BaseModel.fromJsonList<DesignerCaseModel>(
      list,
      (json) => DesignerCaseModel.fromJson(json),
    );
  }
} 