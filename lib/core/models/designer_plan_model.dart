import 'base_model.dart';

/// 设计师方案中的单张图片信息
class DesignerPhoto extends BaseModel {
  final String? url; // 图片地址
  final String? title; // 图片标题

  DesignerPhoto({
    this.url,
    this.title,
  });

  factory DesignerPhoto.fromJson(Map<String, dynamic> json) {
    return DesignerPhoto(
      url: BaseModel.safeString(json, 'url'),
      title: BaseModel.safeString(json, 'title'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'title': title,
    };
  }

  @override
  DesignerPhoto copyWith({
    String? url,
    String? title,
  }) {
    return DesignerPhoto(
      url: url ?? this.url,
      title: title ?? this.title,
    );
  }

  /// 便于列表解析
  static List<DesignerPhoto> listFromJson(List<dynamic>? list) {
    return BaseModel.fromJsonList<DesignerPhoto>(
      list,
      (json) => DesignerPhoto.fromJson(json),
    );
  }
}

/// 设计方案模型
class DesignerPlan extends BaseModel {
  final String? typeDisplay; // 方案类型展示文案
  final List<DesignerPhoto> row; // 该类型下的图片集合

  DesignerPlan({
    this.typeDisplay,
    required this.row,
  });

  factory DesignerPlan.fromJson(Map<String, dynamic> json) {
    return DesignerPlan(
      typeDisplay: BaseModel.safeString(json, 'typeDisplay'),
      row: DesignerPhoto.listFromJson(json['row'] as List<dynamic>?),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'typeDisplay': typeDisplay,
      'row': row.map((e) => e.toJson()).toList(),
    };
  }

  @override
  DesignerPlan copyWith({
    String? typeDisplay,
    List<DesignerPhoto>? row,
  }) {
    return DesignerPlan(
      typeDisplay: typeDisplay ?? this.typeDisplay,
      row: row ?? this.row,
    );
  }

  /// 便于列表解析
  static List<DesignerPlan> listFromJson(List<dynamic>? list) {
    return BaseModel.fromJsonList<DesignerPlan>(
      list,
      (json) => DesignerPlan.fromJson(json),
    );
  }
}
