import 'base_model.dart';

/// 地址模型
class AddressModel extends BaseModel {
  String? id;
  String? firstName; // 收货人姓名 (对应API字段)
  String? phoneNumber; // 手机号 (对应API字段)
  String? state; // 省 (对应API字段)
  String? city; // 市
  String? district; // 区
  String? detailedAddress; // 详细地址 (对应API字段)
  String? fullAddress; // 完整地址 (计算属性)
  bool? isDefault; // 是否默认地址
  String? createTime;
  String? updateTime;

  AddressModel({
    this.id,
    this.firstName,
    this.phoneNumber,
    this.state,
    this.city,
    this.district,
    this.detailedAddress,
    this.fullAddress,
    this.isDefault,
    this.createTime,
    this.updateTime,
  });

  factory AddressModel.fromJson(Map<String, dynamic> json) {
    // 处理 isDefault 字段，可能是字符串 "1"/"0" 或布尔值
    bool? defaultValue;
    final isDefaultRaw = json['isDefault'];
    if (isDefaultRaw != null) {
      if (isDefaultRaw is String) {
        defaultValue = isDefaultRaw == "1";
      } else if (isDefaultRaw is bool) {
        defaultValue = isDefaultRaw;
      } else if (isDefaultRaw is int) {
        defaultValue = isDefaultRaw == 1;
      }
    }

    return AddressModel(
      id: BaseModel.safeString(json, 'id'),
      firstName: BaseModel.safeString(json, 'firstName'),
      phoneNumber: BaseModel.safeString(json, 'phoneNumber'),
      state: BaseModel.safeString(json, 'state'),
      city: BaseModel.safeString(json, 'city'),
      district: BaseModel.safeString(json, 'district'),
      detailedAddress: BaseModel.safeString(json, 'detailedAddress'),
      fullAddress: BaseModel.safeString(json, 'fullAddress'),
      isDefault: defaultValue,
      createTime: BaseModel.safeString(json, 'createTime'),
      updateTime: BaseModel.safeString(json, 'updateTime'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'firstName': firstName,
      'phoneNumber': phoneNumber,
      'state': state,
      'city': city,
      'district': district,
      'detailedAddress': detailedAddress,
      'fullAddress': fullAddress,
      'isDefault': isDefault == true ? "1" : "0", // 转换为API期望的字符串格式
      'createTime': createTime,
      'updateTime': updateTime,
    };
  }

  @override
  AddressModel copyWith({
    String? id,
    String? firstName,
    String? phoneNumber,
    String? state,
    String? city,
    String? district,
    String? detailedAddress,
    String? fullAddress,
    bool? isDefault,
    String? createTime,
    String? updateTime,
  }) {
    return AddressModel(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      state: state ?? this.state,
      city: city ?? this.city,
      district: district ?? this.district,
      detailedAddress: detailedAddress ?? this.detailedAddress,
      fullAddress: fullAddress ?? this.fullAddress,
      isDefault: isDefault ?? this.isDefault,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  /// 获取完整地址字符串
  String get completeAddress {
    final parts = [state, city, district, detailedAddress]
        .where((part) => part != null && part.isNotEmpty)
        .toList();
    return parts.join(' ');
  }
}

/// 订单模型
class OrderModel extends BaseModel {
  String? orderNumber;
  String? orderStatus;
  String? orderStatusName;
  double? totalAmount;
  double? payAmount;
  double? discountAmount;
  String? payType;
  String? payTypeName;
  String? createTime;
  String? payTime;
  AddressModel? deliveryAddress;
  List<OrderItem>? orderItems;
  String? businessName;
  String? businessLogo;

  OrderModel({
    this.orderNumber,
    this.orderStatus,
    this.orderStatusName,
    this.totalAmount,
    this.payAmount,
    this.discountAmount,
    this.payType,
    this.payTypeName,
    this.createTime,
    this.payTime,
    this.deliveryAddress,
    this.orderItems,
    this.businessName,
    this.businessLogo,
  });

  factory OrderModel.fromJson(Map<String, dynamic> json) {
    // 解析订单商品信息
    List<OrderItem> items = [];
    final orderItemsData =
        BaseModel.safeList(json, 'customerCommodityShopOrderInfoItemRespVo');
    if (orderItemsData != null) {
      for (var item in orderItemsData) {
        if (item is Map<String, dynamic>) {
          items.add(OrderItem.fromJson(item));
        }
      }
    }

    return OrderModel(
      orderNumber: BaseModel.safeString(json, 'orderNumber'),
      orderStatus: BaseModel.safeString(json, 'orderStatus'),
      orderStatusName: BaseModel.safeString(json, 'orderStatusName'),
      totalAmount: BaseModel.safeDouble(json, 'totalAmount'),
      payAmount: BaseModel.safeDouble(json, 'payAmount'),
      discountAmount: BaseModel.safeDouble(json, 'discountAmount'),
      payType: BaseModel.safeString(json, 'payType'),
      payTypeName: BaseModel.safeString(json, 'payTypeName'),
      createTime: BaseModel.safeString(json, 'createTime'),
      payTime: BaseModel.safeString(json, 'payTime'),
      deliveryAddress: BaseModel.safeMap(json, 'deliveryAddress') != null
          ? AddressModel.fromJson(BaseModel.safeMap(json, 'deliveryAddress')!)
          : null,
      orderItems: items,
      businessName: BaseModel.safeString(json, 'businessName'),
      businessLogo: BaseModel.safeString(json, 'businessLogo'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'orderNumber': orderNumber,
      'orderStatus': orderStatus,
      'orderStatusName': orderStatusName,
      'totalAmount': totalAmount,
      'payAmount': payAmount,
      'discountAmount': discountAmount,
      'payType': payType,
      'payTypeName': payTypeName,
      'createTime': createTime,
      'payTime': payTime,
      'deliveryAddress': deliveryAddress?.toJson(),
      'orderItems': orderItems?.map((e) => e.toJson()).toList(),
      'businessName': businessName,
      'businessLogo': businessLogo,
    };
  }

  @override
  OrderModel copyWith({
    String? orderNumber,
    String? orderStatus,
    String? orderStatusName,
    double? totalAmount,
    double? payAmount,
    double? discountAmount,
    String? payType,
    String? payTypeName,
    String? createTime,
    String? payTime,
    AddressModel? deliveryAddress,
    List<OrderItem>? orderItems,
    String? businessName,
    String? businessLogo,
  }) {
    return OrderModel(
      orderNumber: orderNumber ?? this.orderNumber,
      orderStatus: orderStatus ?? this.orderStatus,
      orderStatusName: orderStatusName ?? this.orderStatusName,
      totalAmount: totalAmount ?? this.totalAmount,
      payAmount: payAmount ?? this.payAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      payType: payType ?? this.payType,
      payTypeName: payTypeName ?? this.payTypeName,
      createTime: createTime ?? this.createTime,
      payTime: payTime ?? this.payTime,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      orderItems: orderItems ?? this.orderItems,
      businessName: businessName ?? this.businessName,
      businessLogo: businessLogo ?? this.businessLogo,
    );
  }
}

/// 订单项目模型
class OrderItem extends BaseModel {
  String? commodityId;
  String? name; // 商品名称 (对应API字段)
  String? mainPic; // 商品主图 (对应API字段)
  String? commodityPropertyId;
  String? commodityProperty; // 商品属性字符串 (对应API字段)
  int? commodityNum; // 商品数量 (对应API字段)
  double? unitPrice;
  double? totalPrice;

  OrderItem({
    this.commodityId,
    this.name,
    this.mainPic,
    this.commodityPropertyId,
    this.commodityProperty,
    this.commodityNum,
    this.unitPrice,
    this.totalPrice,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      commodityId: BaseModel.safeString(json, 'commodityId'),
      name: BaseModel.safeString(json, 'name'),
      mainPic: BaseModel.safeString(json, 'mainPic'),
      commodityPropertyId: BaseModel.safeString(json, 'commodityPropertyId'),
      commodityProperty: BaseModel.safeString(json, 'commodityProperty'),
      commodityNum: BaseModel.safeInt(json, 'commodityNum'),
      unitPrice: BaseModel.safeDouble(json, 'unitPrice'),
      totalPrice: BaseModel.safeDouble(json, 'totalPrice'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'commodityId': commodityId,
      'name': name,
      'mainPic': mainPic,
      'commodityPropertyId': commodityPropertyId,
      'commodityProperty': commodityProperty,
      'commodityNum': commodityNum,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
    };
  }

  @override
  OrderItem copyWith({
    String? commodityId,
    String? name,
    String? mainPic,
    String? commodityPropertyId,
    String? commodityProperty,
    int? commodityNum,
    double? unitPrice,
    double? totalPrice,
  }) {
    return OrderItem(
      commodityId: commodityId ?? this.commodityId,
      name: name ?? this.name,
      mainPic: mainPic ?? this.mainPic,
      commodityPropertyId: commodityPropertyId ?? this.commodityPropertyId,
      commodityProperty: commodityProperty ?? this.commodityProperty,
      commodityNum: commodityNum ?? this.commodityNum,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }
}
