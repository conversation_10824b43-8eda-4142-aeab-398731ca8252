import 'base_model.dart';

class ArticleModel extends BaseModel {
  String? id;
  String? mainPic;
  String? resourceTitle;
  String? resourceIntro;
  String? resourceInfo;
  String? createTime;
  String? userLikeCount;
  String? userCommentCount;

  ArticleModel({
    this.id,
    this.mainPic,
    this.resourceTitle,
    this.resourceIntro,
    this.resourceInfo,
    this.createTime,
    this.userLikeCount,
    this.userCommentCount,
  });

  factory ArticleModel.fromJson(Map<String, dynamic> json) {
    return ArticleModel(
      id: BaseModel.safeString(json, 'id'),
      mainPic: BaseModel.safeString(json, 'mainPic'),
      resourceTitle: BaseModel.safeString(json, 'resourceTitle'),
      resourceIntro: BaseModel.safeString(json, 'resourceIntro'),
      resourceInfo: BaseModel.safeString(json, 'resourceInfo'),
      createTime: BaseModel.safeString(json, 'createTime'),
      userLikeCount: BaseModel.safeString(json, 'userLikeCount'),
      userCommentCount: BaseModel.safeString(json, 'userCommentCount'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'mainPic': mainPic,
      'resourceTitle': resourceTitle,
      'resourceIntro': resourceIntro,
      'resourceInfo': resourceInfo,
      'createTime': createTime,
      'userLikeCount': userLikeCount,
      'userCommentCount': userCommentCount,
    };
  }

  @override
  ArticleModel copyWith() {
    return ArticleModel(
      id: id,
      mainPic: mainPic,
      resourceTitle: resourceTitle,
      resourceIntro: resourceIntro,
      resourceInfo: resourceInfo,
      createTime: createTime,
      userLikeCount: userLikeCount,
      userCommentCount: userCommentCount,
    );
  }

  @override
  ArticleModel copyWithFields({
    String? id,
    String? mainPic,
    String? resourceTitle,
    String? resourceIntro,
    String? resourceInfo,
    String? createTime,
    String? userLikeCount,
    String? userCommentCount,
  }) {
    return ArticleModel(
      id: id ?? this.id,
      mainPic: mainPic ?? this.mainPic,
      resourceTitle: resourceTitle ?? this.resourceTitle,
      resourceIntro: resourceIntro ?? this.resourceIntro,
      resourceInfo: resourceInfo ?? this.resourceInfo,
      createTime: createTime ?? this.createTime,
      userLikeCount: userLikeCount ?? this.userLikeCount,
      userCommentCount: userCommentCount ?? this.userCommentCount,
    );
  }
}
