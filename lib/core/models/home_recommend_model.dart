import 'base_model.dart';
import 'article_model.dart';
import 'designer_case_model.dart';

/// 首页推荐资源模型
class RecommendModel extends BaseModel {
  /// 资源类型：1=案例 4=资讯 0=视频 等
  final int resourceType;

  /// 资讯数据
  final ArticleModel? article;

  /// 案例数据
  final DesignerCaseModel? designerCase;

  RecommendModel({
    required this.resourceType,
    this.article,
    this.designerCase,
  });

  factory RecommendModel.fromJson(Map<String, dynamic> json) {
    final int type = BaseModel.safeInt(json, 'resourceType') ?? -1;
    ArticleModel? article;
    DesignerCaseModel? designerCase;

    if (type == 4 && json['gazoHuiArticle'] is Map<String, dynamic>) {
      article = ArticleModel.fromJson(
          json['gazoHuiArticle'] as Map<String, dynamic>);
    }

    if (type == 1 && json['gazoHuiDesignerCase'] is Map<String, dynamic>) {
      designerCase = DesignerCaseModel.fromJson(
          json['gazoHuiDesignerCase'] as Map<String, dynamic>);
    }

    return RecommendModel(
      resourceType: type,
      article: article,
      designerCase: designerCase,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'resourceType': resourceType,
      'gazoHuiArticle': article?.toJson(),
      'gazoHuiDesignerCase': designerCase?.toJson(),
    };
  }

  @override
  RecommendModel copyWith({
    int? resourceType,
    ArticleModel? article,
    DesignerCaseModel? designerCase,
  }) {
    return RecommendModel(
      resourceType: resourceType ?? this.resourceType,
      article: article ?? this.article,
      designerCase: designerCase ?? this.designerCase,
    );
  }

  /// 便于列表解析
  static List<RecommendModel> listFromJson(List<dynamic>? list) {
    return BaseModel.fromJsonList<RecommendModel>(
      list,
      (json) => RecommendModel.fromJson(json),
    );
  }
} 