class SoftStyleModel {
  final String pic;        // 图片地址
  final String dictLabel;  // 风格名称
  final String dictValue;  // 风格值（接口需要）
  final int roomType;      // 房间类型（接口需要）
  final int index;         // 所在房间索引位置

  SoftStyleModel({
    required this.pic,
    required this.dictLabel,
    required this.dictValue,
    required this.roomType,
    required this.index,
  });

  /// JSON 转模型
  factory SoftStyleModel.fromJson(Map<String, dynamic> json, {required int index}) {
    return SoftStyleModel(
      pic: json['pic'] ?? '',
      dictLabel: json['dictLabel']?.toString() ?? '',
      dictValue: json['dictValue']?.toString() ?? '',
      roomType: (json['roomType'] is int)
          ? json['roomType'] as int
          : int.tryParse(json['roomType']?.toString() ?? '') ?? 0,
      index: index,
    );
  }

  /// 模型转 JSON（保持兼容外部仍使用 Map 的逻辑）
  Map<String, dynamic> toJson() {
    return {
      'pic': pic,
      'dictLabel': dictLabel,
      'dictValue': dictValue,
      'roomType': roomType,
      'index': index,
    };
  }
} 