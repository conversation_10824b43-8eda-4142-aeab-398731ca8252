import 'base_model.dart';

/// 收藏类型枚举
enum CollectionType {
  commodity(7, "商品"),
  case_(1, "案例"),
  designer(2, "设计师"),
  article(3, "文章");

  final int value;
  final String label;
  const CollectionType(this.value, this.label);

  static CollectionType? fromValue(int value) {
    for (var type in CollectionType.values) {
      if (type.value == value) return type;
    }
    return null;
  }
}

/// 收藏项目模型
class CollectionItem extends BaseModel {
  String? id;
  String? businessId;
  int? businessType;
  String? title;
  String? description;
  String? imageUrl;
  String? createTime;
  CollectionType? type;
  Map<String, dynamic>? extraData; // 存储不同类型收藏的额外数据

  CollectionItem({
    this.id,
    this.businessId,
    this.businessType,
    this.title,
    this.description,
    this.imageUrl,
    this.createTime,
    this.type,
    this.extraData,
  });

  factory CollectionItem.fromJson(Map<String, dynamic> json) {
    final businessType = BaseModel.safeInt(json, 'businessType');
    return CollectionItem(
      id: BaseModel.safeString(json, 'id'),
      businessId: BaseModel.safeString(json, 'businessId'),
      businessType: businessType,
      title: BaseModel.safeString(json, 'title'),
      description: BaseModel.safeString(json, 'description'),
      imageUrl: BaseModel.safeString(json, 'imageUrl'),
      createTime: BaseModel.safeString(json, 'createTime'),
      type: businessType != null ? CollectionType.fromValue(businessType) : null,
      extraData: BaseModel.safeMap(json, 'extraData'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'businessId': businessId,
      'businessType': businessType,
      'title': title,
      'description': description,
      'imageUrl': imageUrl,
      'createTime': createTime,
      'extraData': extraData,
    };
  }

  @override
  CollectionItem copyWith({
    String? id,
    String? businessId,
    int? businessType,
    String? title,
    String? description,
    String? imageUrl,
    String? createTime,
    CollectionType? type,
    Map<String, dynamic>? extraData,
  }) {
    return CollectionItem(
      id: id ?? this.id,
      businessId: businessId ?? this.businessId,
      businessType: businessType ?? this.businessType,
      title: title ?? this.title,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      createTime: createTime ?? this.createTime,
      type: type ?? this.type,
      extraData: extraData ?? this.extraData,
    );
  }

  /// 获取收藏类型的显示名称
  String get typeLabel => type?.label ?? '未知';
}

/// 收藏请求模型
class CollectionRequest extends BaseModel {
  String businessId;
  int businessType;

  CollectionRequest({
    required this.businessId,
    required this.businessType,
  });

  factory CollectionRequest.fromJson(Map<String, dynamic> json) {
    return CollectionRequest(
      businessId: BaseModel.safeString(json, 'businessId') ?? '',
      businessType: BaseModel.safeInt(json, 'businessType') ?? 0,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'businessId': businessId,
      'businessType': businessType,
    };
  }

  @override
  CollectionRequest copyWith({
    String? businessId,
    int? businessType,
  }) {
    return CollectionRequest(
      businessId: businessId ?? this.businessId,
      businessType: businessType ?? this.businessType,
    );
  }

  /// 创建商品收藏请求
  static CollectionRequest forCommodity(String commodityId) {
    return CollectionRequest(
      businessId: commodityId,
      businessType: CollectionType.commodity.value,
    );
  }

  /// 创建案例收藏请求
  static CollectionRequest forCase(String caseId) {
    return CollectionRequest(
      businessId: caseId,
      businessType: CollectionType.case_.value,
    );
  }

  /// 创建设计师收藏请求
  static CollectionRequest forDesigner(String designerId) {
    return CollectionRequest(
      businessId: designerId,
      businessType: CollectionType.designer.value,
    );
  }

  /// 创建文章收藏请求
  static CollectionRequest forArticle(String articleId) {
    return CollectionRequest(
      businessId: articleId,
      businessType: CollectionType.article.value,
    );
  }
}

/// 取消收藏请求模型
class RemoveCollectionRequest extends BaseModel {
  String id;

  RemoveCollectionRequest({
    required this.id,
  });

  factory RemoveCollectionRequest.fromJson(Map<String, dynamic> json) {
    return RemoveCollectionRequest(
      id: BaseModel.safeString(json, 'id') ?? '',
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
    };
  }

  @override
  RemoveCollectionRequest copyWith({
    String? id,
  }) {
    return RemoveCollectionRequest(
      id: id ?? this.id,
    );
  }
}

/// 收藏列表响应模型
class CollectionListResponse extends BaseModel {
  List<CollectionItem>? items;
  int? total;
  int? page;
  int? pageSize;
  bool? hasMore;

  CollectionListResponse({
    this.items,
    this.total,
    this.page,
    this.pageSize,
    this.hasMore,
  });

  factory CollectionListResponse.fromJson(Map<String, dynamic> json) {
    return CollectionListResponse(
      items: BaseModel.fromJsonList(
        BaseModel.safeList(json, 'items'),
        CollectionItem.fromJson,
      ),
      total: BaseModel.safeInt(json, 'total'),
      page: BaseModel.safeInt(json, 'page'),
      pageSize: BaseModel.safeInt(json, 'pageSize'),
      hasMore: BaseModel.safeBool(json, 'hasMore'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'items': items?.map((e) => e.toJson()).toList(),
      'total': total,
      'page': page,
      'pageSize': pageSize,
      'hasMore': hasMore,
    };
  }

  @override
  CollectionListResponse copyWith({
    List<CollectionItem>? items,
    int? total,
    int? page,
    int? pageSize,
    bool? hasMore,
  }) {
    return CollectionListResponse(
      items: items ?? this.items,
      total: total ?? this.total,
      page: page ?? this.page,
      pageSize: pageSize ?? this.pageSize,
      hasMore: hasMore ?? this.hasMore,
    );
  }
}
