import 'base_model.dart';

class Area {
  final dynamic id;
  final dynamic parentId;
  final int deep;
  final String name;
  final String pinYinPrefix;
  final String? pinYin;
  final dynamic originalId;
  final String originalName;
  final List<dynamic> children;

  Area({
    required this.id,
    required this.parentId,
    required this.deep,
    required this.name,
    required this.pinYinPrefix,
    this.pinYin,
    this.originalId,
    required this.originalName,
    required this.children,
  });

  factory Area.fromJson(Map<String, dynamic> json) {
    return Area(
      id: json['id'],
      parentId: json['parentId'],
      deep: BaseModel.safeInt(json, 'deep') ?? 0,
      name: BaseModel.safeString(json, 'name') ?? '',
      pinYinPrefix: BaseModel.safeString(json, 'pinYinPrefix') ?? '',
      pinYin: BaseModel.safeString(json, 'pinYin'),
      originalId: json['originalId'],
      originalName: BaseModel.safeString(json, 'originalName') ?? '',
      children: json['children'] ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'parentId': parentId,
      'deep': deep,
      'name': name,
      'pinYinPrefix': pinYinPrefix,
      'pinYin': pinYin,
      'originalId': originalId,
      'originalName': originalName,
      'children': children,
    };
  }
}