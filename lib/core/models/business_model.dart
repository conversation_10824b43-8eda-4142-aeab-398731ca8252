import 'base_model.dart';

/// 商家/店铺模型
class BusinessModel extends BaseModel {
  final String businessId; // 店铺ID
  final String? businessLogo; // 店铺Logo
  final String? businessName; // 店铺名称
  final String? bgUrl; // 背景图（可选）

  BusinessModel({
    required this.businessId,
    this.businessLogo,
    this.businessName,
    this.bgUrl,
  });

  factory BusinessModel.fromJson(Map<String, dynamic> json) {
    return BusinessModel(
      businessId: BaseModel.safeString(json, 'businessId') ?? '',
      businessLogo: BaseModel.safeString(json, 'businessLogo'),
      businessName: BaseModel.safeString(json, 'businessName'),
      bgUrl: BaseModel.safeString(json, 'bgUrl'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'businessId': businessId,
      'businessLogo': businessLogo,
      'businessName': businessName,
      'bgUrl': bgUrl,
    };
  }

  @override
  BusinessModel copyWith({
    String? businessId,
    String? businessLogo,
    String? businessName,
    String? bgUrl,
  }) {
    return BusinessModel(
      businessId: businessId ?? this.businessId,
      businessLogo: businessLogo ?? this.businessLogo,
      businessName: businessName ?? this.businessName,
      bgUrl: bgUrl ?? this.bgUrl,
    );
  }
}
