/// 软装报价明细整体模型
class QuoteSoftPriceDetailModel {
  final QuickPriceResult quickPriceResult; // 总价信息
  final List<MaterialDetail> material;   // 材料清单

  QuoteSoftPriceDetailModel({
    required this.quickPriceResult,
    required this.material,
  });

  factory QuoteSoftPriceDetailModel.fromJson(Map<String, dynamic> json) {
    return QuoteSoftPriceDetailModel(
      quickPriceResult: QuickPriceResult.fromJson(json['quickPriceResult'] ?? {}),
      material: (json['material'] as List<dynamic>? ?? [])
          .map((e) => MaterialDetail.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

/// 总价
class QuickPriceResult {
  final double totalPrice;

  QuickPriceResult({required this.totalPrice});

  factory QuickPriceResult.fromJson(Map<String, dynamic> json) {
    final num price = json['totalPrice'] ?? 0;
    return QuickPriceResult(totalPrice: price.toDouble());
  }
}

/// 房间材料详情
class MaterialDetail {
  final String roomTypeDisplay;
  final num landArea;
  final List<RespVo> respVos;

  MaterialDetail({
    required this.roomTypeDisplay,
    required this.landArea,
    required this.respVos,
  });

  factory MaterialDetail.fromJson(Map<String, dynamic> json) {
    return MaterialDetail(
      roomTypeDisplay: json['roomTypeDisplay'] ?? '',
      landArea: json['landArea'] ?? 0,
      respVos: (json['respVos'] as List<dynamic>? ?? [])
          .map((e) => RespVo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

/// 预算分类
class RespVo {
  final String budgetDisplay;
  final List<MaterialItem> items;

  RespVo({
    required this.budgetDisplay,
    required this.items,
  });

  factory RespVo.fromJson(Map<String, dynamic> json) {
    return RespVo(
      budgetDisplay: json['budgetDisplay'] ?? '',
      items: (json['items'] as List<dynamic>? ?? [])
          .map((e) => MaterialItem.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

/// 材料条目
class MaterialItem {
  final String skuPic;
  final String materialName;
  final String brandName;
  final String sku;
  final num quotaUsage;

  MaterialItem({
    required this.skuPic,
    required this.materialName,
    required this.brandName,
    required this.sku,
    required this.quotaUsage,
  });

  factory MaterialItem.fromJson(Map<String, dynamic> json) {
    return MaterialItem(
      skuPic: json['skuPic'] ?? '',
      materialName: json['materialName'] ?? '',
      brandName: json['brandName'] ?? '',
      sku: json['sku'] ?? '',
      quotaUsage: json['quotaUsage'] ?? 0,
    );
  }
} 