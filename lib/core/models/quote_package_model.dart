import 'base_model.dart';

/// 报价方案模型
class QuotePackage extends BaseModel {
  /// 方案 ID
  final String? packageId;

  /// 方案封面图
  final String? packagePic;

  /// 方案名称
  final String? packageName;

  /// 基准价格（100㎡）
  final double? basePrice;

  QuotePackage({
    this.packageId,
    this.packagePic,
    this.packageName,
    this.basePrice,
  });

  factory QuotePackage.fromJson(Map<String, dynamic> json) {
    return QuotePackage(
      packageId: BaseModel.safeString(json, 'packageId'),
      packagePic: BaseModel.safeString(json, 'packagePic'),
      packageName: BaseModel.safeString(json, 'packageName'),
      basePrice: BaseModel.safeDouble(json, 'basePrice'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'packageId': packageId,
      'packagePic': packagePic,
      'packageName': packageName,
      'basePrice': basePrice,
    };
  }

  @override
  QuotePackage copyWith({
    String? packageId,
    String? packagePic,
    String? packageName,
    double? basePrice,
  }) {
    return QuotePackage(
      packageId: packageId ?? this.packageId,
      packagePic: packagePic ?? this.packagePic,
      packageName: packageName ?? this.packageName,
      basePrice: basePrice ?? this.basePrice,
    );
  }
} 