import 'base_model.dart';

/// 装修记录模型
class FurnishRecordModel extends BaseModel {
  final String id;
  final String title;
  final String name;
  final String phone;
  final String region;
  final String roomType;
  final int bedroomNumber;
  final int livingRoomNumber;
  final int kitchenRoomNumber;
  final int toiletRoomNumber;
  final double area;
  final String decorateType;
  final String remark;
  final int decorationProgress;
  final String createTime;

  FurnishRecordModel({
    this.id = '',
    this.title = '',
    this.name = '',
    this.phone = '',
    this.region = '',
    this.roomType = '',
    this.bedroomNumber = 0,
    this.livingRoomNumber = 0,
    this.kitchenRoomNumber = 0,
    this.toiletRoomNumber = 0,
    this.area = 0,
    this.decorateType = '',
    this.remark = '',
    this.decorationProgress = 0,
    this.createTime = '',
  });

  factory FurnishRecordModel.fromJson(Map<String, dynamic> json) {
    return FurnishRecordModel(
      id: BaseModel.safeString(json, 'id') ?? '',
      title: BaseModel.safeString(json, 'title') ?? '',
      name: BaseModel.safeString(json, 'name') ?? '',
      phone: BaseModel.safeString(json, 'phone') ?? '',
      region: BaseModel.safeString(json, 'region') ?? '',
      roomType: BaseModel.safeString(json, 'roomType') ?? '',
      bedroomNumber: BaseModel.safeInt(json, 'bedroomNumber') ?? 0,
      livingRoomNumber: BaseModel.safeInt(json, 'livingRoomNumber') ?? 0,
      kitchenRoomNumber: BaseModel.safeInt(json, 'kitchenRoomNumber') ?? 0,
      toiletRoomNumber: BaseModel.safeInt(json, 'toiletRoomNumber') ?? 0,
      area: (BaseModel.safeDouble(json, 'area') ?? 0).toDouble(),
      decorateType: BaseModel.safeString(json, 'decorateType') ?? '',
      remark: BaseModel.safeString(json, 'remark') ?? '',
      decorationProgress: BaseModel.safeInt(json, 'decorationProgress') ?? 0,
      createTime: BaseModel.safeString(json, 'createTime') ?? '',
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'name': name,
      'phone': phone,
      'region': region,
      'roomType': roomType,
      'bedroomNumber': bedroomNumber,
      'livingRoomNumber': livingRoomNumber,
      'kitchenRoomNumber': kitchenRoomNumber,
      'toiletRoomNumber': toiletRoomNumber,
      'area': area,
      'decorateType': decorateType,
      'remark': remark,
      'decorationProgress': decorationProgress,
      'createTime': createTime,
    };
  }

  @override
  FurnishRecordModel copyWith({
    String? id,
    String? title,
    String? name,
    String? phone,
    String? region,
    String? roomType,
    int? bedroomNumber,
    int? livingRoomNumber,
    int? kitchenRoomNumber,
    int? toiletRoomNumber,
    double? area,
    String? decorateType,
    String? remark,
    int? decorationProgress,
    String? createTime,
  }) {
    return FurnishRecordModel(
      id: id ?? this.id,
      title: title ?? this.title,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      region: region ?? this.region,
      roomType: roomType ?? this.roomType,
      bedroomNumber: bedroomNumber ?? this.bedroomNumber,
      livingRoomNumber: livingRoomNumber ?? this.livingRoomNumber,
      kitchenRoomNumber: kitchenRoomNumber ?? this.kitchenRoomNumber,
      toiletRoomNumber: toiletRoomNumber ?? this.toiletRoomNumber,
      area: area ?? this.area,
      decorateType: decorateType ?? this.decorateType,
      remark: remark ?? this.remark,
      decorationProgress: decorationProgress ?? this.decorationProgress,
      createTime: createTime ?? this.createTime,
    );
  }

  /// 便于列表解析
  static List<FurnishRecordModel> listFromJson(List<dynamic>? list) {
    return BaseModel.fromJsonList<FurnishRecordModel>(
      list,
      (json) => FurnishRecordModel.fromJson(json),
    );
  }
} 