import 'base_model.dart';

/// 职位信息模型
class PostModel extends BaseModel {
  /// 头像链接
  final String? avatarUrl;

  /// 职位ID
  final int postId;

  /// 职位名称
  final String postName;

  final String tenantCode;

  PostModel({
    this.avatarUrl,
    required this.postId,
    required this.postName,
    required this.tenantCode,
  });

  factory PostModel.fromJson(Map<String, dynamic> json) {
    return PostModel(
      avatarUrl: BaseModel.safeString(json, 'avatarUrl'),
      postId: BaseModel.safeInt(json, 'postId') ?? 0,
      postName: BaseModel.safeString(json, 'postName') ?? '',
      tenantCode: BaseModel.safeString(json, 'tenantCode') ?? '',
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'avatarUrl': avatarUrl,
      'postId': postId,
      'postName': postName,
      'tenantCode': tenantCode,
    };
  }

  @override
  PostModel copyWith({
    String? avatarUrl,
    int? postId,
    String? postName,
    String? tenantCode,
  }) {
    return PostModel(
        avatarUrl: avatarUrl ?? this.avatarUrl,
        postId: postId ?? this.postId,
        postName: postName ?? this.postName,
        tenantCode: tenantCode ?? this.tenantCode);
  }

  /// 从JSON列表创建PostModel列表
  static List<PostModel> fromJsonList(List<dynamic>? jsonList) {
    return BaseModel.fromJsonList<PostModel>(
      jsonList,
      PostModel.fromJson,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PostModel &&
        other.postId == postId &&
        other.postName == postName &&
        other.avatarUrl == avatarUrl &&
        other.tenantCode == tenantCode;
  }

  @override
  int get hashCode => Object.hash(postId, postName, avatarUrl, tenantCode);
}

/// 职位列表响应类型定义
typedef PostListResponse = ApiResponse<List<PostModel>>;

/// 职位列表响应工厂方法
class PostListResponseFactory {
  static PostListResponse fromJson(Map<String, dynamic> json) {
    return ApiResponse<List<PostModel>>.fromJson(
      json,
      (data) => PostModel.fromJsonList(data),
    );
  }
}
