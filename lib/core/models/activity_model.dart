import 'base_model.dart';

/// 活动模型
class ActivityModel extends BaseModel {
  String? id;
  String? mainPic;
  String? resourceTitle;
  String? resourceIntro;
  String? resourceInfo;
  String? effectiveTimeBegin;
  String? effectiveTimeEnd;
  String? userLikeCount;
  String? userCollectionCount;
  String? userCommentCount;

  ActivityModel({
    this.id,
    this.mainPic,
    this.resourceTitle,
    this.resourceIntro,
    this.resourceInfo,
    this.effectiveTimeBegin,
    this.effectiveTimeEnd,
    this.userLikeCount,
    this.userCollectionCount,
    this.userCommentCount,
  });

  /// 使用 [json] 创建 [ActivityModel] 实例
  factory ActivityModel.fromJson(Map<String, dynamic> json) {
    return ActivityModel(
      id: BaseModel.safeString(json, 'id'),
      mainPic: BaseModel.safeString(json, 'mainPic'),
      resourceTitle: BaseModel.safeString(json, 'resourceTitle'),
      resourceIntro: BaseModel.safeString(json, 'resourceIntro'),
      resourceInfo: BaseModel.safeString(json, 'resourceInfo'),
      effectiveTimeBegin: BaseModel.safeString(json, 'effectiveTimeBegin'),
      effectiveTimeEnd: BaseModel.safeString(json, 'effectiveTimeEnd'),
      userLikeCount: BaseModel.safeString(json, 'userLikeCount'),
      userCollectionCount: BaseModel.safeString(json, 'userCollectionCount'),
      userCommentCount: BaseModel.safeString(json, 'userCommentCount'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'mainPic': mainPic,
      'resourceTitle': resourceTitle,
      'resourceIntro': resourceIntro,
      'resourceInfo': resourceInfo,
      'effectiveTimeBegin': effectiveTimeBegin,
      'effectiveTimeEnd': effectiveTimeEnd,
      'userLikeCount': userLikeCount,
      'userCollectionCount': userCollectionCount,
      'userCommentCount': userCommentCount,
    };
  }

  /// 创建当前实例的浅复制
  @override
  ActivityModel copyWith() {
    return ActivityModel(
      id: id,
      mainPic: mainPic,
      resourceTitle: resourceTitle,
      resourceIntro: resourceIntro,
      resourceInfo: resourceInfo,
      effectiveTimeBegin: effectiveTimeBegin,
      effectiveTimeEnd: effectiveTimeEnd,
      userLikeCount: userLikeCount,
      userCollectionCount: userCollectionCount,
      userCommentCount: userCommentCount,
    );
  }

  /// 提供可选参数的复制方法，方便更新指定字段
  ActivityModel copyWithFields({
    String? id,
    String? mainPic,
    String? resourceTitle,
    String? resourceIntro,
    String? resourceInfo,
    String? effectiveTimeBegin,
    String? effectiveTimeEnd,
    String? userLikeCount,
    String? userCollectionCount,
    String? userCommentCount,
  }) {
    return ActivityModel(
      id: id ?? this.id,
      mainPic: mainPic ?? this.mainPic,
      resourceTitle: resourceTitle ?? this.resourceTitle,
      resourceIntro: resourceIntro ?? this.resourceIntro,
      resourceInfo: resourceInfo ?? this.resourceInfo,
      effectiveTimeBegin: effectiveTimeBegin ?? this.effectiveTimeBegin,
      effectiveTimeEnd: effectiveTimeEnd ?? this.effectiveTimeEnd,
      userLikeCount: userLikeCount ?? this.userLikeCount,
      userCollectionCount: userCollectionCount ?? this.userCollectionCount,
      userCommentCount: userCommentCount ?? this.userCommentCount,
    );
  }

  /// 便于列表解析
  static List<ActivityModel> listFromJson(List<dynamic>? list) {
    return BaseModel.fromJsonList<ActivityModel>(
      list,
      (json) => ActivityModel.fromJson(json),
    );
  }
}
