import 'base_model.dart';

/// 设计师动态（Moment）模型
class DesignerMomentModel extends BaseModel {
  final String id; // 动态ID
  final String? dynamicTitle; // 动态标题
  final String? dynamicInfo; // 动态内容
  final List<String> dynamicPic; // 图片列表
  final String? createTime; // 创建时间

  DesignerMomentModel({
    required this.id,
    this.dynamicTitle,
    this.dynamicInfo,
    required this.dynamicPic,
    this.createTime,
  });

  factory DesignerMomentModel.fromJson(Map<String, dynamic> json) {
    return DesignerMomentModel(
      id: BaseModel.safeString(json, 'id') ?? '',
      dynamicTitle: BaseModel.safeString(json, 'dynamicTitle'),
      dynamicInfo: BaseModel.safeString(json, 'dynamicInfo'),
      dynamicPic: BaseModel.safeStringList(json, 'dynamicPic') ?? [],
      createTime: BaseModel.safeString(json, 'createTime'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dynamicTitle': dynamicTitle,
      'dynamicInfo': dynamicInfo,
      'dynamicPic': dynamicPic,
      'createTime': createTime,
    };
  }

  @override
  DesignerMomentModel copyWith({
    String? id,
    String? dynamicTitle,
    String? dynamicInfo,
    List<String>? dynamicPic,
    String? createTime,
  }) {
    return DesignerMomentModel(
      id: id ?? this.id,
      dynamicTitle: dynamicTitle ?? this.dynamicTitle,
      dynamicInfo: dynamicInfo ?? this.dynamicInfo,
      dynamicPic: dynamicPic ?? this.dynamicPic,
      createTime: createTime ?? this.createTime,
    );
  }

  /// 便于列表解析
  static List<DesignerMomentModel> listFromJson(List<dynamic>? list) {
    return BaseModel.fromJsonList<DesignerMomentModel>(
      list,
      (json) => DesignerMomentModel.fromJson(json),
    );
  }
} 