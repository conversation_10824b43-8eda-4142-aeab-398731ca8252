import 'dart:convert';

/// 基础Model抽象类
/// 提供统一的JSON序列化和反序列化接口
abstract class BaseModel {
  /// 从JSON Map创建Model实例
  /// 子类必须实现此方法
  static BaseModel fromJson(Map<String, dynamic> json) {
    throw UnimplementedError('fromJson must be implemented by subclass');
  }

  /// 将Model实例转换为JSON Map
  Map<String, dynamic> toJson();

  /// 安全地从JSON中获取字符串值
  static String? safeString(Map<String, dynamic> json, String key) {
    final value = json[key];
    return value?.toString();
  }

  /// 安全地从JSON中获取整数值
  static int? safeInt(Map<String, dynamic> json, String key) {
    final value = json[key];
    if (value == null) return null;
    if (value is int) return value;
    return int.tryParse(value.toString());
  }

  /// 安全地从JSON中获取双精度浮点数值
  static double? safeDouble(Map<String, dynamic> json, String key) {
    final value = json[key];
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    return double.tryParse(value.toString());
  }

  /// 安全地从JSON中获取布尔值
  static bool? safeBool(Map<String, dynamic> json, String key) {
    final value = json[key];
    if (value == null) return null;
    if (value is bool) return value;
    if (value is String) {
      return value.toLowerCase() == 'true' || value == '1';
    }
    if (value is int) return value == 1;
    return null;
  }

  /// 安全地从JSON中获取列表
  static List<T>? safeList<T>(Map<String, dynamic> json, String key) {
    final value = json[key];
    if (value == null) return null;
    if (value is List) {
      return value.whereType<T>().toList();
    }
    return null;
  }

  /// 安全地从JSON中获取字符串列表
  static List<String>? safeStringList(Map<String, dynamic> json, String key) {
    final value = json[key];
    if (value == null) return null;
    if (value is List) {
      return value.map((e) => e.toString()).toList();
    }
    return null;
  }

  /// 安全地从JSON中获取Map
  static Map<String, dynamic>? safeMap(Map<String, dynamic> json, String key) {
    final value = json[key];
    if (value == null) return null;
    if (value is Map<String, dynamic>) return value;
    return null;
  }

  /// 从JSON列表创建Model列表的辅助方法
  static List<T> fromJsonList<T extends BaseModel>(
    List<dynamic>? jsonList,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    if (jsonList == null) return [];
    return jsonList
        .whereType<Map<String, dynamic>>()
        .map((item) => fromJson(item))
        .toList();
  }

  @override
  String toString() {
    return jsonEncode(toJson());
  }

  /// 创建当前Model的副本
  /// 子类可以重写此方法以提供更具体的实现
  BaseModel copyWith();

  /// 检查两个Model是否相等
  /// 默认通过JSON字符串比较，子类可以重写以提供更高效的实现
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! BaseModel) return false;
    return toString() == other.toString();
  }

  @override
  int get hashCode => toString().hashCode;
}

/// API响应的通用包装类
class ApiResponse<T> {
  final int code;
  final String message;
  final T? data;
  final bool success;

  ApiResponse({
    required this.code,
    required this.message,
    this.data,
  }) : success = code == 200;

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? dataParser,
  ) {
    return ApiResponse<T>(
      code: BaseModel.safeInt(json, 'code') ?? 0,
      message: BaseModel.safeString(json, 'msg') ?? '',
      data: dataParser != null && json['data'] != null 
          ? dataParser(json['data']) 
          : json['data'] as T?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'msg': message,
      'data': data,
    };
  }
}
