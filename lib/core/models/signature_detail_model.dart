import 'base_model.dart';

/// 签名服务详情模型
class SignatureDetailModel extends BaseModel {
  final String? serviceId;
  final String? customerName;
  final String? customerPhone;
  final String? projectId;
  final String? projectAddress;
  final String? address;
  final String? serviceStatusDisplay;
  final String? decorateType;
  final String? decorateTypeDisplay;
  final int? area;
  final String? roomType;
  final String? roomTypeDisplay;
  final String? idCard;
  final String? region;

  SignatureDetailModel({
    this.serviceId,
    this.customerName,
    this.customerPhone,
    this.projectId,
    this.projectAddress,
    this.address,
    this.serviceStatusDisplay,
    this.decorateType,
    this.decorateTypeDisplay,
    this.area,
    this.roomType,
    this.roomTypeDisplay,
    this.idCard,
    this.region,
  });

  /// 使用 [json] 创建 [SignatureDetailModel] 实例
  factory SignatureDetailModel.fromJson(Map<String, dynamic> json) {
    return SignatureDetailModel(
      serviceId: BaseModel.safeString(json, 'serviceId'),
      customerName: BaseModel.safeString(json, 'customerName'),
      customerPhone: BaseModel.safeString(json, 'customerPhone'),
      projectId: BaseModel.safeString(json, 'projectId'),
      projectAddress: BaseModel.safeString(json, 'projectAddress'),
      address: BaseModel.safeString(json, 'address'),
      serviceStatusDisplay: BaseModel.safeString(json, 'serviceStatusDisplay'),
      decorateType: BaseModel.safeString(json, 'decorateType'),
      decorateTypeDisplay: BaseModel.safeString(json, 'decorateTypeDisplay'),
      area: BaseModel.safeInt(json, 'area'),
      roomType: BaseModel.safeString(json, 'roomType'),
      roomTypeDisplay: BaseModel.safeString(json, 'roomTypeDisplay'),
      idCard: BaseModel.safeString(json, 'idCard'),
      region: BaseModel.safeString(json, 'region'),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'serviceId': serviceId,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'projectId': projectId,
      'projectAddress': projectAddress,
      'address':address,
      'serviceStatusDisplay': serviceStatusDisplay,
      'decorateType': decorateType,
      'decorateTypeDisplay': decorateTypeDisplay,
      'area': area,
      'roomType': roomType,
      'roomTypeDisplay': roomTypeDisplay,
      'idCard': idCard,
      'region': region,
    };
  }

  @override
  SignatureDetailModel copyWith({
    String? serviceId,
    String? customerName,
    String? customerPhone,
    String? projectId,
    String? projectAddress,
    String? address,
    String? serviceStatusDisplay,
    String? decorateType,
    String? decorateTypeDisplay,
    int? area,
    String? roomType,
    String? roomTypeDisplay,
    String? idCard,
    String? region,
  }) {
    return SignatureDetailModel(
      serviceId: serviceId ?? this.serviceId,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      projectId: projectId ?? this.projectId,
      projectAddress: projectAddress ?? this.projectAddress,
      address: address ?? this.address,
      serviceStatusDisplay: serviceStatusDisplay ?? this.serviceStatusDisplay,
      decorateType: decorateType ?? this.decorateType,
      decorateTypeDisplay: decorateTypeDisplay ?? this.decorateTypeDisplay,
      area: area ?? this.area,
      roomType: roomType ?? this.roomType,
      roomTypeDisplay: roomTypeDisplay ?? this.roomTypeDisplay,
      idCard: idCard ?? this.idCard,
      region: region ?? this.region,
    );
  }

  /// 获取脱敏后的手机号
  String get maskedPhone {
    if (customerPhone == null || customerPhone!.length < 11) {
      return customerPhone ?? '';
    }
    return '${customerPhone!.substring(0, 3)} **** ${customerPhone!.substring(7)}';
  }

  /// 获取脱敏后的地址
  String get maskedAddress {
    if (projectAddress == null || projectAddress!.length < 8) {
      return projectAddress ?? '';
    }
    return '${projectAddress!.substring(0, projectAddress!.length - 4)}****';
  }

  /// 获取格式化的面积信息
  String get formattedArea {
    if (area == null) return '';
    return '$area㎡';
  }

  /// 判断是否为整装
  bool get isWholeDecorate {
    return decorateType == 'whole';
  }

  /// 判断是否为复式房
  bool get isDuplexRoom {
    return roomType == '1';
  }
}

/// 签名服务详情响应模型
class SignatureDetailResponseModel extends BaseModel {
  final String? msg;
  final int? code;
  final SignatureDetailModel? data;

  SignatureDetailResponseModel({
    this.msg,
    this.code,
    this.data,
  });

  /// 使用 [json] 创建 [SignatureDetailResponseModel] 实例
  factory SignatureDetailResponseModel.fromJson(Map<String, dynamic> json) {
    return SignatureDetailResponseModel(
      msg: BaseModel.safeString(json, 'msg'),
      code: BaseModel.safeInt(json, 'code'),
      data: json['data'] != null
          ? SignatureDetailModel.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'msg': msg,
      'code': code,
      'data': data?.toJson(),
    };
  }

  @override
  SignatureDetailResponseModel copyWith({
    String? msg,
    int? code,
    SignatureDetailModel? data,
  }) {
    return SignatureDetailResponseModel(
      msg: msg ?? this.msg,
      code: code ?? this.code,
      data: data ?? this.data,
    );
  }

  /// 判断请求是否成功
  bool get isSuccess {
    return code == 200;
  }
}
