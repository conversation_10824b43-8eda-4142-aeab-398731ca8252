import 'base_model.dart';

/// 设计师列表项模型
class DesignerListItemModel extends BaseModel {
  final String userId; // 用户ID
  final String? avatar; // 头像
  final String? realName; // 姓名
  final int? workingYears; // 从业年限
  final int? caseNumber; // 案例数量
  final List<String> excelStyle; // 擅长风格
  final List<String> caseMainPic; // 案例主图列表

  DesignerListItemModel({
    required this.userId,
    this.avatar,
    this.realName,
    this.workingYears,
    this.caseNumber,
    required this.excelStyle,
    required this.caseMainPic,
  });

  factory DesignerListItemModel.fromJson(Map<String, dynamic> json) {
    return DesignerListItemModel(
      userId: BaseModel.safeString(json, 'userId') ?? '',
      avatar: BaseModel.safeString(json, 'avatar'),
      realName: BaseModel.safeString(json, 'realName'),
      workingYears: BaseModel.safeInt(json, 'workingYears'),
      caseNumber: BaseModel.safeInt(json, 'caseNumber'),
      excelStyle: BaseModel.safeStringList(json, 'excelStyle') ?? [],
      caseMainPic: BaseModel.safeStringList(json, 'caseMainPic') ?? [],
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'avatar': avatar,
      'realName': realName,
      'workingYears': workingYears,
      'caseNumber': caseNumber,
      'excelStyle': excelStyle,
      'caseMainPic': caseMainPic,
    };
  }

  @override
  DesignerListItemModel copyWith({
    String? userId,
    String? avatar,
    String? realName,
    int? workingYears,
    int? caseNumber,
    List<String>? excelStyle,
    List<String>? caseMainPic,
  }) {
    return DesignerListItemModel(
      userId: userId ?? this.userId,
      avatar: avatar ?? this.avatar,
      realName: realName ?? this.realName,
      workingYears: workingYears ?? this.workingYears,
      caseNumber: caseNumber ?? this.caseNumber,
      excelStyle: excelStyle ?? this.excelStyle,
      caseMainPic: caseMainPic ?? this.caseMainPic,
    );
  }

  // 列表解析
  static List<DesignerListItemModel> listFromJson(List<dynamic>? list) {
    return BaseModel.fromJsonList<DesignerListItemModel>(
      list,
      (json) => DesignerListItemModel.fromJson(json),
    );
  }
}
