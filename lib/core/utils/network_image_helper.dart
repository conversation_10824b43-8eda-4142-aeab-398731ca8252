// 文件: lib/utils/network_image_helper.dart

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class NetworkImageHelper {
  // 单例模式
  static final NetworkImageHelper _instance = NetworkImageHelper._internal();
  factory NetworkImageHelper() => _instance;
  NetworkImageHelper._internal();
  static const String defaultAvatarUrl =
      'https://fileserver.gazolife.cn/2025/7/20250701_5a317a4ce3cd63d866a3bd862adbf4b5_20250701163446A346.png';

  // 自定义 HttpClient
  HttpClient get _customHttpClient {
    HttpClient client = HttpClient()
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
    return client;
  }

  // 获取带证书豁免的 CachedNetworkImage
  Widget getCachedNetworkImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit? fit,
    String defaultImageAsset =
        'assets/images/personal/icon_default_avatar.png', // 添加默认图片路径参数

    Widget Function(BuildContext, String)? placeholder,
    Widget Function(BuildContext, String, dynamic)? errorWidget,
  }) {
    // 更严格的URL验证
    final String finalImageUrl = _validateAndGetImageUrl(imageUrl);

    // 如果最终URL仍然无效，返回默认图片
    if (!_isValidUrl(finalImageUrl)) {
      return Image.asset(
        defaultImageAsset,
        width: width,
        height: height,
        fit: fit ?? BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _defaultErrorWidget(context, imageUrl, error);
        },
      );
    }

    return CachedNetworkImage(
      imageUrl: finalImageUrl,
      width: width,
      height: height,
      fit: fit ?? BoxFit.cover,
      // httpClient: _customHttpClient,
      placeholder: placeholder ?? _defaultPlaceholder,
      errorWidget: errorWidget ?? _defaultErrorWidget,
    );
  }

  // 获取带证书豁免的 Image.network
  Widget getNetworkImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit? fit,
  }) {
    final String finalImageUrl = _validateAndGetImageUrl(imageUrl);

    if (!_isValidUrl(finalImageUrl)) {
      return Container(
        width: width,
        height: height,
        color: Colors.grey[200],
        child: const Center(
          child: Icon(Icons.error_outline, color: Colors.red),
        ),
      );
    }

    return Image.network(
      finalImageUrl,
      width: width,
      height: height,
      fit: fit ?? BoxFit.cover,
      headers: const {
        "Access-Control-Allow-Origin": "*",
      },
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return _defaultPlaceholder(context, finalImageUrl);
      },
      errorBuilder: (context, error, stackTrace) {
        return _defaultErrorWidget(context, finalImageUrl, error);
      },
    );
  }

  // URL验证和处理方法
  String _validateAndGetImageUrl(String? imageUrl) {
    // 处理null或空字符串
    if (imageUrl == null || imageUrl.trim().isEmpty) {
      return defaultAvatarUrl;
    }

    // 移除可能的空白字符
    final cleanUrl = imageUrl.trim();

    // 如果URL看起来有效，返回它
    if (_isValidUrl(cleanUrl)) {
      return cleanUrl;
    }

    // 否则返回默认URL
    return defaultAvatarUrl;
  }

  // 检查URL是否有效
  bool _isValidUrl(String url) {
    if (url.isEmpty) return false;

    try {
      final uri = Uri.parse(url);
      // 检查是否有有效的scheme和host
      return uri.hasScheme &&
          (uri.scheme == 'http' || uri.scheme == 'https') &&
          uri.host.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  // 默认占位组件
  Widget _defaultPlaceholder(BuildContext context, String url) {
    return Container(
      color: Colors.grey[200],
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  // 默认错误组件
  Widget _defaultErrorWidget(BuildContext context, String url, dynamic error) {
    return Container(
      color: Colors.grey[200],
      child: const Center(
        child: Icon(Icons.error_outline, color: Colors.red),
      ),
    );
  }
}
