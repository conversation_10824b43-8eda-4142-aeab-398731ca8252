import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';

/// Firebase 服务管理类
/// 统一管理 Firebase Crashlytics 崩溃报告功能
class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  static FirebaseService get instance => _instance;

  // Firebase Crashlytics 实例
  late FirebaseCrashlytics _crashlytics;

  /// 初始化 Firebase Crashlytics 服务
  Future<void> initialize() async {
    try {
      // 初始化 Crashlytics
      _crashlytics = FirebaseCrashlytics.instance;

      // 配置 Crashlytics
      await _setupCrashlytics();

      debugPrint('Firebase Crashlytics 服务初始化成功');
    } catch (e) {
      debugPrint('Firebase Crashlytics 服务初始化失败: $e');
    }
  }

  /// 配置 Crashlytics
  Future<void> _setupCrashlytics() async {
    // 在 debug 模式下禁用 Crashlytics
    if (kDebugMode) {
      await _crashlytics.setCrashlyticsCollectionEnabled(false);
    } else {
      await _crashlytics.setCrashlyticsCollectionEnabled(true);
    }
  }

  /// 记录崩溃信息
  Future<void> recordError(dynamic exception, StackTrace? stackTrace,
      {String? reason}) async {
    try {
      await _crashlytics.recordError(exception, stackTrace, reason: reason);
    } catch (e) {
      debugPrint('记录崩溃信息失败: $e');
    }
  }

  /// 记录自定义日志
  void log(String message) {
    try {
      _crashlytics.log(message);
    } catch (e) {
      debugPrint('记录自定义日志失败: $e');
    }
  }

  /// 获取 Crashlytics 实例
  FirebaseCrashlytics get crashlytics => _crashlytics;
}
