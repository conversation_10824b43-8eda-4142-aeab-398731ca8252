// 导入必要的包
import 'package:dio/dio.dart'; // HTTP请求库
import 'package:oktoast/oktoast.dart'; // Toast提示组件
import 'package:flutter_easyloading/flutter_easyloading.dart'; // 加载动画组件
import 'package:flutter/material.dart'; // Flutter UI框架
import 'package:flutter_smarthome/core/utils/user_manager.dart'; // 用户管理工具
import 'package:flutter_smarthome/core/models/user_model.dart'; // 用户数据模型
import 'package:flutter_smarthome/core/utils/navigation_controller.dart'; // 导航控制器
import 'package:flutter_smarthome/core/debug/network_debug_manager.dart'; // 网络调试管理器
import 'package:package_info_plus/package_info_plus.dart'; // 应用版本信息
import 'dart:io'; // 平台信息

/// API管理器类 - 单例模式
///
/// 功能说明：
/// 1. 统一管理所有HTTP请求
/// 2. 自动处理用户认证（Token刷新）
/// 3. 统一错误处理和用户提示
/// 4. 网络调试功能（debug模式）
/// 5. 请求/响应日志记录
/// 6. 自动添加版本信息请求头（支持后端API版本适配）
///
/// 主要特性：
/// - 单例模式确保全局唯一实例
/// - 自动Token刷新机制
/// - 请求队列管理（防止重复刷新Token）
/// - 统一的错误处理和用户提示
/// - 集成网络调试工具
/// - 自动添加应用版本、平台信息等请求头
class ApiManager {
  // 单例模式实现
  static final ApiManager _instance = ApiManager._internal();
  factory ApiManager() => _instance;

  // Dio HTTP客户端实例
  late Dio _dio;

  // API服务器基础URL
  final String _baseUrl = 'http://erf.gazo.net.cn:6380';
  // 其他可用的服务器地址：
  // 'http://erf.gazo.net.cn:6380';  // 测试环境
  // 'http://localhost:6380';    // 本地开发环境
  // 'https://api.gazolife.cn'       // 生产环境

  // Token刷新状态标记 - 防止并发刷新
  bool _isRefreshing = false;

  // 待重试的请求队列 - 在Token刷新期间暂存请求
  final List<RequestOptions> _pendingRequests = [];

  // 网络调试管理器
  final NetworkDebugManager _debugManager = NetworkDebugManager();

  // 存储请求开始时间，用于计算耗时
  final Map<String, DateTime> _requestStartTimes = {};

  // 版本信息
  String _appVersion = '';
  String _buildNumber = '';
  String _packageName = '';
  String _platformName = '';
  String _platformVersion = '';

  // 私有构造函数，确保单例
  ApiManager._internal() {
    _initVersionInfo();
    _initDio();
  }

  /// 初始化版本信息
  void _initVersionInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      _appVersion = packageInfo.version;
      _buildNumber = packageInfo.buildNumber;
      _packageName = packageInfo.packageName;

      _platformName = Platform.operatingSystem;
      _platformVersion = Platform.operatingSystemVersion;
    } catch (e) {
      print('获取版本信息失败: $e');
    }
  }

  /// 初始化Dio HTTP客户端
  ///
  /// 设置基础配置：
  /// - 基础URL
  /// - 连接超时时间（5秒）
  /// - 接收超时时间（3秒）
  /// - 添加请求/响应/错误拦截器
  void _initDio() {
    _dio = Dio(BaseOptions(
      baseUrl: _baseUrl, // 设置API基础地址
      connectTimeout: const Duration(milliseconds: 10000), // 连接超时10秒
      receiveTimeout: const Duration(milliseconds: 10000), // 接收超时10秒
    ));

    // 添加拦截器处理请求、响应和错误
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: _handleRequest, // 请求拦截器 - 添加认证头等
      onResponse: _handleResponse, // 响应拦截器 - 处理业务逻辑错误
      onError: _handleError, // 错误拦截器 - 处理网络错误和认证错误
    ));
  }

  /// 请求拦截器 - 在发送请求前进行预处理
  ///
  /// 主要功能：
  /// 1. 添加公共请求头（Content-Type、TerminalId）
  /// 2. 自动添加用户认证Token
  /// 3. 自动添加版本信息（API版本、应用版本、平台信息等）
  /// 4. 记录请求日志
  /// 5. 显示加载动画
  /// 6. 记录调试信息
  ///
  /// [options] 请求配置选项
  /// [handler] 拦截器处理器
  void _handleRequest(
      RequestOptions options, RequestInterceptorHandler handler) {
    final UserModel? user = UserManager.instance.user;

    // 添加公共请求头信息
    options.headers.addAll({
      'Content-Type': 'application/json; charset=UTF-8', // 设置请求内容类型
      'TerminalId': 'ce5c98bea83e4d3289f3fc5f25c445a6', // 终端标识ID
      'API-Version': '1.0', // API版本号
      'App-Version': _appVersion, // 应用版本号
      'Platform': _platformName, // 平台名称（android/ios）
      'Platform-Version': _platformVersion, // 平台版本
    });

    // 如果调用方没有单独指定 Authorization，则自动添加用户Token
    options.headers.putIfAbsent('Authorization', () => user?.accessToken ?? '');

    // 记录请求开始时间和调试信息
    final requestId = _debugManager.logRequest(options);
    _requestStartTimes[requestId] = DateTime.now();

    // 将requestId存储到options的extra中，以便在响应时使用
    options.extra['debugRequestId'] = requestId;

    _logRequest(options); // 记录请求日志
    EasyLoading.show(status: '加载中...'); // 显示加载动画
    handler.next(options); // 继续执行请求
  }

  /// 响应拦截器 - 处理服务器响应
  ///
  /// 主要功能：
  /// 1. 记录响应日志
  /// 2. 关闭加载动画
  /// 3. 处理业务状态码（200成功、401认证失败、500服务器错误）
  /// 4. 自动Token刷新机制
  /// 5. 记录调试信息
  ///
  /// [response] 服务器响应
  /// [handler] 拦截器处理器
  Future<void> _handleResponse(
      Response response, ResponseInterceptorHandler handler) async {
    // 记录调试信息
    final requestId =
        response.requestOptions.extra['debugRequestId'] as String?;
    if (requestId != null) {
      final startTime = _requestStartTimes[requestId];
      final duration =
          startTime != null ? DateTime.now().difference(startTime) : null;
      _debugManager.logResponse(requestId, response, duration ?? Duration.zero);
      _requestStartTimes.remove(requestId);
    }

    _logResponse(response); // 记录响应日志
    EasyLoading.dismiss(); // 关闭加载动画

    // 处理所有响应数据
    if (response.data is Map<String, dynamic>) {
      final code = response.data['code']; // 业务状态码
      final msg = response.data['msg']; // 响应消息

      // 请求成功，直接返回
      if (code == 200) {
        return handler.next(response);
      }

      // 处理401认证错误（业务级别的认证失败）
      if (code == 401) {
        final requestOptions = response.requestOptions;

        // 如果是退出登录接口或刷新Token接口，直接处理登录过期
        if (requestOptions.path.contains('logout') ||
            requestOptions.path.contains('refresh-token')) {
          _handleAuthError(msg ?? '登录已失效，请重新登录');
          return handler.reject(DioException(
            requestOptions: requestOptions,
            response: response,
            error: msg ?? '认证失败',
          ));
        }

        // Token刷新逻辑：如果当前没有在刷新，开启刷新流程
        if (!_isRefreshing) {
          _isRefreshing = true; // 标记正在刷新
          _pendingRequests.add(requestOptions); // 将当前请求加入队列

          try {
            // 执行Token刷新
            await _refreshToken();

            // 刷新成功后，重新执行队列中的所有请求
            final List<RequestOptions> requestsToRetry =
                List.from(_pendingRequests);
            _pendingRequests.clear();

            for (var req in requestsToRetry) {
              // 更新请求头中的Authorization
              req.headers["Authorization"] =
                  UserManager.instance.user?.accessToken ?? "";

              // 如果是当前请求，返回重试结果
              if (req == requestOptions) {
                final retryResponse = await _dio.fetch(req);
                return handler.resolve(retryResponse);
              } else {
                // 其他请求静默重试
                _dio.fetch(req).catchError((e) {
                  // 静默处理其他请求的错误
                  return Response(requestOptions: req, statusCode: 500);
                });
              }
            }
          } catch (e) {
            // Token刷新失败，处理登录过期
            _handleAuthError(msg ?? '登录已失效，请重新登录');
            return handler.reject(DioException(
              requestOptions: requestOptions,
              response: response,
              error: msg ?? '认证失败',
            ));
          } finally {
            // 重置刷新状态
            _isRefreshing = false;
          }
        } else {
          // 已经在刷新中，将请求加入等待队列，等待刷新完成后处理
          _pendingRequests.add(requestOptions);

          // 等待刷新完成
          while (_isRefreshing) {
            await Future.delayed(const Duration(milliseconds: 100));
          }

          // 刷新完成后重试当前请求
          try {
            requestOptions.headers["Authorization"] =
                UserManager.instance.user?.accessToken ?? "";
            final retryResponse = await _dio.fetch(requestOptions);
            return handler.resolve(retryResponse);
          } catch (e) {
            // 重试失败，可能token刷新失败了
            return handler.reject(DioException(
              requestOptions: requestOptions,
              response: response,
              error: msg ?? '认证失败',
            ));
          }
        }
        return; // 中断后续流程，等待刷新逻辑处理
      }

      // 处理500错误
      if (code == 500) {
        showToast(
          msg ?? '服务器错误',
          position: ToastPosition.center,
        );
        return handler.reject(DioException(
          requestOptions: response.requestOptions,
          response: response,
          error: msg ?? '服务器错误',
        ));
      }
    }

    // 默认继续处理响应
    handler.next(response);
  }

  /// ===== 核心：在 onError 时处理 401 =====
  void _handleError(DioException err, ErrorInterceptorHandler handler) async {
    // 记录调试信息
    final requestId = err.requestOptions.extra['debugRequestId'] as String?;
    if (requestId != null) {
      final startTime = _requestStartTimes[requestId];
      final duration =
          startTime != null ? DateTime.now().difference(startTime) : null;
      _debugManager.logError(requestId, err, duration ?? Duration.zero);
      _requestStartTimes.remove(requestId);
    }

    _logError(err);
    EasyLoading.dismiss();

    // 检查是否是认证错误（HTTP状态码401 或 响应体中业务code为401）
    bool isAuthError = false;
    String? authErrorMsg;

    if (err.response?.statusCode == 401) {
      isAuthError = true;
      authErrorMsg = "登录已失效，请重新登录";
    } else if (err.response?.data is Map<String, dynamic>) {
      final responseData = err.response?.data as Map<String, dynamic>;
      if (responseData['code'] == 401) {
        isAuthError = true;
        authErrorMsg = "登录已失效，请重新登录";
      }
    }

    // 如果是认证错误并且不是刷新接口本身，就执行刷新流程
    if (isAuthError && !err.requestOptions.path.contains('refresh-token')) {
      // 如果是退出登录接口，直接处理登录过期
      if (err.requestOptions.path.contains('logout')) {
        _handleAuthError(authErrorMsg ?? '登录已失效，请重新登录');
        handler.next(err);
        return;
      }

      // 判断是否正在刷新
      if (!_isRefreshing) {
        _isRefreshing = true;
        _pendingRequests.add(err.requestOptions);

        try {
          // 调用刷新token的方法
          await _refreshToken();

          // 刷新成功以后，依次重试队列中的请求
          final List<RequestOptions> requestsToRetry =
              List.from(_pendingRequests);
          _pendingRequests.clear();

          for (var request in requestsToRetry) {
            // 重新设置新的 accessToken
            request.headers["Authorization"] =
                UserManager.instance.user?.accessToken ?? "";

            if (request == err.requestOptions) {
              // 当前请求重试
              final response = await _dio.fetch(request);
              handler.resolve(response);
              return;
            } else {
              // 其他请求静默重试
              _dio.fetch(request).catchError((e) {
                // 静默处理错误
                return Response(requestOptions: request, statusCode: 500);
              });
            }
          }
        } catch (e) {
          // 刷新失败：跳转登录
          _handleAuthError(authErrorMsg ?? "登录已失效，请重新登录");
          handler.next(err);
        } finally {
          // 关闭刷新状态
          _isRefreshing = false;
        }
      } else {
        // 已经在刷新，先把请求加入队列
        _pendingRequests.add(err.requestOptions);

        // 等待刷新完成
        while (_isRefreshing) {
          await Future.delayed(const Duration(milliseconds: 100));
        }

        // 刷新完成后重试当前请求
        try {
          err.requestOptions.headers["Authorization"] =
              UserManager.instance.user?.accessToken ?? "";
          final response = await _dio.fetch(err.requestOptions);
          handler.resolve(response);
        } catch (e) {
          // 重试失败
          handler.next(err);
        }
      }
    } else {
      // 其他错误，正常处理
      String errorMsg = _getErrorMessage(err);
      showToast(errorMsg, position: ToastPosition.center);
      handler.next(err);
    }
  }

  /// 刷新token的方法
  Future<void> _refreshToken() async {
    final oldRefreshToken = UserManager.instance.user?.refreshToken;
    if (oldRefreshToken == null) {
      throw Exception("无 refreshToken, 无法刷新");
    }

    try {
      // 发起刷新接口
      final response = await _dio.post(
        "/api/login/refresh-token",
        options: Options(headers: {
          'Authorization': oldRefreshToken,
        }),
      );

      // 判断是否成功
      if (response.data["code"] == 200) {
        final newAccessToken = response.data["data"]["accessToken"];
        final newRefreshToken = response.data["data"]["refreshToken"];
        // 使用 UserManager 更新并持久化新的 token
        await UserManager.instance.updateUser((u) {
          u.accessToken = newAccessToken;
          u.refreshToken = newRefreshToken;
        });
      } else {
        throw Exception("刷新失败: ${response.data["msg"]}");
      }
    } catch (e) {
      rethrow;
    }
  }

  String _getErrorMessage(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return '连接超时，请稍后重试';
      case DioExceptionType.receiveTimeout:
        return '接收超时，请稍后重试';
      case DioExceptionType.badResponse:
        if (error.response?.data is Map) {
          return error.response?.data['msg'] ?? '请求失败';
        }
        return '服务器响应错误';
      case DioExceptionType.cancel:
        return '请求已取消';
      default:
        return '请求失败，请检查网络';
    }
  }

  void _handleAuthError(String? msg) {
    // 先清除用户数据，这会自动触发notifyUserChanged
    UserManager.instance.clearUser();

    showToast(
      '登录已失效，请重新登录',
      position: ToastPosition.top,
    );

    // 给UI一些时间来响应用户状态变化
    // BaseTabBarController会自动响应用户状态变化，从员工模式切换到游客模式
    Future.delayed(const Duration(milliseconds: 500), () {
      // 确保用户状态变化已经被通知，让BaseTabBarController更新tab配置
      UserManager.instance.notifier.notifyUserChanged();
    });

    // 不再强制跳转到登录页面，让用户继续使用游客模式
    // 如果用户需要登录，可以通过"我的"页面主动登录
  }

  void _logRequest(RequestOptions options) {
    print('┌────── Request ──────');
    print('│ URL: ${options.baseUrl}${options.path}');
    print('│ Method: ${options.method}');
    print('│ Headers: ${options.headers}');
    if (options.queryParameters.isNotEmpty) {
      print('│ Query Parameters: ${options.queryParameters}');
    }
    if (options.data != null) {
      print('│ Body: ${options.data}');
    }
    print('└────────────────────');
  }

  void _logResponse(Response response) {
    print('┌────── Response ──────');
    print('│ Status Code: ${response.statusCode}');
    print('│ Data: ${response.data}');
    print('└─────────────────────');
  }

  void _logError(DioException error) {
    print('┌────── Error ──────');
    print('│ Type: ${error.type}');
    print('│ Message: ${error.message}');
    print('│ Error: ${error.error}');
    if (error.response != null) {
      print('│ Response: ${error.response?.data}');
    }
    print('└───────────────────');
  }

  BuildContext? _getGlobalContext() =>
      NavigationController.navigatorKey.currentContext;

  // 请求方法保持不变，但错误处理更统一
  Future<dynamic> get(String path,
      {Map<String, dynamic>? queryParameters}) async {
    try {
      final response = await _dio.get(path, queryParameters: queryParameters);
      return response.data['data'];
    } catch (e) {
      rethrow;
    }
  }

  /// POST 请求
  /// [path] 请求路径
  /// [data] 请求体
  Future<dynamic> post(String path, {Map<String, dynamic>? data}) async {
    try {
      Response response = await _dio.post(
        path,
        data: data,
      );
      return response.data['data'];
    } catch (e) {
      rethrow;
    }
  }

  Future<dynamic> postWithList(String path,
      {List<Map<String, dynamic>>? data}) async {
    try {
      Response response = await _dio.post(
        path,
        data: data,
      );
      return response.data['data'];
    } catch (e) {
      rethrow;
    }
  }

  /// PUT 请求
  Future<dynamic> put(String path, {Map<String, dynamic>? data}) async {
    try {
      Response response = await _dio.put(
        path,
        data: data,
      );
      return response.data['data'];
    } catch (e) {
      rethrow;
    }
  }

  /// DELETE 请求
  Future<dynamic> delete(String path, {Map<String, dynamic>? data}) async {
    try {
      Response response = await _dio.delete(
        path,
        data: data,
      );
      return response.data['data'];
    } catch (e) {
      rethrow;
    }
  }

  Future<dynamic> deleteWithParameters(String path,
      {Map<String, dynamic>? data}) async {
    try {
      Response response = await _dio.delete(
        path,
        queryParameters: data,
      );
      return response.data['data'];
    } catch (e) {
      rethrow;
    }
  }

  /// DELETE 请求 传参数组
  Future<dynamic> deleteWithList(String path, {List<dynamic>? data}) async {
    try {
      Response response = await _dio.delete(
        path,
        data: data,
      );
      return response.data['data'];
    } catch (e) {
      rethrow;
    }
  }

  Future<dynamic> uploadImage(
    String path,
    String filePath, {
    String? fileName,
    String formName = 'file',
  }) async {
    try {
      final file = await MultipartFile.fromFile(
        filePath,
        filename: fileName ?? filePath.split('/').last,
      );

      final formData = FormData.fromMap({formName: file});

      final response = await _dio.post(
        path,
        data: formData,
        options: Options(contentType: 'multipart/form-data'),
      );

      return response.data['data'];
    } catch (e) {
      print('图片上传失败: $e');
      rethrow; // 改为抛出错误，让拦截器统一处理
    }
  }
}
