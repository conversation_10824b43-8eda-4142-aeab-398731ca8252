import 'package:flutter/material.dart';
import 'package:flutter_smarthome/shared/components/product-grid.dart';
import 'package:flutter_smarthome/core/models/product_item.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/empty_state.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

// 新增: 导入完整的models
import 'package:flutter_smarthome/core/models/models.dart';

class ShoppingListWidget extends StatefulWidget {
  final String? firstCategoryId;
  final String? secondCategoryId;
  final String? categoryName;
  final String? businessId;
  final String? type; // 商品检索类型：1 - 精选、0 - 商品、2 - 活动、 3 - 新品
  const ShoppingListWidget({
    super.key,
    this.firstCategoryId,
    this.secondCategoryId,
    this.categoryName,
    this.businessId,
    this.type,
  });

  @override
  State<ShoppingListWidget> createState() => _ShoppingListWidgetState();
}

class _ShoppingListWidgetState extends State<ShoppingListWidget> {
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  int pageNum = 1;
  final int pageSize = 10;
  final List<ProductItem> products = [];

  @override
  void initState() {
    super.initState();
    getBusinessList();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: widget.businessId == null
          ? AppBar(
              backgroundColor: Colors.white,
              elevation: 0,
              title: Text(widget.categoryName ?? '',
                  style: const TextStyle(
                      color: Colors.black, fontWeight: FontWeight.bold)),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
                onPressed: () => Navigator.pop(context),
              ),
            )
          : null,
      body: SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        header: const WaterDropHeader(),
        footer: CustomFooter(
          builder: (BuildContext context, LoadStatus? mode) {
            Widget body;
            if (mode == LoadStatus.idle) {
              body = const Text("上拉加载");
            } else if (mode == LoadStatus.loading) {
              body = const CircularProgressIndicator();
            } else if (mode == LoadStatus.failed) {
              body = const Text("加载失败！点击重试！");
            } else if (mode == LoadStatus.canLoading) {
              body = const Text("松手加载更多");
            } else {
              body = const Text("");
            }
            return SizedBox(
              height: 55.0,
              child: Center(child: body),
            );
          },
        ),
        controller: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        child: products.isEmpty
            ? EmptyStateWidget(
                onRefresh: _onRefresh,
                emptyText: '暂无数据',
                buttonText: '点击刷新',
              )
            : ProductGrid(
                products: products,
                crossAxisCount: 2,
                childAspectRatio: 0.72,
              ),
      ),
    );
  }

  void _onRefresh() async {
    pageNum = 1;
    products.clear();
    await getBusinessList();
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    pageNum++;
    await getBusinessList();
    if (products.isNotEmpty) {
      _refreshController.loadFailed();
    } else {
      _refreshController.loadComplete();
    }
  }

  //普通商品列表查询
  Future<void> getBusinessList() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/shopping/commodity/list',
        queryParameters: {
          'searchFirstCategoryId': widget.firstCategoryId,
          'searchSecondCategoryId': widget.secondCategoryId,
          'businessId': widget.businessId,
          'type': widget.type,
          'pageNum': pageNum,
          'pageSize': pageSize,
        },
      );
      if (response['rows'] != null && response['rows'].isNotEmpty) {
        final List<dynamic> productsData = response['rows'] as List<dynamic>;

        if (response['pageTotal'] == pageNum || response['pageTotal'] == 0) {
          _refreshController.loadNoData();
        }
        setState(() {
          for (var item in productsData) {
            if (item is Map<String, dynamic>) {
              // 使用Model的fromJson方法，自动处理类型转换和空值
              ProductItem productItem = ProductItem.fromJson(item);
              products.add(productItem);
            }
          }
        });
      }
    } catch (e) {
      debugPrint('获取商品列表失败: $e');
      if (mounted) {
        // 可以在这里添加用户友好的错误提示
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(content: Text('加载失败，请重试')),
        // );
      }
    }
  }
}
