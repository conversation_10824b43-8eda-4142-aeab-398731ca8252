import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/models/models.dart';
import 'package:flutter_smarthome/core/utils/login_event.dart';
import 'package:flutter_smarthome/core/utils/network_image_helper.dart';
import 'package:flutter_smarthome/core/utils/user_manager.dart';
import 'package:oktoast/oktoast.dart';

class IdentityChoiceController extends StatefulWidget {
  final VoidCallback? onLoginSuccess;
  final String phone; //手机号
  final String code; //验证码
  final List<PostModel>? preloadedPostList; // 预加载的身份列表
  const IdentityChoiceController({
    super.key,
    this.onLoginSuccess,
    required this.phone,
    required this.code,
    this.preloadedPostList,
  });

  @override
  State<IdentityChoiceController> createState() =>
      _IdentityChoiceControllerState();
}

class _IdentityChoiceControllerState extends State<IdentityChoiceController> {
  int? selectedIndex; // 记录选中的索引
  List<PostModel> postList = []; // 职位列表
  bool isLoading = true; // 加载状态
  static const platform = MethodChannel('com.smartlife.app/login');

  @override
  void initState() {
    super.initState();
    // 如果有预加载的数据，直接使用；否则请求接口
    if (widget.preloadedPostList != null &&
        widget.preloadedPostList!.isNotEmpty) {
      setState(() {
        postList = widget.preloadedPostList!;
        isLoading = false;
      });
    } else {
      _getRoleList(); // 页面初始化时加载数据
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // 背景图覆盖整个屏幕
          Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/2.0x/icon_identity_bg.png'),
                fit: BoxFit.cover,
              ),
            ),
          ),
          // 主要内容
          Column(
            children: [
              //页面整体滚动
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(
                          height: MediaQuery.of(context).size.height * 0.15),
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Padding(
                          padding: EdgeInsets.only(left: 32.w),
                          child: Image.asset(
                            'assets/images/personal/icon_personal_tip.png',
                            width: 140.w,
                            height: 30.h,
                          ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 32.w),
                        child: isLoading
                            ? const Center(
                                child: CircularProgressIndicator(
                                  color: Color(0xFFFFA839),
                                ),
                              )
                            : GridView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: postList.length,
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2,
                                  mainAxisSpacing: 16.h,
                                  crossAxisSpacing: 24.w,
                                  childAspectRatio: 3,
                                ),
                                itemBuilder: (context, index) {
                                  final post = postList[index];
                                  return GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        selectedIndex = index;
                                      });
                                    },
                                    child: Container(
                                        height: 60.h,
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(50),
                                          border: selectedIndex == index
                                              ? Border.all(
                                                  color:
                                                      const Color(0xFFFFA839),
                                                  width: 2,
                                                )
                                              : null,
                                        ),
                                        child: Row(
                                          children: [
                                            SizedBox(width: 16.w),
                                            NetworkImageHelper()
                                                .getCachedNetworkImage(
                                              imageUrl: post.avatarUrl ??
                                                  "https://fileserver.gazolife.cn/2025/7/20250701_5a317a4ce3cd63d866a3bd862adbf4b5_20250701163446A346.png",
                                              fit: BoxFit.cover,
                                              width: 40.w,
                                              height: 40.h,
                                            ),
                                            SizedBox(width: 4.w),
                                            Expanded(
                                              child: Text(
                                                post.postName,
                                                style: TextStyle(
                                                  fontSize: 14.sp,
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.black,
                                                ),
                                                overflow: TextOverflow.ellipsis,
                                                maxLines: 1,
                                              ),
                                            ),
                                          ],
                                        )),
                                  );
                                },
                              ),
                      ),
                    ],
                  ),
                ),
              ),
              // 底部按钮区域
              SafeArea(
                child: Padding(
                  padding:
                      EdgeInsets.only(left: 32.w, right: 44.w, bottom: 32.h),
                  child: GestureDetector(
                    onTap: () {
                      // 检查是否选择了职位
                      if (selectedIndex == null) {
                        showToast('请先选择一个职位');
                        return;
                      }
                      // 执行登录逻辑
                      _handleLogin();
                    },
                    child: Container(
                      width: double.infinity,
                      height: 44.h,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                            colors: [Color(0xFF4F4F4F), Color(0xFF2A2A2A)]),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          '确定',
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 14.sp,
                              fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  //请求接口
  Future<void> _getRoleList() async {
    try {
      final apiManager = ApiManager();
      final data = await apiManager.get('/api/login/role/list',
          queryParameters: {"mobile": widget.phone});
      // ApiManager已经返回data部分，直接解析数组
      if (data != null) {
        final postList = PostModel.fromJsonList(data);
        setState(() {
          this.postList = postList;
          isLoading = false;
        });
      } else {
        setState(() {
          isLoading = false;
        });
        showToast('获取角色列表失败');
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      showToast('获取角色列表失败，请重试');
      rethrow; // 重新抛出异常，让调用者知道操作失败
    }
  }

  Future<void> _handleLogin() async {
    try {
      //发起登录请求
      final response = await ApiManager().post(
        '/api/login/via-code',
        data: {
          'mobile': widget.phone,
          'code': widget.code.toString(),
          'postId': postList[selectedIndex!].postId.toString(),
          'tenantCode': postList[selectedIndex!].tenantCode.toString(),
        },
      );

      if (response != null) {
        // 处理登录成功
        UserModel user = UserModel.fromJson(response);
        await UserManager.instance.saveUser(user);

        // 发送登录成功事件
        eventBus.emit(LoginEvent(true));

        if (widget.onLoginSuccess != null) {
          widget.onLoginSuccess!();
        }
        if (postList[selectedIndex!].postId == 0) {
          // 如果是普通用户
          try {
            await platform.invokeMethod('tuyaLogin', {
              'mobile': user.mobile,
              'password': user.tuyaPwd,
            });
          } catch (e) {
            debugPrint('调用原生方法失败: $e');
          }
        }

        if (mounted) {
          Navigator.pop(context, true);
          Navigator.pop(context, true);
        }
      }
    } catch (e) {
      showToast('登录失败，请重试');
    }
  }
}
