import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/shared/dialogs/bottom_sheet_selector.dart';
import 'package:flutter_smarthome/shared/dialogs/nickname_dialog.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/image_picker.dart';
import 'package:flutter_smarthome/core/utils/network_image_helper.dart';
import 'package:oktoast/oktoast.dart';
import 'package:flutter_smarthome/core/models/user_model.dart';
import 'package:flutter_smarthome/core/utils/user_manager.dart';
import 'package:city_pickers/city_pickers.dart';

class PersonalInfoWidget extends StatefulWidget {
  const PersonalInfoWidget({super.key});

  @override
  State<PersonalInfoWidget> createState() => _PersonalInfoWidgetState();
}

class _PersonalInfoWidgetState extends State<PersonalInfoWidget> {
  UserModel? user; // 用户信息
  int? _selectedGenderIndex; // 选中的性别索引
  //用户字典
  Map<String, dynamic> userDict = {};

  @override
  void initState() {
    super.initState();
    user = UserManager.instance.user;
    userDict = {
      'nickname': user?.nickname,
      'avatar': user?.avatar,
      'sex': user?.sex,
      'city': user?.city,
      'profile': user?.profile,
    };

    // 监听用户信息变化
    UserManager.instance.notifier.addListener(_onUserChanged);
  }

  @override
  void dispose() {
    // 移除监听器
    UserManager.instance.notifier.removeListener(_onUserChanged);
    super.dispose();
  }

  void _onUserChanged() {
    if (mounted) {
      setState(() {
        user = UserManager.instance.user;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: const Text('个人信息',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold)),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: Column(
        children: <Widget>[
          SizedBox(height: 10.h),
          ListTile(
            title: const Text('头像'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(25), // 可选：使头像呈圆形
                    color: Colors.grey[200], // 可选：添加背景色
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(25), // 如果要圆形头像
                    child: NetworkImageHelper().getCachedNetworkImage(
                      imageUrl: user?.avatar ?? '',
                      width: 30.w,
                      height: 30.h,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                SizedBox(width: 10.w),
                const Icon(Icons.arrow_forward_ios,
                    size: 16, color: Colors.grey),
              ],
            ),
            onTap: () {
              showAvatarDialog(context);
            },
          ),
          SizedBox(height: 10.h),
          //分割线
          Divider(
            indent: 20.w,
            endIndent: 20.w,
            height: 1.h,
            color: const Color(0xFFF9F9F9),
          ),
          ListTile(
            title: const Text('昵称'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(user?.nickname ?? ''),
                SizedBox(width: 10.w),
                const Icon(Icons.arrow_forward_ios,
                    size: 16, color: Colors.grey),
              ],
            ),
            onTap: () {
              showNickNameDialog(context);
            },
          ),
          //分割线
          Divider(
            indent: 20.w,
            endIndent: 20.w,
            height: 1,
            color: const Color(0xFFF9F9F9),
          ),
          ListTile(
            title: const Text('简介'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(user?.profile ?? ''),
                SizedBox(width: 10.w),
                const Icon(Icons.arrow_forward_ios,
                    size: 16, color: Colors.grey),
              ],
            ),
            onTap: () {
              showProfileDialog(context);
            },
          ),
          Divider(
            indent: 20.w,
            endIndent: 20.w,
            height: 1,
            color: const Color(0xFFF9F9F9),
          ),
          ListTile(
            title: const Text('性别'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(user?.sex == '1' ? '男' : '女'),
                SizedBox(width: 10.w),
                const Icon(Icons.arrow_forward_ios,
                    size: 16, color: Colors.grey),
              ],
            ),
            onTap: () {
              showGenderSelector(context);
            },
          ),
          Divider(
            indent: 20.w,
            endIndent: 20.w,
            height: 1,
            color: const Color(0xFFF9F9F9),
          ),
          ListTile(
            title: const Text('城市'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(user?.city ?? ''),
                SizedBox(width: 10.w),
                const Icon(Icons.arrow_forward_ios,
                    size: 16, color: Colors.grey),
              ],
            ),
            onTap: () async {
              try {
                final result = await getResult();
                if (result == null) {
                  return;
                }
                final address =
                    '${result.provinceName}-${result.cityName}-${result.areaName}';
                userDict['city'] = address;

                await _handleEditPersonalInfo();
                await UserManager.instance
                    .updateUser((user) => user.city = address);
              } catch (e) {
                debugPrint('修改城市失败: $e');
                showToast('修改失败，请重试');
              }
            },
          ),
        ],
      ),
    );
  }

  //修改头像
  void showAvatarDialog(BuildContext context) async {
    try {
      final File? imageFile =
          await ImagePickerUtils.showImagePickerDialog(context);

      // 处理选择结果
      if (imageFile != null) {
        debugPrint('选中的图片路径: ${imageFile.path}');

        // 显示上传进度
        showToast('正在上传头像...');

        final String? avatarUrl = await uploadImage(imageFile);
        if (avatarUrl != null) {
          userDict['avatar'] = avatarUrl;
          await _handleEditPersonalInfo();
          await UserManager.instance
              .updateUser((user) => user.avatar = avatarUrl);
        } else {
          showToast('头像上传失败，请重试');
        }
      }
    } catch (e) {
      debugPrint('修改头像失败: $e');
      showToast('头像上传失败，请重试');
    }
  }

  // 上传图片
  Future<String?> uploadImage(File imageFile) async {
    try {
      final response = await ApiManager()
          .uploadImage('/api/personal/file/upload/oss', imageFile.path);
      return response?['url'] as String?;
    } catch (e) {
      debugPrint('图片上传失败: $e');
      return null;
    }
  }

  //修改昵称
  void showNickNameDialog(BuildContext context) {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => NickNameDialog(
        controller: controller,
        title: '修改昵称',
        onCancel: () => Navigator.pop(context),
        onConfirm: () async {
          // 处理确认逻辑
          final nickname = controller.text.trim();
          if (nickname.isEmpty) {
            showToast('昵称不能为空');
            return;
          }

          userDict['nickname'] = nickname;
          try {
            await _handleEditPersonalInfo();
            await UserManager.instance
                .updateUser((user) => user.nickname = nickname);
            if (mounted && context.mounted) {
              Navigator.pop(context);
            }
          } catch (e) {
            debugPrint('修改昵称失败: $e');
            showToast('修改失败，请重试');
          }
        },
      ),
    );
  }

  //更改简介
  void showProfileDialog(BuildContext context) {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => NickNameDialog(
        controller: controller,
        title: '更改简介',
        onCancel: () => Navigator.pop(context),
        onConfirm: () async {
          final profile = controller.text.trim();
          userDict['profile'] = profile;

          try {
            await _handleEditPersonalInfo();
            await UserManager.instance
                .updateUser((user) => user.profile = profile);
            if (mounted && context.mounted) {
              Navigator.pop(context);
            }
          } catch (e) {
            debugPrint('修改简介失败: $e');
            showToast('修改失败，请重试');
          }
        },
      ),
    );
  }

  //修改性别
  void showGenderSelector(BuildContext context) {
    BottomSheetSelector.show(
      context: context,
      options: const ['男', '女'],
      initialSelectedIndex: _selectedGenderIndex,
      title: '选择性别', // 可选
      onSelected: (index) async {
        final sexValue = index == 0 ? '1' : '2';
        userDict['sex'] = sexValue;
        setState(() {
          _selectedGenderIndex = index;
        });

        try {
          await _handleEditPersonalInfo();
          await UserManager.instance.updateUser((user) => user.sex = sexValue);
        } catch (e) {
          debugPrint('修改性别失败: $e');
          showToast('修改失败，请重试');
        }
      },
    );
  }

  //修改城市
  Future<Result?> getResult() async {
    Result? cityPickerResult = await CityPickers.showCityPicker(
      context: context,
    );
    return cityPickerResult;
  }

  Future<void> _handleEditPersonalInfo() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.put(
        '/api/personal/edit/info',
        data: userDict,
      );

      if (response != null) {
        showToast('修改成功');
      } else {
        throw Exception('服务器返回空响应');
      }
    } catch (e) {
      debugPrint('修改个人信息失败: $e');
      showToast('修改失败，请重试');
      rethrow; // 重新抛出异常，让调用者知道操作失败
    }
  }
}
