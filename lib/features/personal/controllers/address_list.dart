import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/features/personal/controllers/address_form.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/empty_state.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';
import 'package:oktoast/oktoast.dart';

// 新增: 导入Model
import 'package:flutter_smarthome/core/models/models.dart';

class AddressListWidget extends StatefulWidget {
  final Function(AddressModel)? onAddressSelected; // 更新回调类型为AddressModel
  const AddressListWidget({super.key, this.onAddressSelected});

  @override
  State<AddressListWidget> createState() => _AddressListWidgetState();
}

class _AddressListWidgetState extends State<AddressListWidget> {
  // 地址模型列表
  List<AddressModel> _addressModels = [];

  @override
  void initState() {
    super.initState();
    _getListData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: HexColor('#F8F8F8'),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: const Text('收货地址',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold)),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: Colors.black),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      const AddressFormWidget(type: AddressFormType.create),
                ),
              ).then((result) {
                _getListData();
              });
            },
          ),
        ],
      ),
      body: SafeArea(
        child: _addressModels.isEmpty
            ? EmptyStateWidget(
                onRefresh: _getListData,
                emptyText: '暂无数据',
                buttonText: '点击刷新',
              )
            : ListView.builder(
                itemCount: _addressModels.length,
                itemBuilder: (BuildContext context, int index) {
                  final addressModel = _addressModels[index];
                  return GestureDetector(
                    onTap: () {
                      if (widget.onAddressSelected != null) {
                        Navigator.pop(context);
                        widget.onAddressSelected!(addressModel);
                      }
                    },
                    child: _buildListCell(addressModel),
                  );
                },
              ),
      ),
    );
  }

  Widget _buildListCell(AddressModel addressModel) {
    return Dismissible(
      key: Key(addressModel.id ?? ''),
      direction: DismissDirection.endToStart,
      confirmDismiss: (direction) async {
        return await showDialog(
          context: context,
          builder: (context) {
            return AlertDialog(
              title: const Text('确认删除'),
              content: const Text('您确定要删除这个地址吗？'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('取消'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('确认'),
                ),
              ],
            );
          },
        );
      },
      background: Container(
        color: Colors.red,
        alignment: Alignment.centerRight,
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: const Icon(Icons.delete, color: Colors.white),
      ),
      onDismissed: (direction) {
        final String addressId = addressModel.id ?? '';
        setState(() {
          _addressModels.removeWhere((element) => element.id == addressId);
        });
        deleteData(addressId);
      },
      child: Padding(
        padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 0.h),
        child: Container(
          width: double.infinity,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
          child: Stack(children: [
            Column(
              children: [
                SizedBox(height: 12.h),
                Row(
                  children: [
                    SizedBox(width: 16.w),
                    Text(addressModel.firstName ?? '',
                        style: TextStyle(
                            fontSize: 14.sp, fontWeight: FontWeight.bold)),
                    SizedBox(width: 8.w),
                    Text(addressModel.phoneNumber ?? '',
                        style: TextStyle(
                            fontSize: 14.sp, fontWeight: FontWeight.bold)),
                  ],
                ),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    SizedBox(width: 16.w),
                    if (addressModel.isDefault == true) ...[
                      Container(
                        padding: EdgeInsets.fromLTRB(4.w, 2.h, 4.w, 2.h),
                        decoration: BoxDecoration(
                          color: HexColor('#FFF7F0'),
                          borderRadius: BorderRadius.circular(2.r),
                        ),
                        child: Text('默认',
                            style: TextStyle(
                                color: HexColor('#FFA555'), fontSize: 10.sp)),
                      ),
                      SizedBox(width: 8.w),
                    ],
                    Text(addressModel.detailedAddress ?? '',
                        style: TextStyle(
                            fontSize: 12.sp, color: HexColor('#999999'))),
                  ],
                ),
                SizedBox(height: 12.h),
              ],
            ),
            Positioned(
              bottom: 25.h,
              right: 16.w,
              child: GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AddressFormWidget(
                          type: AddressFormType.edit,
                          addressModel: addressModel),
                    ),
                  ).then((result) {
                    _getListData();
                  });
                },
                child: Container(
                  width: 16.w,
                  height: 16.w,
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(
                          'assets/images/3.0x/icon_address_edit.png'),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
            ),
          ]),
        ),
      ),
    );
  }

  //获取数据
  Future<void> _getListData() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get('/api/shopping/address/list');
      if (mounted && response != null) {
        setState(() {
          _addressModels = _parseAddressModels(response);
        });
      }
    } catch (e) {
      debugPrint('获取地址列表失败: $e');
    }
  }

  // 将接口响应解析为 AddressModel 列表
  List<AddressModel> _parseAddressModels(dynamic response) {
    if (response is List) {
      return response
          .whereType<Map<String, dynamic>>()
          .map((e) => AddressModel.fromJson(e))
          .toList();
    }
    return [];
  }

  //删除收货地址
  Future<void> deleteData(String addressId) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.deleteWithList(
        '/api/shopping/address/remove',
        data: [addressId],
      );
      if (response != null) {
        showToast('删除成功');
      }
    } catch (e) {
      debugPrint('删除地址失败: $e');
      if (mounted) {
        showToast('删除失败，请重试');
      }
    }
  }
}
