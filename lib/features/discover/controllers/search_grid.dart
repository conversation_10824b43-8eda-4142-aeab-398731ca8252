import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/features/designer/controllers/case_detail.dart';
import 'package:flutter_smarthome/features/designer/controllers/designer_home.dart';
import 'package:flutter_smarthome/features/decoration/controllers/quote_number.dart';
import 'package:flutter_smarthome/features/shopping/controllers/shopping_business.dart';
import 'package:flutter_smarthome/features/shopping/controllers/shopping_detail.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/empty_state.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';
import 'package:flutter_smarthome/core/utils/network_image_helper.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:flutter_smarthome/core/models/designer_list_item_model.dart';
import 'package:flutter_smarthome/core/models/case_model.dart';
import 'package:flutter_smarthome/core/models/product_item.dart';
import 'package:flutter_smarthome/core/models/quote_package_model.dart';
import 'package:flutter_smarthome/core/models/business_model.dart';

class SearchGridPageWidget extends StatefulWidget {
  final List<dynamic> searchTypes; // 搜索类型
  final String searchValue; // 搜索关键字
  final VoidCallback onRefresh;

  const SearchGridPageWidget({
    super.key,
    required this.searchTypes,
    required this.searchValue,
    required this.onRefresh,
  });

  @override
  State<SearchGridPageWidget> createState() => _SearchGridPageWidgetState();
}

class _SearchGridPageWidgetState extends State<SearchGridPageWidget> {
  int pageNum = 1;
  final int pageSize = 10;
  final List<dynamic> dataList = []; // 数据源，使用具体 Model 而非 Map
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    _onRefresh();
  }

  @override
  void dispose() {
    super.dispose();
    _refreshController.dispose();
  }

  @override
  void didUpdateWidget(SearchGridPageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.searchValue != widget.searchValue) {
      _onRefresh();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        body: SafeArea(
          child: SmartRefresher(
            enablePullDown: true,
            enablePullUp: true,
            header: const WaterDropHeader(),
            footer: CustomFooter(
              builder: (BuildContext context, LoadStatus? mode) {
                Widget body;
                if (mode == LoadStatus.idle) {
                  body = const Text("上拉加载");
                } else if (mode == LoadStatus.loading) {
                  body = const CircularProgressIndicator();
                } else if (mode == LoadStatus.failed) {
                  body = const Text("加载失败！点击重试！");
                } else if (mode == LoadStatus.canLoading) {
                  body = const Text("松手加载更多");
                } else {
                  body = const Text("");
                }
                return SizedBox(
                  height: 55.0,
                  child: Center(child: body),
                );
              },
            ),
            controller: _refreshController,
            onRefresh: _onRefresh,
            onLoading: _onLoading,
            child: dataList.isEmpty
                ? EmptyStateWidget(onRefresh: _onRefresh)
                : _buildAllTypes(),
          ),
        ));
  }

  //构建整体结构
  Widget _buildAllTypes() {
    return Padding(
      padding: EdgeInsets.all(10.w),
      child: widget.searchTypes.first == 6
          ? ListView.builder(
              itemCount: dataList.length,
              itemBuilder: (BuildContext context, int index) {
                final BusinessModel item = dataList[index] as BusinessModel;
                return GestureDetector(
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => ShoppingBusinessWidget(
                                  businessId: item.businessId,
                                  businessLogo: item.businessLogo ?? "",
                                  businessName: item.businessName ?? "",
                                )));
                  },
                  child: _buildBusinessInfo(item),
                );
              },
            )
          : GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 1.0,
                crossAxisSpacing: 10.0,
                mainAxisSpacing: 10.0,
              ),
              itemCount: dataList.length,
              itemBuilder: (BuildContext context, int index) {
                switch (widget.searchTypes.first) {
                  case 1: // 设计师
                    final DesignerListItemModel item =
                        dataList[index] as DesignerListItemModel;
                    return GestureDetector(
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    DesignerHomeWidget(userId: item.userId)));
                      },
                      child: _buildDesigner(item),
                    );
                  case 2: // 案例
                    final CaseModel item = dataList[index] as CaseModel;
                    return GestureDetector(
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => CaseDetailWidget(
                                      title: item.caseTitle,
                                      caseId: item.id,
                                    )));
                      },
                      child: _buildCaseCell(item),
                    );
                  case 3: // 商品
                    final ProductItem item = dataList[index] as ProductItem;
                    return GestureDetector(
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => ShoppingDetailPageWidget(
                                    commodityId: item.id)));
                      },
                      child: _buildBusinessItem(item),
                    );
                  case 4: // 产品套餐
                    final QuotePackage item = dataList[index] as QuotePackage;
                    return GestureDetector(
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => const QuoteNumberPage(
                                    renovationType:
                                        RenovationType.fullRenovation)));
                      },
                      child: _buildPackages(item),
                    );
                  default:
                    return const Placeholder();
                }
              },
            ),
    );
  }

  //构建店铺
  Widget _buildBusinessInfo(BusinessModel item) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Container(
        height: 160.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.w),
          color: HexColor('#FFF7F0'),
        ),
        child: Column(
          children: [
            SizedBox(height: 16.h),
            Row(
              children: [
                SizedBox(width: 16.w),
                ClipRRect(
                  borderRadius: BorderRadius.circular(10.w),
                  child: NetworkImageHelper().getCachedNetworkImage(
                      imageUrl: item.businessLogo ?? "",
                      width: 20.w,
                      height: 20.w),
                ),
                SizedBox(width: 10.w),
                Text(
                  item.businessName ?? "",
                  style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                      color: HexColor('#2A2A2A')),
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.fromLTRB(10.w, 5.h, 10.w, 5.h),
                  decoration: BoxDecoration(
                    color: Colors.transparent, // 设置透明背景色
                    borderRadius: BorderRadius.circular(4.w),
                    border: Border.all(color: HexColor('#999999'), width: 0.5),
                  ),
                  child: Text(
                    '进店',
                    style:
                        TextStyle(fontSize: 13.sp, color: HexColor('#2A2A2A')),
                  ),
                ),
                SizedBox(width: 16.w),
              ],
            ),
            // Padding(
            //   padding: EdgeInsets.all(16.w),
            //   child: NetworkImageHelper().getCachedNetworkImage(imageUrl: item['bgUrl'] ?? ""),
            // )
          ],
        ),
      ),
    );
  }

  //构建产品
  Widget _buildPackages(QuotePackage item) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8.0),
          child: NetworkImageHelper()
              .getCachedNetworkImage(imageUrl: item.packagePic ?? ""),
        ),
        SizedBox(height: 5.h),
        Text(item.packageName ?? "",
            style: TextStyle(fontSize: 13.sp, color: Colors.black)),
        SizedBox(height: 5.h),
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: '100㎡仅需 ',
                style: TextStyle(
                  color: HexColor('#999999'),
                  fontSize: 12.sp,
                  fontWeight: FontWeight.normal,
                ),
              ),
              TextSpan(
                text: (item.basePrice ?? 0).toString(),
                style: TextStyle(
                  color: Colors.orange,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextSpan(
                text: ' 元起',
                style: TextStyle(
                  color: HexColor('#999999'),
                  fontSize: 12.sp,
                  fontWeight: FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
        SizedBox(
          height: 5.h,
        )
      ],
    );
  }

//构建商品
  Widget _buildBusinessItem(ProductItem item) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8.0),
          child: NetworkImageHelper().getCachedNetworkImage(
            imageUrl: item.imageUrl,
            width: double.infinity,
            height: 100.h, // 确保图片高度适中
            fit: BoxFit.cover,
          ),
        ),
        SizedBox(height: 5.h),
        Text(
          item.title,
          style: TextStyle(fontSize: 13.sp, color: Colors.black),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(height: 5.h),
        Row(
          children: [
            Expanded(
              child: Text(
                item.shop,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Text(
              '¥${item.price}',
              style: TextStyle(
                fontSize: 16.sp,
                color: HexColor('#222222'),
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(width: 5.w),
            Text(
              '积分 ${item.points}',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ],
    );
  }

  //构建设计师
  Widget _buildDesigner(DesignerListItemModel item) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8.0),
          child: NetworkImageHelper().getCachedNetworkImage(
            imageUrl: item.avatar ?? "",
            width: double.infinity,
            height: 100.h, // 确保图片高度适中
            fit: BoxFit.cover,
          ),
        ),
        SizedBox(height: 5.h),
        Text(item.realName ?? "",
            style: TextStyle(fontSize: 13.sp, color: Colors.black)),
        SizedBox(height: 5.h),
        Text(
          '${item.excelStyle.join("/")} | 案例作品 ${item.caseNumber ?? 0}套',
          style: TextStyle(color: HexColor('#999999'), fontSize: 12.sp),
        ),
        SizedBox(
          height: 5.h,
        )
      ],
    );
  }

  //构建案例
  Widget _buildCaseCell(CaseModel item) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8.0),
          child: NetworkImageHelper().getCachedNetworkImage(
            imageUrl: (item.caseMainPic.isNotEmpty
                    ? item.caseMainPic[0]
                    : "https://fileserver.gazolife.cn/2025/7/20250701_ad9a4f70848f275491d5b675baad3cb7_20250701163439A344.png")
                as String,
            width: double.infinity,
            height: 100.h, // 确保图片高度适中
            fit: BoxFit.cover,
          ),
        ),
        SizedBox(
          height: 5.h,
        ),
        Text(
          '${item.householdType ?? ""} · ${item.designStyle ?? ""}',
          style: TextStyle(color: HexColor('#CA9C72'), fontSize: 12.sp),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(
          height: 3.h,
        ),
        Text(
          item.caseTitle,
          style: TextStyle(color: Colors.black, fontSize: 13.sp),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(
          height: 3.h,
        ),
        Row(
          children: [
            ClipOval(
              child: NetworkImageHelper().getCachedNetworkImage(
                  imageUrl: item.avatar ??
                      "https://fileserver.gazolife.cn/2025/7/20250701_ad9a4f70848f275491d5b675baad3cb7_20250701163439A344.png",
                  width: 16.w,
                  height: 16.w),
            ),
            SizedBox(
              width: 5.w,
            ),
            Text(
              item.realName ?? "",
              style: TextStyle(color: HexColor('#999999'), fontSize: 12.sp),
            ),
          ],
        )
      ],
    );
  }

  Future<void> _getSearchResults() async {
    try {
      final apiManager = ApiManager();
      final response =
          await apiManager.get('/api/home/<USER>', queryParameters: {
        'searchValue': widget.searchValue,
        'searchTypes': widget.searchTypes,
        'pageNum': pageNum,
        'pageSize': pageSize,
      });

      if (response['pageTotal'] == pageNum || response['pageTotal'] == 0) {
        _refreshController.loadNoData();
      }

      if (response['rows'] != null) {
        final List<dynamic> rows = response['rows'];
        List<dynamic> arr = [];

        switch (widget.searchTypes.first) {
          case 1:
            arr = rows
                .whereType<Map<String, dynamic>>()
                .map((e) => DesignerListItemModel.fromJson(e))
                .toList();
            break;
          case 2:
            arr = rows
                .whereType<Map<String, dynamic>>()
                .map((e) => CaseModel.fromJson(e))
                .toList();
            break;
          case 3:
            arr = rows
                .whereType<Map<String, dynamic>>()
                .map((e) => ProductItem.fromJson(e))
                .toList();
            break;
          case 4:
            arr = rows
                .whereType<Map<String, dynamic>>()
                .map((e) => QuotePackage.fromJson(e))
                .toList();
            break;
          case 6:
            arr = rows
                .whereType<Map<String, dynamic>>()
                .map((e) => BusinessModel.fromJson(e))
                .toList();
            break;
          default:
            arr = [];
        }

        if (mounted) {
          setState(() {
            dataList.addAll(arr);
          });
        }
      }
    } catch (e) {
      print(e);
    }
  }

  void _onRefresh() async {
    pageNum = 1;
    dataList.clear();
    try {
      // 执行数据刷新操作
      await _getSearchResults(); // 或其他数据加载方法
      widget.onRefresh();
      _refreshController.refreshCompleted(); // 完成刷新
    } catch (e) {
      _refreshController.refreshFailed(); // 刷新失败
    }
  }

  void _onLoading() async {
    pageNum++;
    await _getSearchResults();
    if (dataList.isNotEmpty) {
      _refreshController.loadFailed();
    } else {
      _refreshController.loadComplete();
    }
  }
}
