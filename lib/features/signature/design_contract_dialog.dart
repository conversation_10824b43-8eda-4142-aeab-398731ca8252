import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/features/signature/esign_webview.dart';
import 'package:flutter_smarthome/features/signature/scan_contract_pdf.dart';
import 'package:flutter_smarthome/features/signature/signature_payment.dart';
import 'package:flutter_smarthome/shared/dialogs/discount_info_dialog.dart';
import 'package:flutter_smarthome/widgets/account_type_dialog.dart';

class DesignContractDialog extends StatefulWidget {
  final String? title;
  final String? serviceId;
  final String? customerName;
  final String? customerAddress;
  final VoidCallback? onClose;

  const DesignContractDialog({
    Key? key,
    this.title,
    this.serviceId,
    this.customerName,
    this.customerAddress,
    this.onClose,
  }) : super(key: key);

  @override
  State<DesignContractDialog> createState() => _DesignContractDialogState();

  // 静态方法来显示弹框
  static Future<T?> show<T>({
    required BuildContext context,
    String? title,
    String? serviceId,
    String? customerName,
    String? customerAddress,
    VoidCallback? onClose,
    bool barrierDismissible = true,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isDismissible: barrierDismissible,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return DesignContractDialog(
          title: title,
          serviceId: serviceId,
          customerName: customerName,
          customerAddress: customerAddress,
          onClose: onClose,
        );
      },
    );
  }
}

class _DesignContractDialogState extends State<DesignContractDialog> {
  // 静态变量用于保存用户的选择状态，在多次打开弹窗之间保持
  static final Map<String, Map<String, dynamic>?> _cachedSelectedCompany = {};
  static final Map<String, String> _cachedDesignFee = {};

  // 签约公司列表
  List<Map<String, dynamic>> companyOptions = [];
  Map<String, dynamic>? selectedCompany;
  Map<String, dynamic>? designDataSource; //设计合同
  bool eContractStatus = false;
  String? eContractId; //e签宝合同id
  Map<String, dynamic>? discountInfo; //优惠申请信息

  // 设计费输入控制器
  final TextEditingController _designFeeController = TextEditingController();

  // 加载状态
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  // 初始化所有数据
  Future<void> _initializeData() async {
    // 并行加载所有数据，但统一更新UI
    final futures = [
      _requestData(widget.serviceId!, '0'),
      _requestEContractStatus(widget.serviceId!, '0'),
      _fetchPaymentCompanies(),
      _getDiscountInfo(widget.serviceId!, '0').then((result) {
        discountInfo = result;
      })
    ];

    await Future.wait(futures);

    // 所有数据加载完成后，统一更新UI
    if (mounted) {
      setState(() {
        // 优先使用缓存的用户选择，如果没有缓存则设置默认值
        if (companyOptions.isNotEmpty) {
          _setCompanyFromCacheOrDefault();
        }
        if (designDataSource != null) {
          _setDesignFeeFromCacheOrDefault();
        }
        isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _designFeeController.dispose();
    super.dispose();
  }

  // 获取收款公司列表
  Future<void> _fetchPaymentCompanies() async {
    final apiManager = ApiManager();
    try {
      final response = await apiManager
          .get('/api/signature/company/list', queryParameters: {});

      if (response != null && response is List) {
        companyOptions = List<Map<String, dynamic>>.from(response);
      }
    } catch (e) {
      debugPrint('Failed to load payment companies: $e');
      // 错误处理可以在统一的地方处理
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // 点击空白区域收起键盘
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Align(
        alignment: Alignment.bottomCenter,
        child: Material(
          color: Colors.transparent,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: double.infinity,
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.7,
              minHeight: MediaQuery.of(context).size.height * 0.4,
            ),
            margin: EdgeInsets.only(
              // 当键盘弹起时，调整底部边距避免遮挡
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.0),
                topRight: Radius.circular(20.0),
              ),
            ),
            child: Stack(
              children: [
                // 主要内容
                SingleChildScrollView(
                  // 确保内容可以滚动，避免键盘遮挡
                  padding: EdgeInsets.only(
                    left: 20.0,
                    right: 20.0,
                    top: 20.0,
                    bottom: 20.0 + MediaQuery.of(context).viewInsets.bottom,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 20.h),

                      // 标题
                      Text(
                        widget.title ?? '设计合同',
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 24.h),

                      // 根据签约状态显示不同内容
                      _buildMainContent(),

                      // 底部按钮
                      _buildBottomButton(),
                      SizedBox(height: 20.h),
                    ],
                  ),
                ),

                // 右上角关闭按钮
                Positioned(
                  top: 16.0,
                  right: 16.0,
                  child: GestureDetector(
                    onTap: () {
                      // 先收起键盘
                      FocusScope.of(context).unfocus();
                      // 然后关闭弹窗
                      if (widget.onClose != null) {
                        widget.onClose!();
                      } else {
                        Navigator.of(context).pop();
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.all(4.0),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.grey[200],
                      ),
                      child: const Icon(
                        Icons.close,
                        size: 20.0,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建主要内容
  Widget _buildMainContent() {
    // 如果还在加载中，显示加载状态
    if (isLoading) {
      return SizedBox(
        height: 200.h,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              SizedBox(height: 16.h),
              Text(
                '正在加载合同信息...',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // 获取签约状态
    bool alreadySign = designDataSource?['alreadySign'] ?? false;

    // 如果已签约，显示合同信息
    if (alreadySign) {
      return _buildSignedContractInfo();
    }
    // 如果未签约，显示合同编辑界面
    else {
      return _buildContractEditForm();
    }
  }

  // 构建已签约合同信息显示
  Widget _buildSignedContractInfo() {
    List<Widget> children = [
      // 签约公司
      _buildInfoRow('签约公司', designDataSource?['incomeCompanyName'] ?? ''),
      SizedBox(height: 16.h),

      // 设计费
      _buildInfoRow('设计费', '${designDataSource?['originalPrice'] ?? 0}'),
      SizedBox(height: 16.h),
    ];

    // 如果有优惠信息，显示优惠金额和设计费合计
    if (discountInfo != null) {
      children.addAll([
        // 优惠金额
        _buildInfoRow('优惠金额', '${discountInfo?['discountPrice'] ?? 0}'),
        SizedBox(height: 16.h),

        // 设计费合计
        _buildInfoRow('设计费合计', _calculateTotal()),
        SizedBox(height: 16.h),
      ]);
    }

    children.add(SizedBox(height: 40.h));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }

  // 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '$label:',
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.grey[600],
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  // 计算合计金额
  String _calculateTotal() {
    double originalPrice = double.tryParse(
            designDataSource?['originalPrice']?.toString() ?? '0') ??
        0;
    double discountAmount =
        double.tryParse(discountInfo?['discountPrice']?.toString() ?? '0') ?? 0;
    double total = originalPrice - discountAmount;
    return total.toStringAsFixed(0);
  }

  // 计算编辑表单中的合计金额（使用输入框中的设计费）
  String _calculateTotalForEdit() {
    double originalPrice = double.tryParse(_designFeeController.text) ?? 0;
    double discountAmount =
        double.tryParse(discountInfo?['discountPrice']?.toString() ?? '0') ?? 0;
    double total = originalPrice - discountAmount;
    return total.toStringAsFixed(0);
  }

  // 构建只读字段
  Widget _buildReadOnlyField(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black,
          ),
        ),
        SizedBox(height: 8.h),
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 16.h),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
        ),
      ],
    );
  }

  // 构建合同编辑表单
  Widget _buildContractEditForm() {
    List<Widget> children = [
      // 签约公司选择
      _buildCompanySelector(),
      SizedBox(height: 20.h),

      // 设计费输入
      _buildDesignFeeInput(),
      SizedBox(height: 16.h),
    ];

    // 如果有优惠信息，显示优惠金额和设计费合计
    if (discountInfo != null) {
      children.addAll([
        // 优惠金额（不可编辑）
        _buildReadOnlyField(
            '优惠金额', _formatDiscountPrice(discountInfo?['discountPrice'])),
        SizedBox(height: 16.h),

        // 设计费合计（不可编辑）
        _buildReadOnlyField('设计费合计', _calculateTotalForEdit()),
        SizedBox(height: 16.h),
      ]);
    }

    children.addAll([
      // 申请优惠按钮
      _buildDiscountButton(),
      SizedBox(height: 40.h),
    ]);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }

  // 构建签约公司选择器
  Widget _buildCompanySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '签约公司',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black,
          ),
        ),
        SizedBox(height: 8.h),
        GestureDetector(
          onTap: isLoading ? null : _showCompanyPicker,
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8.r),
              color: isLoading ? Colors.grey[100] : Colors.white,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    isLoading
                        ? '加载中...'
                        : (selectedCompany?['companyName'] ?? ''),
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: isLoading || selectedCompany == null
                          ? Colors.grey[600]
                          : Colors.black,
                    ),
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.grey[600],
                  size: 20.w,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 构建设计费输入框
  Widget _buildDesignFeeInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '设计费',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black,
          ),
        ),
        SizedBox(height: 8.h),
        TextField(
          controller: _designFeeController,
          keyboardType: TextInputType.number,
          // 添加文本输入动作，当用户点击完成时收起键盘
          textInputAction: TextInputAction.done,
          onChanged: (value) {
            // 保存用户输入到缓存
            _cachedDesignFee[widget.serviceId!] = value;
            // 当输入内容变化时，更新UI以刷新设计费合计和申请优惠按钮状态
            setState(() {});
          },
          onSubmitted: (value) {
            // 当用户点击完成按钮时，收起键盘
            FocusScope.of(context).unfocus();
          },
          decoration: InputDecoration(
            hintText: '请输入设计费金额',
            hintStyle: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: const BorderSide(color: Colors.black),
            ),
            contentPadding:
                EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            suffixText: '元',
            suffixStyle: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
            // 添加白色背景
            fillColor: Colors.white,
            filled: true,
          ),
        ),
      ],
    );
  }

  // 检查设计费是否有效
  bool _isDesignFeeValid() {
    String designFeeText = _designFeeController.text.trim();
    if (designFeeText.isEmpty) return false;

    double? designFee = double.tryParse(designFeeText);
    return designFee != null && designFee > 0;
  }

  // 构建申请优惠按钮
  Widget _buildDiscountButton() {
    // 检查设计费是否已填写
    bool isDesignFeeValid = _isDesignFeeValid();

    return Align(
      alignment: Alignment.centerLeft,
      child: GestureDetector(
        onTap: isDesignFeeValid ? _applyDiscount : null,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          decoration: BoxDecoration(
            border: Border.all(
              color: isDesignFeeValid ? Colors.grey[400]! : Colors.grey[300]!,
            ),
            borderRadius: BorderRadius.circular(20.r),
            color: isDesignFeeValid ? Colors.transparent : Colors.grey[100],
          ),
          child: Text(
            '申请优惠',
            style: TextStyle(
              fontSize: 14.sp,
              color: isDesignFeeValid ? Colors.black : Colors.grey[400],
            ),
          ),
        ),
      ),
    );
  }

  // 构建底部按钮
  Widget _buildBottomButton() {
    // 如果还在加载中，显示加载状态
    if (isLoading) {
      return SizedBox(
        width: double.infinity,
        height: 44.h,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(22.r),
          ),
          child: Center(
            child: SizedBox(
              width: 20.w,
              height: 20.h,
              child: const CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
              ),
            ),
          ),
        ),
      );
    }

    // 获取签约状态
    bool alreadySign = designDataSource?['alreadySign'] ?? false;

    // 情况1：已签约，显示查看合同和去支付按钮
    if (alreadySign) {
      return Row(
        children: [
          // 查看合同按钮
          Expanded(
            child: _buildActionButton(
              text: '查看合同',
              onTap: () => _showContractPicPicker(
                  designDataSource?['contractPic'] as List<dynamic>?,
                  eContractId ?? ''),
              isOutlined: true,
            ),
          ),
          SizedBox(width: 8.w),
          // 去支付按钮
          Expanded(
            child: _buildActionButton(
              text: '去支付',
              onTap: () {
                // TODO: 去支付
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => SignaturePaymentWidget(
                            contractData: designDataSource!,
                            customerName: widget.customerName,
                            customerAddress: widget.customerAddress,
                          )),
                );
              },
              isOutlined: false,
            ),
          ),
        ],
      );
    }
    // 情况2：都是false，只显示拟合同按钮
    else if (!alreadySign && !eContractStatus) {
      return SizedBox(
        width: double.infinity,
        height: 44.h,
        child: ElevatedButton(
          onPressed: _submitContract,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.black,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(22.r),
            ),
          ),
          child: Text(
            '拟合同',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      );
    }
    // 情况2：alreadySign是false但eContractStatus是true，显示3个按钮
    else if (!alreadySign && eContractStatus) {
      return Column(
        children: [
          Row(
            children: [
              // 拟合同按钮
              Expanded(
                child: _buildActionButton(
                  text: '拟合同',
                  onTap: _submitContract,
                  isOutlined: true,
                ),
              ),
              SizedBox(width: 8.w),
              // 查看合同按钮
              Expanded(
                child: _buildActionButton(
                  text: '查看合同',
                  onTap: _viewContract,
                  isOutlined: true,
                ),
              ),
              SizedBox(width: 8.w),
              // 去签约按钮
              Expanded(
                child: _buildActionButton(
                  text: '去签约',
                  onTap: () {
                    showDialog(
                      context: context,
                      builder: (context) => AccountTypeDialog(
                        initialValue: 'private',
                        onConfirm: (selectedType,
                            {String? operatorName, String? operatorPhone}) {
                          // 处理确认逻辑
                          debugPrint('选择的账户类型：$selectedType');
                          if (selectedType == 'public') {
                            debugPrint('经办人姓名：$operatorName');
                            debugPrint('经办人电话：$operatorPhone');
                          }
                          // 调用签约接口
                          _requestDirectSign(
                            eContractId ?? '',
                            selectedType == 'private' ? '0' : '1',
                            operatorName ?? '',
                            operatorPhone ?? '',
                          );
                        },
                      ),
                    );
                  },
                  isOutlined: false,
                ),
              ),
            ],
          ),
        ],
      );
    }
    // 其他情况，保持原有逻辑（已签约等）
    else {
      return SizedBox(
        width: double.infinity,
        height: 44.h,
        child: ElevatedButton(
          onPressed: _submitContract,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.black,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(22.r),
            ),
          ),
          child: Text(
            '拟合同',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      );
    }
  }

  // 构建操作按钮
  Widget _buildActionButton({
    required String text,
    required VoidCallback onTap,
    required bool isOutlined,
  }) {
    return SizedBox(
      height: 44.h,
      child: ElevatedButton(
        onPressed: onTap,
        style: ElevatedButton.styleFrom(
          backgroundColor: isOutlined ? Colors.white : Colors.black,
          foregroundColor: isOutlined ? Colors.black : Colors.white,
          elevation: 0,
          side: isOutlined ? BorderSide(color: Colors.grey.shade300) : null,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(22.r),
          ),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  // 显示签约公司选择器
  void _showCompanyPicker() {
    if (companyOptions.isEmpty) return;

    // 获取公司名称列表
    List<String> companyNames = companyOptions
        .map((option) => option['companyName'] ?? '')
        .toList()
        .cast<String>();

    // 获取当前选中的索引
    String currentValue = selectedCompany?['companyName'] ?? '';
    int selectedIndex = companyNames.indexOf(currentValue);
    if (selectedIndex == -1) selectedIndex = 0;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 300.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
        ),
        child: Column(
          children: [
            // 拖拽指示器
            Container(
              width: 36.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 8.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),

            // 标题栏
            Container(
              height: 50.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey[200]!,
                    width: 1.h,
                  ),
                ),
              ),
              child: Row(
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      '取消',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16.sp,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      '选择签约公司',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      // 根据选中的索引更新选中的公司
                      if (selectedIndex >= 0 &&
                          selectedIndex < companyOptions.length) {
                        setState(() {
                          selectedCompany = companyOptions[selectedIndex];
                          // 保存用户选择到缓存
                          _cachedSelectedCompany[widget.serviceId!] =
                              selectedCompany;
                        });
                      }
                    },
                    child: Text(
                      '确定',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 选择器
            Expanded(
              child: CupertinoPicker(
                itemExtent: 44.h,
                scrollController:
                    FixedExtentScrollController(initialItem: selectedIndex),
                onSelectedItemChanged: (index) {
                  selectedIndex = index;
                },
                children: companyNames
                    .map(
                      (companyName) => Center(
                        child: Text(
                          companyName,
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 优先从缓存设置公司，如果没有缓存则设置默认值
  void _setCompanyFromCacheOrDefault() {
    if (companyOptions.isEmpty) return;

    final serviceId = widget.serviceId!;
    final cachedCompany = _cachedSelectedCompany[serviceId];

    // 如果有缓存的公司选择，尝试恢复
    if (cachedCompany != null) {
      final matchedCompany = companyOptions.firstWhere(
        (company) => company['id'] == cachedCompany['id'],
        orElse: () => {},
      );

      if (matchedCompany.isNotEmpty) {
        selectedCompany = matchedCompany;
        return;
      }
    }

    // 如果没有缓存或缓存的公司不存在，使用默认逻辑
    _setDefaultCompany();
  }

  // 设置默认选中的公司
  void _setDefaultCompany() {
    if (companyOptions.isEmpty) return;

    // 如果 designDataSource 中有 incomeCompanyName，尝试匹配
    if (designDataSource != null &&
        designDataSource!['incomeCompanyName'] != null) {
      final targetCompanyName = designDataSource!['incomeCompanyName'];
      final matchedCompany = companyOptions.firstWhere(
        (company) => company['companyName'] == targetCompanyName,
        orElse: () => companyOptions.first,
      );
      selectedCompany = matchedCompany;
    } else {
      // 如果没有指定公司名称，不默认选择任何公司
      selectedCompany = null;
    }
  }

  // 优先从缓存设置设计费，如果没有缓存则设置默认值
  void _setDesignFeeFromCacheOrDefault() {
    final serviceId = widget.serviceId!;
    final cachedDesignFee = _cachedDesignFee[serviceId];

    // 如果有缓存的设计费，使用缓存值
    if (cachedDesignFee != null && cachedDesignFee.isNotEmpty) {
      _designFeeController.text = cachedDesignFee;
      return;
    }

    // 如果没有缓存，使用默认逻辑
    _setDefaultDesignFee();
  }

  // 设置默认设计费
  void _setDefaultDesignFee() {
    if (designDataSource != null &&
        designDataSource!['originalPrice'] != null) {
      _designFeeController.text = designDataSource!['originalPrice'].toString();
    }
  }

  // 申请优惠
  void _applyDiscount() async {
    // 调用对话框，等待弹窗关闭
    await DiscountInfoDialog.show(
      widget.serviceId,
      '0',
      originalPrice: _designFeeController.text,
      context,
      onSubmit: (discountType, packageType, discountAmount, discountContent) {
        // 这里可以处理提交时的逻辑，如果需要的话
      },
    );

    // 弹窗关闭后刷新数据
    if (mounted) {
      await _refreshDataAfterSign();
    }
  }

  // 设计合同录入
  void _submitContract() {
    // 验证输入
    if (selectedCompany == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请选择签约公司')),
      );
      return;
    }

    if (_designFeeController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入设计费金额')),
      );
      return;
    }

    _submitContractAsync();
  }

  // 异步提交合同
  Future<void> _submitContractAsync() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.post(
        '/api/signature/design/contract/commit',
        data: {
          'customerProjectServiceId': widget.serviceId,
          'originalPrice': _designFeeController.text,
          'incomeCompany': selectedCompany?['id'] ?? ''
        },
      );

      if (response != null && mounted) {
        // 跳转到签约页面
        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ESignWebViewWidget(
              esignContractId: eContractId,
              contractType: '0',
              serviceId: widget.serviceId ?? '',
            ),
          ),
        );

        // 从签约页面返回后刷新数据，但不关闭弹窗
        if (mounted) {
          await _refreshDataAfterSign();
        }
      }
    } catch (e) {
      debugPrint('提交合同失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('提交失败: $e')),
        );
      }
    }
  }

  // 查看合同
  void _viewContract() async {
    if (eContractId != null) {
      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ScanContractPDFWidget(
            esignContractId: eContractId!,
            contractUrl: '',
            isDirectLoad: false,
          ),
        ),
      );

      // 从查看合同页面返回后刷新数据（如果在查看页面进行了签约操作）
      if (mounted) {
        await _refreshDataAfterSign();
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('合同ID不存在，无法查看合同')),
      );
    }
  }

  // 显示合同图片选择器底部弹窗
  void _showContractPicPicker(
      List<dynamic>? contractPics, String esignContractId) {
    if (contractPics == null || contractPics.isEmpty) {
      // 如果没有合同图片，直接显示提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('没有可用的合同文件')),
      );
      return;
    }

    // 创建一个可以保存选中索引的变量
    int selectedIndex = 0;

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 250.h,
          padding: EdgeInsets.all(16.r),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '选择合同文件',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: const Icon(Icons.close),
                  ),
                ],
              ),
              const Divider(),
              Expanded(
                child: CupertinoPicker(
                  magnification: 1.2,
                  squeeze: 1.2,
                  useMagnifier: true,
                  itemExtent: 40,
                  scrollController: FixedExtentScrollController(initialItem: 0),
                  onSelectedItemChanged: (int index) {
                    // 保存选中的索引
                    selectedIndex = index;
                  },
                  children:
                      List<Widget>.generate(contractPics.length, (int index) {
                    final filename = _getFilenameFromUrl(contractPics[index]);
                    return Center(
                      child: Text(
                        '合同文件 ${index + 1}: $filename',
                        style: TextStyle(fontSize: 14.sp),
                      ),
                    );
                  }),
                ),
              ),
              SizedBox(height: 10.h),
              GestureDetector(
                onTap: () {
                  Navigator.pop(context); // 关闭选择器
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ScanContractPDFWidget(
                        esignContractId: esignContractId,
                        contractUrl: contractPics[selectedIndex],
                        isDirectLoad: true, // 直接加载模式
                      ),
                    ),
                  );
                },
                child: Container(
                  width: double.infinity,
                  height: 40.h,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Center(
                    child: Text(
                      '确定',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16.sp,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 从URL中提取文件名
  String _getFilenameFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;
      if (pathSegments.isNotEmpty) {
        return pathSegments.last.length > 15
            ? '${pathSegments.last.substring(0, 15)}...'
            : pathSegments.last;
      }
    } catch (e) {
      // 解析URL出错时
    }
    return '未命名文件';
  }

  //去签约
  Future<void> _requestDirectSign(
      String eid, String type, String agentName, String agentPhone) async {
    if (widget.serviceId == null) return;

    final apiManager = ApiManager();
    try {
      final response = await apiManager.post(
        '/api/signature/direct/sign',
        data: {
          'esignContractId': eid,
          'psnOrOrg': type,
          'agentName': agentName,
          'agentPhone': agentPhone,
        },
      );

      if (response != null) {
        // 显示提示弹窗
        _showSuccessDialog();
      }
    } catch (e) {
      // 签约失败处理
    }
  }

  // 显示成功提示弹窗
  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('提示'),
          content: const Text('已将电子合同链接通过短信形式发送至客户手机，请及时提醒客户完成合同签署，链接有效期三小时'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭弹窗
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  // 从签约页面返回后刷新数据
  Future<void> _refreshDataAfterSign() async {
    debugPrint('开始刷新签约后的数据...');

    // 保存当前用户选择的公司，避免刷新后被重置
    final currentSelectedCompany = selectedCompany;

    // 重新获取所有相关数据
    final futures = [
      _requestData(widget.serviceId!, '0'),
      _requestEContractStatus(widget.serviceId!, '0'),
      _getDiscountInfo(widget.serviceId!, '0').then((result) {
        discountInfo = result;
      })
    ];

    await Future.wait(futures);

    // 刷新UI
    if (mounted) {
      setState(() {
        // 如果用户之前已经选择了公司，保持用户的选择
        if (currentSelectedCompany != null && companyOptions.isNotEmpty) {
          // 检查用户之前选择的公司是否还在公司列表中
          final matchedCompany = companyOptions.firstWhere(
            (company) => company['id'] == currentSelectedCompany['id'],
            orElse: () => {},
          );

          if (matchedCompany.isNotEmpty) {
            // 如果找到匹配的公司，保持用户的选择
            selectedCompany = matchedCompany;
            // 更新缓存
            _cachedSelectedCompany[widget.serviceId!] = selectedCompany;
          } else {
            // 如果用户之前选择的公司不在列表中，重新设置默认值
            _setDefaultCompany();
            // 更新缓存
            _cachedSelectedCompany[widget.serviceId!] = selectedCompany;
          }
        } else {
          // 如果用户之前没有选择公司，设置默认值
          if (companyOptions.isNotEmpty) {
            _setDefaultCompany();
            // 更新缓存
            _cachedSelectedCompany[widget.serviceId!] = selectedCompany;
          }
        }

        // 保持设计费不变，不重新设置默认值
        // 因为用户可能已经修改了设计费，我们应该保持用户的输入
      });

      debugPrint(
          '数据刷新完成，签约状态: ${designDataSource?['alreadySign']}, E签宝状态: $eContractStatus');
    }
  }

  //合同详情
  Future<void> _requestData(String serviceId, String contractType) async {
    final apiManager = ApiManager();
    final response = await apiManager.get(
      '/api/signature/naked/contract',
      queryParameters: {'serviceId': serviceId, 'contractType': contractType},
    );

    if (response != null && mounted) {
      designDataSource = response;
    }
  }

  //E 签宝合同状态
  Future<void> _requestEContractStatus(
      String serviceId, String contractType) async {
    final apiManager = ApiManager();
    final response = await apiManager.get(
      '/api/signature/contract/logic',
      queryParameters: {'serviceId': serviceId, 'contractType': contractType},
    );

    debugPrint('E签宝合同状态响应: $response');

    if (response != null && mounted) {
      eContractId = response['id'];
      debugPrint('获取到的合同ID: $eContractId');
      // 修复：明确设置状态，而不只是在特定条件下设置为true
      eContractStatus =
          response['isSign'] == '0' || response['isSign'] == '1'; //已填写且未签约
      debugPrint(
          '合同状态: eContractStatus=$eContractStatus, isSign=${response['isSign']}');
    }
  }

  //获取优惠申请信息
  Future<Map<String, dynamic>?> _getDiscountInfo(
      String serviceId, String contractType) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/discount/info',
        queryParameters: {'serviceId': serviceId, 'contractType': 0},
      );
      if (response != null) {
        return response;
      }
      return null;
    } catch (e) {
      debugPrint('获取优惠信息失败: $e');
      return null;
    }
  }

  // 格式化优惠价格，保留2位小数
  String _formatDiscountPrice(dynamic price) {
    if (price == null) return '0.00';

    // 转换为数字
    double priceNum = 0.0;
    if (price is num) {
      priceNum = price.toDouble();
    } else if (price is String) {
      if (price.isEmpty) {
        priceNum = 0.0;
      } else {
        priceNum = double.tryParse(price) ?? 0.0;
      }
    }

    // 格式化为2位小数
    return priceNum.toStringAsFixed(2);
  }
}
