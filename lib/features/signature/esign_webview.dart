import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

class ESignWebViewWidget extends StatefulWidget {
  final String? contractType;
  final String? serviceId;
  final String? esignContractId;
  final String? budgetId;
  const ESignWebViewWidget({
    super.key,
    required this.contractType,
    required this.serviceId,
    required this.esignContractId,
    this.budgetId,
  });

  @override
  State<ESignWebViewWidget> createState() => _ESignWebViewWidgetState();
}

class _ESignWebViewWidgetState extends State<ESignWebViewWidget> {
  late WebViewController _webViewController;
  String? webViewUrl;
  bool isLoading = true;
  final timestamp = DateTime.now().millisecondsSinceEpoch;

  // WebSocket 相关变量
  WebSocketChannel? _webSocketChannel;
  String? _wsUrl;
  bool _isWsConnected = false;

  @override
  void initState() {
    super.initState();
    _initWebView();
    _initWebSocket();
    _loadWebView();
  }

  void _initWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              isLoading = false;
            });
          },
        ),
      );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('填写补全合同'),
        backgroundColor: Colors.white,
        foregroundColor: const Color.fromRGBO(0, 0, 0, 1),
        elevation: 0,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
      ),
      body: SafeArea(
        child: Stack(
          children: [
            if (webViewUrl != null)
              WebViewWidget(controller: _webViewController)
            else
              const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('正在加载合同数据...'),
                  ],
                ),
              ),
            if (isLoading && webViewUrl != null)
              Container(
                color: Colors.white.withOpacity(0.8),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('页面加载中...'),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _disconnectWebSocket();
    super.dispose();
  }

  /// 初始化 WebSocket 连接
  void _initWebSocket() {
    _generateWebSocketUrl();
    _connectWebSocket();
  }

  /// 生成 WebSocket URL
  void _generateWebSocketUrl() {
    if (kDebugMode) {
      // Debug 环境
      _wsUrl = 'ws://erf.gazo.net.cn:8087/test-api/message/ws/$timestamp';
    } else {
      // Release 环境
      _wsUrl = 'wss://erf.zglife.com.cn/prod-api/message/ws/$timestamp';
    }

    debugPrint('WebSocket URL: $_wsUrl');
  }

  /// 连接 WebSocket
  void _connectWebSocket() {
    if (_wsUrl == null || !mounted) return;

    try {
      _webSocketChannel = WebSocketChannel.connect(Uri.parse(_wsUrl!));
      _isWsConnected = true;

      debugPrint('WebSocket 连接成功');

      // 监听 WebSocket 消息
      _webSocketChannel!.stream.listen(
        (message) {
          if (mounted) {
            debugPrint('WebSocket 收到消息: $message');
            _handleWebSocketMessage(message);
          }
        },
        onError: (error) {
          debugPrint('WebSocket 错误: $error');
          if (mounted) {
            _isWsConnected = false;
            _reconnectWebSocket();
          }
        },
        onDone: () {
          debugPrint('WebSocket 连接关闭');
          if (mounted) {
            _isWsConnected = false;
            _reconnectWebSocket();
          }
        },
        cancelOnError: false, // 不要在错误时取消订阅
      );
    } catch (e) {
      debugPrint('WebSocket 连接失败: $e');
      if (mounted) {
        _isWsConnected = false;
        _reconnectWebSocket();
      }
    }
  }

  /// 处理 WebSocket 消息
  void _handleWebSocketMessage(dynamic message) {
    try {
      // 这里可以根据实际需求处理不同类型的消息
      debugPrint('处理 WebSocket 消息: $message');

      // 检查是否是填写完成的消息
      if (message.toString().contains('"key":"FILL_DOCTEMPLATE"')) {
        debugPrint('收到填写完成消息，2秒后自动关闭页面');

        // 先断开 WebSocket 连接
        _disconnectWebSocket();

        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            // 连续 pop 两次
            Navigator.of(context).pop();
            Future.delayed(const Duration(milliseconds: 100), () {
              if (mounted && Navigator.of(context).canPop()) {
                Navigator.of(context).pop();
              }
            });
          }
        });
        return;
      }

      // 例如：如果收到合同状态更新消息，可以刷新 WebView
      if (message.toString().contains('contract_updated')) {
        if (mounted && webViewUrl != null) {
          _webViewController.reload();
        }
      }
    } catch (e) {
      debugPrint('处理 WebSocket 消息时出错: $e');
    }
  }

  /// 重连 WebSocket
  void _reconnectWebSocket() {
    if (!mounted) return;

    Future.delayed(const Duration(seconds: 5), () {
      if (mounted && !_isWsConnected) {
        debugPrint('WebSocket 尝试重连...');
        _connectWebSocket();
      }
    });
  }

  /// 断开 WebSocket 连接
  void _disconnectWebSocket() {
    if (_webSocketChannel != null) {
      try {
        // 使用标准的正常关闭状态码 1000，而不是 1001
        _webSocketChannel!.sink.close(1000, 'Normal closure');
      } catch (e) {
        debugPrint('关闭 WebSocket 时出错: $e');
        // 如果正常关闭失败，尝试强制关闭
        try {
          _webSocketChannel!.sink.close();
        } catch (e2) {
          debugPrint('强制关闭 WebSocket 时出错: $e2');
        }
      } finally {
        _webSocketChannel = null;
        _isWsConnected = false;
        debugPrint('WebSocket 连接已断开');
      }
    }
  }

  // 获取重写合同的数据
  Future<void> _loadWebView() async {
    try {
      final apiManager = ApiManager();
      // 调用API获取WebView数据
      final response = await apiManager.post(
        '/api/signature/contract/webview',
        data: {
          'contractType': widget.contractType ?? '',
          'serviceId': widget.serviceId,
          'timestamp': timestamp,
          'budgetId': widget.budgetId,
          'esignContractId': widget.esignContractId,
        },
      );

      if (response != null && response['docTemplateFillUrl'] != null) {
        setState(() {
          webViewUrl = response['docTemplateFillUrl'];
        });
        _webViewController.loadRequest(Uri.parse(webViewUrl!));
      }
    } catch (e) {
      debugPrint('加载WebView失败: $e');
      // 处理错误
    }
  }
}
