import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/empty_state.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';
import 'package:flutter_smarthome/core/models/models.dart';
import 'package:flutter_smarthome/features/signature/signature_add_user.dart';
import 'package:flutter_smarthome/features/signature/signature_detail.dart';
import 'package:flutter_smarthome/features/signature/widgets/signature_filter_dialog.dart';
import 'package:flutter_smarthome/features/signature/widgets/signature_search_dialog.dart';
import 'package:flutter_smarthome/shared/dialogs/channel_meeting_dialog.dart';
import 'package:flutter_smarthome/shared/dialogs/customer_docking_dialog.dart';
import 'package:flutter_smarthome/shared/dialogs/dispatch_order_dialog.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class MySignatureListWidget extends StatefulWidget {
  const MySignatureListWidget({super.key});

  @override
  State<MySignatureListWidget> createState() => _MySignatureListWidgetState();
}

class _MySignatureListWidgetState extends State<MySignatureListWidget> {
  List<SignatureModel> signatureList = [];

  // 分页相关变量
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  int pageNum = 1;
  final int pageSize = 10;
  int pageTotal = 0; // 添加总页数变量

  // 筛选相关变量
  String? selectedFilter;

  // 查询相关变量
  Map<String, dynamic> searchParams = {};

  @override
  void initState() {
    super.initState();
    _getSignatureList();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          '我的订单',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
      ),
      body: Column(
        children: [
          // 查询和筛选按钮行
          Row(
            children: [
              SizedBox(
                width: 16.w,
              ),
              Text(
                '订单列表',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) =>
                              const SignatureAddUserWidget()));
                },
                child: Row(
                  children: [
                    Image.asset(
                      'assets/images/3.0x/icon_signature_add.png',
                      width: 16.w,
                      height: 16.h,
                    ),
                    SizedBox(
                      width: 4.w,
                    ),
                    Text(
                      '新增',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 16.w,
              ),
              //右边两个按钮，按钮都是图片带文字的，分别是查询和筛选按钮
              GestureDetector(
                onTap: () {
                  showModalBottomSheet(
                    context: context,
                    backgroundColor: Colors.transparent,
                    isScrollControlled: true,
                    useSafeArea: false,
                    builder: (context) => Padding(
                      padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom,
                      ),
                      child: SignatureSearchDialog(
                        initialSearchParams: searchParams,
                        onSearchConfirmed: (params) {
                          // 设置搜索参数并重新加载数据
                          searchParams = params;
                          _onRefresh();
                        },
                      ),
                    ),
                  );
                },
                child: Row(
                  children: [
                    Image.asset(
                      'assets/images/signature/icon_signature_search.png',
                      width: 16.w,
                      height: 16.h,
                    ),
                    SizedBox(
                      width: 4.w,
                    ),
                    Text(
                      searchParams.isNotEmpty ? '查询中' : '查询',
                      style: TextStyle(
                        color: searchParams.isNotEmpty
                            ? HexColor('#FFAF2A')
                            : Colors.black,
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 16.w,
              ),
              GestureDetector(
                onTap: () {
                  showModalBottomSheet(
                    context: context,
                    backgroundColor: Colors.transparent,
                    isScrollControlled: true,
                    useSafeArea: false,
                    builder: (context) => Padding(
                      padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom,
                      ),
                      child: SignatureFilterDialog(
                        selectedFilter: selectedFilter,
                        onFilterSelected: (filter) {
                          // 设置筛选条件并重新加载数据
                          selectedFilter = filter;
                          _onRefresh();
                        },
                      ),
                    ),
                  );
                },
                child: Row(
                  children: [
                    Image.asset(
                      'assets/images/signature/icon_signature_filter.png',
                      width: 16.w,
                      height: 16.h,
                    ),
                    SizedBox(
                      width: 4.w,
                    ),
                    Text(
                      selectedFilter != null && selectedFilter!.isNotEmpty
                          ? '筛选中'
                          : '筛选',
                      style: TextStyle(
                        color:
                            selectedFilter != null && selectedFilter!.isNotEmpty
                                ? HexColor('#FFAF2A')
                                : Colors.black,
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 16.w,
              ),
            ],
          ),
          SizedBox(
            height: 8.h,
          ),
          // SmartRefresher 包裹列表内容
          Expanded(
            child: SmartRefresher(
              enablePullDown: true,
              enablePullUp: true,
              header: const WaterDropHeader(),
              footer: CustomFooter(
                builder: (BuildContext context, LoadStatus? mode) {
                  Widget body;
                  if (mode == LoadStatus.idle) {
                    body = const Text("上拉加载");
                  } else if (mode == LoadStatus.loading) {
                    body = const CircularProgressIndicator();
                  } else if (mode == LoadStatus.failed) {
                    body = const Text("加载失败！点击重试！");
                  } else if (mode == LoadStatus.canLoading) {
                    body = const Text("松手加载更多");
                  } else if (mode == LoadStatus.noMore) {
                    body = const Text("没有更多数据了");
                  } else {
                    body = const Text("");
                  }
                  return SizedBox(
                    height: 55.0,
                    child: Center(child: body),
                  );
                },
              ),
              controller: _refreshController,
              onRefresh: _onRefresh,
              onLoading: _onLoading,
              child: signatureList.isEmpty
                  ? EmptyStateWidget(
                      onRefresh: _onRefresh,
                      emptyText: '暂无数据',
                      buttonText: '点击刷新',
                    )
                  : ListView.builder(
                      itemCount: signatureList.length,
                      padding:
                          EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                      itemBuilder: (context, index) {
                        // 添加边界检查，防止数组越界
                        if (index >= signatureList.length) {
                          return const SizedBox.shrink();
                        }
                        final item = signatureList[index];
                        return GestureDetector(
                            onTap: () {
                              // TODO: 跳转到详情页
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => SignatureDetailWidget(
                                        serviceId: item.serviceId ?? '')),
                              );
                            },
                            child: Container(
                              margin: EdgeInsets.only(bottom: 16.h),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8.r),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withValues(alpha: 0.2),
                                    spreadRadius: 2,
                                    blurRadius: 5,
                                    offset: const Offset(0, 3),
                                  ),
                                ],
                              ),
                              child: Column(
                                children: [
                                  SizedBox(
                                    height: 16.h,
                                  ),
                                  Row(
                                    children: [
                                      SizedBox(
                                        width: 16.w,
                                      ),
                                      Text(
                                        item.customerName ?? '', //客户姓名
                                        style: TextStyle(
                                          color: Colors.black,
                                          fontSize: 14.sp,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 16.w,
                                      ),
                                      Text(
                                        item.maskedPhone, //客户电话
                                        style: TextStyle(
                                          color: HexColor('#999999'),
                                          fontSize: 12.sp,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 8.w,
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          // 跳转到新增用户页面，传递当前数据作为默认值
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  SignatureAddUserWidget(
                                                dataSource: {
                                                  'customerName':
                                                      item.customerName,
                                                  'customerPhone':
                                                      item.customerPhone,
                                                  'projectAddress':
                                                      item.projectAddress,
                                                  'area': item.area?.toString(),
                                                  'decorateType':
                                                      item.decorateType,
                                                  'roomType': item.roomType,
                                                },
                                              ),
                                            ),
                                          );
                                        },
                                        child: Image.asset(
                                          'assets/images/3.0x/icon_user_edit.png',
                                          width: 16.w,
                                          height: 16.h,
                                        ),
                                      ),
                                      const Spacer(),
                                      Text(
                                        item.decorateTypeDisplay ?? '', //装修类型
                                        style: TextStyle(
                                          color: HexColor('#FFAF2A'),
                                          fontSize: 12.sp,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 16.w,
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 8.h,
                                  ),
                                  Row(
                                    children: [
                                      SizedBox(
                                        width: 16.w,
                                      ),
                                      Image.asset(
                                        'assets/images/signature/icon_signature_location.png',
                                        width: 16.w,
                                        height: 16.h,
                                      ),
                                      SizedBox(
                                        width: 4.w,
                                      ),
                                      Expanded(
                                        child: Text(
                                          item.maskedAddress, //客户地址
                                          style: TextStyle(
                                            color: HexColor('#999999'),
                                            fontSize: 12.sp,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 16.w,
                                      )
                                    ],
                                  ),
                                  SizedBox(
                                    height: 8.h,
                                  ),
                                  Row(
                                    children: [
                                      SizedBox(
                                        width: 16.w,
                                      ),
                                      Text(
                                        '面积:',
                                        style: TextStyle(
                                          color: HexColor('#999999'),
                                          fontSize: 14.sp,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 4.w,
                                      ),
                                      Text(
                                        '${item.area ?? 0}m²',
                                        style: TextStyle(
                                          color: HexColor('#333333'),
                                          fontSize: 14.sp,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 8.h,
                                  ),
                                  Row(
                                    children: [
                                      SizedBox(
                                        width: 16.w,
                                      ),
                                      Text(
                                        '房屋类型:',
                                        style: TextStyle(
                                          color: HexColor('#999999'),
                                          fontSize: 14.sp,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 4.w,
                                      ),
                                      Text(
                                        item.roomTypeDisplay ?? '',
                                        style: TextStyle(
                                          color: HexColor('#333333'),
                                          fontSize: 14.sp,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 8.h,
                                  ),
                                  Row(
                                    children: [
                                      SizedBox(
                                        width: 16.w,
                                      ),
                                      Text(
                                        '客户状态:',
                                        style: TextStyle(
                                          color: HexColor('#999999'),
                                          fontSize: 14.sp,
                                        ),
                                      ),
                                      SizedBox(
                                        width: 4.w,
                                      ),
                                      Text(
                                        item.serviceStatusDisplay ?? '',
                                        style: TextStyle(
                                          color: HexColor('#333333'),
                                          fontSize: 14.sp,
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 16.h,
                                  ),
                                  // 分割线
                                  Container(
                                    height: 1.h,
                                    margin:
                                        EdgeInsets.symmetric(horizontal: 16.w),
                                    color: HexColor('#F5F5F5'),
                                  ),
                                  SizedBox(
                                    height: 16.h,
                                  ),
                                  // 底部按钮行 - 右对齐，固定宽度
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      // 客户对接按钮 - 只有当 buttJointStatus == "0" 时显示
                                      if (item.buttJointStatus == "0") ...[
                                        GestureDetector(
                                          onTap: () {
                                            // 在需要显示弹窗的地方调用
                                            CustomerDockingDialog.show(
                                              context,
                                              serviceId: item.serviceId,
                                              serviceStatus: item.serviceStatus,
                                              decorateType: item.decorateType,
                                              customerName:
                                                  item.customerName ?? '',
                                              decorationAddress:
                                                  item.projectAddress ?? '',
                                              onConfirm: (
                                                  {required DateTime
                                                      selectedDate,
                                                  required String intentionType,
                                                  required String
                                                      decorationType}) {
                                                //刷新当前页数据
                                                _onRefresh();
                                              },
                                            );
                                          },
                                          child: Container(
                                            width: 80.w,
                                            height: 32.h,
                                            decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius:
                                                  BorderRadius.circular(16.r),
                                              border: Border.all(
                                                color: HexColor('#FFAF2A'),
                                                width: 1,
                                              ),
                                            ),
                                            child: Center(
                                              child: Text(
                                                '客户对接',
                                                style: TextStyle(
                                                  color: HexColor('#FFAF2A'),
                                                  fontSize: 12.sp,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(width: 8.w),
                                      ],
                                      // 渠道见面按钮 - 总是显示
                                      GestureDetector(
                                        onTap: () {
                                          ChannelMeetingDialog.show(
                                            context,
                                            serviceId: item.serviceId ?? '',
                                            customerName:
                                                item.customerName ?? '',
                                            decorationAddress:
                                                item.projectAddress ?? '',
                                            onConfirm: (
                                                {required DateTime
                                                    selectedDate}) {
                                              //刷新当前页数据
                                              _onRefresh();
                                            },
                                          );
                                        },
                                        child: Container(
                                          width: 80.w,
                                          height: 32.h,
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            borderRadius:
                                                BorderRadius.circular(16.r),
                                            border: Border.all(
                                              color: HexColor('#FFAF2A'),
                                              width: 1,
                                            ),
                                          ),
                                          child: Center(
                                            child: Text(
                                              '渠道见面',
                                              style: TextStyle(
                                                color: HexColor('#FFAF2A'),
                                                fontSize: 12.sp,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 8.w),
                                      // 派单按钮 - 总是显示
                                      GestureDetector(
                                        onTap: () {
                                          showModalBottomSheet(
                                            context: context,
                                            backgroundColor: Colors.transparent,
                                            isScrollControlled: true,
                                            builder: (context) =>
                                                DispatchOrderDialog(
                                              serviceId: item.serviceId ?? '',
                                              sourceId: item.sourceId ?? '',
                                              customerName:
                                                  item.customerName ?? '',
                                              decorationAddress:
                                                  item.projectAddress ?? '',
                                              onSelected: (selectedType) {
                                                //刷新当前页数据
                                                _onRefresh();
                                              },
                                            ),
                                          );
                                        },
                                        child: Container(
                                          width: 80.w,
                                          height: 32.h,
                                          decoration: BoxDecoration(
                                            color: HexColor('#FFAF2A'),
                                            borderRadius:
                                                BorderRadius.circular(16.r),
                                          ),
                                          child: Center(
                                            child: Text(
                                              '派单',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 12.sp,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 16.w),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 16.h,
                                  ),
                                ],
                              ),
                            ));
                      },
                    ),
            ),
          ),
        ],
      ),
    );
  }

  // 下拉刷新
  void _onRefresh() async {
    pageNum = 1;
    pageTotal = 0; // 重置总页数
    signatureList.clear();
    // 立即更新UI状态，避免在数据加载期间出现数组越界错误
    setState(() {});
    try {
      await _getSignatureList();
      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshFailed();
    }
  }

  // 上拉加载更多
  void _onLoading() async {
    // 检查是否已经到达最后一页
    if (pageNum >= pageTotal && pageTotal > 0) {
      _refreshController.loadNoData();
      return;
    }

    pageNum++;
    try {
      await _getSignatureList();
      _refreshController.loadComplete();
    } catch (e) {
      _refreshController.loadFailed();
    }
  }

  Future<void> _getSignatureList() async {
    try {
      // 获取订单列表
      final apiManager = ApiManager();

      // 构建查询参数
      Map<String, dynamic> queryParams = {
        'pageNum': pageNum,
        'pageSize': pageSize,
      };

      // 筛选条件：服务状态
      if (selectedFilter != null && selectedFilter!.isNotEmpty) {
        queryParams['serviceStatus'] = selectedFilter;
      }

      // 映射查询参数到API要求的参数名称
      if (searchParams.isNotEmpty) {
        _addQueryParam(queryParams, searchParams, 'customerCode',
            'projectNumber'); // 客户编号 -> 项目编号
        _addQueryParam(queryParams, searchParams, 'decorationTypeId',
            'decorateType'); // 装修类型ID -> 装修类型
        _addQueryParam(
            queryParams, searchParams, 'customerName', 'customerName'); // 客户姓名
        _addQueryParam(queryParams, searchParams, 'customerPhone',
            'customerPhone'); // 客户电话
        _addQueryParam(queryParams, searchParams, 'firstSourceId',
            'firstSourceId'); // 首次来源ID
        _addQueryParam(queryParams, searchParams, 'designDeptId',
            'designDept'); // 设计部门ID -> 设计部门
        _addQueryParam(
            queryParams, searchParams, 'sourceId', 'sourceId'); // 来源ID
        _addQueryParam(queryParams, searchParams, 'sourceDetailId',
            'sourceDetailsId'); // 来源明细ID
        _addQueryParam(queryParams, searchParams, 'channelUserId',
            'electricPinUserId'); // 渠道客服ID -> 电销客服ID
        _addQueryParam(queryParams, searchParams, 'referrerPhone',
            'referrerPhone'); // 推荐人手机号
      }

      final result = await apiManager.get(
        '/api/signature/list',
        queryParameters: queryParams,
      );

      if (result != null && mounted) {
        final responseModel = SignatureResponseModel.fromJson(result);
        final List<SignatureModel> newItems = responseModel.rows ?? [];
        final int currentPageTotal = responseModel.pageTotal ?? 0;

        setState(() {
          signatureList.addAll(newItems);
          pageTotal = currentPageTotal; // 更新总页数
        });

        // 处理加载更多状态
        if (newItems.isEmpty || pageNum >= pageTotal) {
          _refreshController.loadNoData();
        }
      }
    } catch (e) {
      print('获取签名列表失败: $e');
      // 如果加载失败，回退页码
      if (pageNum > 1) {
        pageNum--;
      }
      rethrow; // 重新抛出异常，让调用方处理
    }
  }

  // 辅助方法：添加查询参数（只有非空值才添加）
  void _addQueryParam(Map<String, dynamic> queryParams,
      Map<String, dynamic> searchParams, String fromKey, String toKey) {
    final value = searchParams[fromKey];
    if (value != null && value.toString().trim().isNotEmpty) {
      queryParams[toKey] = value;
    }
  }
}
