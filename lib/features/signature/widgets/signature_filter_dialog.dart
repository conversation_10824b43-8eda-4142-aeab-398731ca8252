import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/models/models.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';

class SignatureFilterDialog extends StatefulWidget {
  final String? selectedFilter;
  final Function(String?) onFilterSelected;

  const SignatureFilterDialog({
    super.key,
    this.selectedFilter,
    required this.onFilterSelected,
  });

  @override
  State<SignatureFilterDialog> createState() => _SignatureFilterDialogState();
}

class _SignatureFilterDialogState extends State<SignatureFilterDialog> {
  String? selectedFilter;

  // 筛选选项列表
  List<DictOption> _filterOptions = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    selectedFilter = widget.selectedFilter;
    _fetchCustomerStatus('customer_info_now_status');
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 拖拽指示器
            Container(
              margin: EdgeInsets.only(top: 8.h, bottom: 4.h),
              width: 36.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: HexColor('#E0E0E0'),
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),

            // 标题栏
            Container(
              height: 50.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Stack(
                children: [
                  // 居中的标题
                  Center(
                    child: Text(
                      '筛选',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  // 右侧的关闭按钮
                  Positioned(
                    right: 0,
                    top: 0,
                    bottom: 0,
                    child: GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        size: 24.w,
                        color: HexColor('#999999'),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 筛选选项列表
            Flexible(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: _isLoading
                    ? Center(
                        child: Padding(
                          padding: EdgeInsets.all(32.h),
                          child: const CircularProgressIndicator(
                            color: Colors.black,
                          ),
                        ),
                      )
                    : _filterOptions.isEmpty
                        ? Center(
                            child: Padding(
                              padding: EdgeInsets.all(32.h),
                              child: Text(
                                '暂无筛选选项',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: HexColor('#999999'),
                                ),
                              ),
                            ),
                          )
                        : ListView.builder(
                            shrinkWrap: true,
                            itemCount: _filterOptions.length,
                            itemBuilder: (context, index) {
                              final option = _filterOptions[index];
                              final isSelected = selectedFilter == option.value;

                              return GestureDetector(
                                onTap: () {
                                  setState(() {
                                    selectedFilter = option.value;
                                  });
                                },
                                child: SizedBox(
                                  height: 48.h,
                                  child: Row(
                                    children: [
                                      Text(
                                        option.label,
                                        style: TextStyle(
                                          fontSize: 14.sp,
                                          color: Colors.black,
                                        ),
                                      ),
                                      const Spacer(),
                                      Container(
                                        width: 20.w,
                                        height: 20.h,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: isSelected
                                                ? Colors.black
                                                : HexColor('#CCCCCC'),
                                            width: 1.5,
                                          ),
                                          color: isSelected
                                              ? Colors.black
                                              : Colors.transparent,
                                        ),
                                        child: isSelected
                                            ? Icon(
                                                Icons.check,
                                                size: 14.w,
                                                color: Colors.white,
                                              )
                                            : null,
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
              ),
            ),

            // 底部按钮
            Container(
              padding: EdgeInsets.all(16.w),
              child: Row(
                children: [
                  // 重置按钮
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          selectedFilter = null;
                        });
                      },
                      child: Container(
                        height: 44.h,
                        decoration: BoxDecoration(
                          color: HexColor('#F5F5F5'),
                          borderRadius: BorderRadius.circular(22.r),
                        ),
                        child: Center(
                          child: Text(
                            '重置',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: HexColor('#666666'),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: 12.w),

                  // 确定按钮
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        widget.onFilterSelected(selectedFilter);
                        Navigator.of(context).pop();
                      },
                      child: Container(
                        height: 44.h,
                        decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius: BorderRadius.circular(22.r),
                        ),
                        child: Center(
                          child: Text(
                            '确定',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  //获取客户状态列表
  Future<void> _fetchCustomerStatus(String path) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/home/<USER>/$path',
        queryParameters: null,
      );

      if (response != null && response[path] != null) {
        _filterOptions = (response[path] as List<dynamic>)
            .map((e) => DictOption.fromJson(Map<String, dynamic>.from(e)))
            .toList();
        setState(() {
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      print('获取客户状态失败: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }
}
