import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/models/models.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';
import 'package:flutter_smarthome/features/signature/tabs/customer_info_tab.dart';
import 'package:flutter_smarthome/features/signature/tabs/custom_info_tab.dart';
import 'package:flutter_smarthome/features/signature/tabs/quote_info_tab.dart';

import 'package:flutter_smarthome/shared/dialogs/edit_customer_info_dialog.dart';

class SignatureDetailBody extends StatefulWidget {
  final String? serviceId;
  final Function(String? customerName, String? projectAddress)? onDataLoaded;
  const SignatureDetailBody({super.key, this.serviceId, this.onDataLoaded});

  @override
  State<SignatureDetailBody> createState() => _SignatureDetailBodyState();
}

class _SignatureDetailBodyState extends State<SignatureDetailBody> {
  SignatureDetailModel? _detailHeaderData; // 详情头部数据

  @override
  void initState() {
    super.initState();
    _getDetailHeaderData();
  }

  Future<void> _getDetailHeaderData() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/detail',
        queryParameters: {'serviceId': widget.serviceId},
      );
      if (response != null && mounted) {
        setState(() {
          _detailHeaderData = SignatureDetailModel.fromJson(response);
        });

        // 通过回调函数将数据传递给父组件
        if (widget.onDataLoaded != null) {
          widget.onDataLoaded!(
            _detailHeaderData?.customerName,
            _detailHeaderData?.projectAddress,
          );
        }
      }
    } catch (e) {
      debugPrint('获取签名详情头部数据失败: $e');
    }
  }

  void _onEditButtonTap() {
    // 处理编辑按钮点击事件
    debugPrint('编辑按钮被点击');

    EditCustomerInfoDialog.show(
      context,
      initialName: _detailHeaderData?.customerName,
      initialPhone: _detailHeaderData?.customerPhone,
      initialIdCard: _detailHeaderData?.idCard,
      initialRegion: _detailHeaderData?.region,
      initialAddress: _detailHeaderData?.address,
      onSubmit: (name, phone, idCard, region, address) {
        // 处理提交的数据
        _updateCustomerInfo(name, phone, idCard, region, address);
      },
    );
  }

  Future<void> _updateCustomerInfo(String name, String phone, String idCard,
      String region, String address) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.put(
        '/api/signature/edit/customer/info',
        data: {
          'projectId': _detailHeaderData?.projectId ?? '',
          'name': name,
          'idCard': idCard,
          'region': region,
          'address': address,
        },
      );

      if (response != null && mounted) {
        // 更新成功后只刷新头部数据，避免重复调用
        await _getDetailHeaderData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('客户信息更新成功')),
          );
        }
      }
    } catch (e) {
      debugPrint('更新客户信息失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('更新客户信息失败，请重试')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return NestedScrollView(
      headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
        return <Widget>[
          SliverToBoxAdapter(
            child: Container(
              color: Colors.transparent,
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8.r),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.08),
                          blurRadius: 12,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              _detailHeaderData?.customerName ?? '',
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              _detailHeaderData?.maskedPhone ?? '',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                            SizedBox(width: 8.w),
                            GestureDetector(
                              onTap: () {
                                // 点击编辑按钮的处理逻辑
                                _onEditButtonTap();
                              },
                              child: Image.asset(
                                'assets/images/3.0x/icon_address_edit.png',
                                width: 16.w,
                                height: 16.h,
                              ),
                            ),
                            const Spacer(),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 8.w, vertical: 2.h),
                              child: Text(
                                _detailHeaderData?.decorateTypeDisplay ?? '',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: HexColor('#FFAF2A'),
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8.h),
                        GestureDetector(
                          onTap: _onEditButtonTap,
                          child: Row(
                            children: [
                              Image.asset(
                                'assets/images/signature/icon_signature_location.png',
                                width: 16.w,
                                height: 16.h,
                              ),
                              SizedBox(width: 4.w),
                              Expanded(
                                child: Text(
                                  _detailHeaderData?.projectAddress ?? '',
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: Colors.grey[600],
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                              SizedBox(width: 4.w),
                            ],
                          ),
                        ),
                        SizedBox(height: 8.h),
                        Row(
                          children: [
                            Text(
                              '面积: ',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                            Text(
                              '${_detailHeaderData?.area ?? ''}m²',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Text(
                              '房屋类型: ',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                            Text(
                              _detailHeaderData?.roomTypeDisplay ?? '',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Text(
                              '客户状态: ',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                            Text(
                              _detailHeaderData?.serviceStatusDisplay ?? '',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 12.h), // 减少间距
                ],
              ),
            ),
          ),
        ];
      },
      body: DefaultTabController(
        length: 3,
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20.r),
                  topRight: Radius.circular(20.r),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.08),
                    blurRadius: 12,
                    offset: const Offset(0, -4),
                  ),
                ],
              ),
              child: TabBar(
                isScrollable: true,
                tabAlignment: TabAlignment.start,
                labelColor: Colors.black,
                unselectedLabelColor: Colors.grey[600],
                indicatorColor: Colors.black,
                indicatorWeight: 2.0,
                indicatorSize: TabBarIndicatorSize.label,
                padding: EdgeInsets.only(left: 16.w),
                labelPadding: EdgeInsets.only(left: 16.w, right: 16.w),
                dividerColor: Colors.transparent,
                labelStyle: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.bold,
                ),
                unselectedLabelStyle: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.normal,
                ),
                tabs: const [
                  Tab(text: '客户信息'),
                  Tab(text: '营销信息'),
                  Tab(text: '服务信息'),
                ],
              ),
            ),
            // 分割线
            Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              height: 0.5,
              color: Colors.grey[300],
            ),
            Expanded(
              child: Container(
                color: Colors.white,
                child: TabBarView(
                  children: [
                    CustomerInfoTab(serviceId: widget.serviceId), // 客户信息
                    CustomInfoTab(serviceId: widget.serviceId), // 营销信息
                    QuoteInfoTab(serviceId: widget.serviceId), // 服务信息
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
