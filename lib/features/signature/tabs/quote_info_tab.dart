import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';

class QuoteInfoTab extends StatefulWidget {
  final String? serviceId;
  const QuoteInfoTab({super.key, required this.serviceId});

  @override
  State<QuoteInfoTab> createState() => _QuoteInfoTabState();
}

class _QuoteInfoTabState extends State<QuoteInfoTab> {
  Map<String, dynamic>? _dataSource;
  @override
  void initState() {
    super.initState();
    _requestData();
  }

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: EdgeInsets.all(16.w),
      children: [
        _buildInfoItem('上报日期', _dataSource?['projectCreateTime'] ?? '-'),
        _buildInfoItem('派单日期', _dataSource?['dispatchOrderTime'] ?? '-'),
        _buildInfoItem('城市', _dataSource?['city'] ?? '-'),
        _buildInfoItem('公司', _dataSource?['incomeCompanyName'] ?? '-'),
        _buildInfoItem('设计部门', _dataSource?['designDeptName'] ?? '-'),
        _buildInfoItem('设计师', _dataSource?['designerUserName'] ?? '-'),
        _buildInfoItem('客服', _dataSource?['customerServiceUserName'] ?? '-'),
      ],
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(
                value,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.black,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _requestData() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/serviceInfo',
        queryParameters: {'serviceId': widget.serviceId},
      );
      if (response != null && mounted) {
        setState(() {
          _dataSource = response;
        });
      }
    } catch (e) {
      debugPrint('获取服务信息失败: $e');
    }
  }
}
