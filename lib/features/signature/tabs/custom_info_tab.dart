import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';

class CustomInfoTab extends StatefulWidget {
  final String? serviceId;
  const CustomInfoTab({super.key, required this.serviceId});

  @override
  State<CustomInfoTab> createState() => _CustomInfoTabState();
}

class _CustomInfoTabState extends State<CustomInfoTab> {
  Map<String, dynamic>? _dataSource;
  @override
  void initState() {
    super.initState();
    _requestData();
  }

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: EdgeInsets.all(16.w),
      children: [
        _buildInfoItem('派单类型', _dataSource?['marketingDeptDisplay'] ?? '-'),
        _buildInfoItem('推荐人手机号', _dataSource?['referrerPhone'] ?? '-'),
        _buildInfoItem('来源', _dataSource?['sourceName'] ?? '-'),
        _buildInfoItem('来源明细', _dataSource?['sourceDetailsName'] ?? '-'),
        _buildInfoItem('首次来源', _dataSource?['firstSourceName'] ?? '-'),
        _buildInfoItem('渠道客服', _dataSource?['electricPinUserName'] ?? '-'),
        _buildInfoItem('对接时间', _dataSource?['buttJointTime'] ?? '-'),
        _buildInfoItem('介绍人', _dataSource?['introducerName'] ?? '-'),
      ],
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(
                value,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.black,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _requestData() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/marketingInfo',
        queryParameters: {'serviceId': widget.serviceId},
      );
      if (response != null && mounted) {
        setState(() {
          _dataSource = response;
        });
      }
    } catch (e) {
      debugPrint('获取营销失败: $e');
    }
  }
}
