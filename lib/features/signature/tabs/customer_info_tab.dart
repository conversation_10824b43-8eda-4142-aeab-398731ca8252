import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';

class CustomerInfoTab extends StatefulWidget {
  final String? serviceId;
  const CustomerInfoTab({super.key, required this.serviceId});

  @override
  State<CustomerInfoTab> createState() => _CustomerInfoTabState();
}

class _CustomerInfoTabState extends State<CustomerInfoTab> {
  Map<String, dynamic>? _dataSource;
  @override
  void initState() {
    super.initState();
    _requestData();
  }

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: EdgeInsets.all(16.w),
      children: [
        _buildInfoItem('客户编号', _dataSource?['projectNumber'] ?? '-'), //客户编号
        _buildInfoItem('会员类型', _dataSource?['memberTypeDisplay'] ?? '-'), //会员类型
        _buildInfoItem(
            '客户状态', _dataSource?['customerTypeDisplay'] ?? '-'), //客户状态
        _buildInfoItem('楼盘名称', _dataSource?['communityName'] ?? '-'),
        _buildInfoItem('地址', _dataSource?['projectAddress'] ?? '-'),
        _buildInfoItem('区域', _dataSource?['region'] ?? '-'), //区域
        _buildInfoItem('房屋类型', _dataSource?['roomTypeDisplay'] ?? '-'),
        _buildInfoItem('户型',
            '${_dataSource?['bedroomNumber'] ?? 0}室${_dataSource?['livingRoomNumber'] ?? 0}厅${_dataSource?['kitchenRoomNumber'] ?? 0}厨${_dataSource?['toiletRoomNumber'] ?? 0}卫'), //户型
        _buildInfoItem('面积', '${_dataSource?['area'] ?? 0}㎡'), //面积
        _buildInfoItem('期房/现房', _dataSource?['houseStatusDisplay'] ?? '-'),
        _buildInfoItem('交房日期', _dataSource?['houseDate'] ?? '-'), //交房日期
        _buildInfoItem(
            '计划装修日期', _dataSource?['planDecorateDate'] ?? '-'), //计划装修日期
        _buildInfoItem('备注', _dataSource?['remark'] ?? '-'),
        _buildInfoItem('邮箱', _dataSource?['email'] ?? '-'),
        _buildInfoItem('QQ', _dataSource?['qqNumber'] ?? '-'), //QQ
        _buildInfoItem('微信号', _dataSource?['wechatNumber'] ?? '-'),
        _buildInfoItem('登记者', _dataSource?['registerUser'] ?? '-'), //客户来源
      ],
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(
                value,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.black,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _requestData() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/customerInfo',
        queryParameters: {'serviceId': widget.serviceId},
      );
      if (response != null && mounted) {
        setState(() {
          _dataSource = response;
        });
      }
    } catch (e) {
      debugPrint('获取客户信息失败: $e');
    }
  }
}
