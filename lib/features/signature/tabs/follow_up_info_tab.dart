import 'package:flutter/material.dart';
import 'package:flutter_smarthome/core/utils/empty_state.dart';

class FollowUpInfoTab extends StatefulWidget {
  const FollowUpInfoTab({super.key});

  @override
  State<FollowUpInfoTab> createState() => _FollowUpInfoTabState();
}

class _FollowUpInfoTabState extends State<FollowUpInfoTab> {
  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(onRefresh: () {
      // 刷新逻辑
      debugPrint('刷新跟进信息');
    });
  }
}
