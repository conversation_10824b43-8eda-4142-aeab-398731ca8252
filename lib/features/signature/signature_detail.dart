import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/features/signature/naked_sign_dialog.dart';
import 'package:flutter_smarthome/features/signature/design_contract_dialog.dart';
import 'signature_detail_body.dart';

class SignatureDetailWidget extends StatefulWidget {
  final String serviceId; // 服务ID
  const SignatureDetailWidget({super.key, required this.serviceId});

  @override
  State<SignatureDetailWidget> createState() => _SignatureDetailWidgetState();
}

class _SignatureDetailWidgetState extends State<SignatureDetailWidget> {
  String? _customerName;
  String? _projectAddress;

  void _onDataLoaded(String? customerName, String? projectAddress) {
    setState(() {
      _customerName = customerName;
      _projectAddress = projectAddress;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true, // 让body背景延伸到AppBar后面
      appBar: AppBar(
        title: const Text('用户详情'),
        //标题加粗
        titleTextStyle: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 18.sp,
          color: Colors.black,
        ),
        backgroundColor: Colors.transparent,
        foregroundColor: const Color.fromRGBO(0, 0, 0, 1),
        elevation: 0,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
        // actions: [
        //   GestureDetector(
        //     onTap: () {
        //       // TODO: 跳转到合同信息页面
        //     },
        //     child: Container(
        //         margin: EdgeInsets.only(right: 16.w),
        //         padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        //         decoration: BoxDecoration(
        //           color: Colors.grey[200],
        //           borderRadius: BorderRadius.circular(15.r),
        //         ),
        //         child: Row(
        //           mainAxisSize: MainAxisSize.min,
        //           children: [
        //             Image.asset(
        //               'assets/images/signature/icon_signature_info.png',
        //               width: 12.w,
        //               height: 12.h,
        //             ),
        //             SizedBox(width: 4.w),
        //             Text(
        //               '合同信息',
        //               style: TextStyle(
        //                 color: Colors.black,
        //                 fontSize: 12.sp,
        //               ),
        //             ),
        //           ],
        //         )),
        //   ),
        // ],
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            stops: [0.0, 0.4], // 渐变范围调整
            colors: [
              Color(0xFFFFF4E0), // #FFF4E0 顶部颜色
              Colors.white, // 白色 底部颜色
            ],
          ),
        ),
        child: SafeArea(
          child: SignatureDetailBody(
            serviceId: widget.serviceId,
            onDataLoaded: _onDataLoaded,
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.all(16.0),
        child: SafeArea(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Container(
                  height: 44.h,
                  margin: EdgeInsets.only(right: 8.w),
                  child: ElevatedButton(
                    onPressed: () {
                      // TODO: 裸签合同
                      NakedSignDialog.show(
                        context: context,
                        title: '裸签合同',
                        serviceId: widget.serviceId,
                        customerName: _customerName,
                        customerAddress: _projectAddress,
                        content: const Text('弹框内容'),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      elevation: 0,
                      minimumSize: Size.zero,
                      padding: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(22.r),
                        side: const BorderSide(color: Colors.grey, width: 1),
                      ),
                    ),
                    child: Text(
                      '裸签合同',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  height: 44.h,
                  margin: EdgeInsets.symmetric(horizontal: 4.w),
                  child: ElevatedButton(
                    onPressed: () {
                      // 设计合同
                      DesignContractDialog.show(
                        context: context,
                        title: '设计合同',
                        serviceId: widget.serviceId,
                        customerName: _customerName,
                        customerAddress: _projectAddress,
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black,
                      elevation: 0,
                      minimumSize: Size.zero,
                      padding: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(22.r),
                        side: const BorderSide(color: Colors.grey, width: 1),
                      ),
                    ),
                    child: Text(
                      '设计合同',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
