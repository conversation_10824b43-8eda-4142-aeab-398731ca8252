import 'package:flutter/material.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';
import 'package:flutter/services.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:http/http.dart' as http;
import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

class ScanContractPDFWidget extends StatefulWidget {
  final String? esignContractId;
  final String? contractUrl;
  final bool isDirectLoad;
  const ScanContractPDFWidget(
      {super.key,
      required this.esignContractId,
      required this.contractUrl,
      this.isDirectLoad = false});

  @override
  State<ScanContractPDFWidget> createState() => _ScanContractPDFWidgetState();
}

class _ScanContractPDFWidgetState extends State<ScanContractPDFWidget> {
  Map<String, dynamic>? _dataSource;
  WebViewController? _webViewController;
  bool _isLoading = true;
  String? _errorMessage;
  Timer? _loadingTimeoutTimer;
  int _retryCount = 0;
  static const int _maxRetries = 3;
  static const int _loadingTimeoutSeconds = 30; // PDF下载可能需要更长时间
  bool _usePdfViewer = false; // 是否使用PDF查看器
  Uint8List? _pdfBytes; // PDF字节数据
  final GlobalKey<SfPdfViewerState> _pdfViewerKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _initializeWebView();

    // 根据传入的参数决定加载方式
    if (widget.isDirectLoad == true) {
      _loadDirectPDF(widget.contractUrl!);
    } else {
      _loadContractPDF();
    }
  }

  @override
  void dispose() {
    _loadingTimeoutTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_dataSource?['fileName'] ?? '合同文件'),
        actions: [
          if (_dataSource?['fileDownloadUrl'] != null) ...[
            IconButton(
              icon: const Icon(Icons.copy),
              tooltip: '复制PDF链接',
              onPressed: () {
                _downloadPDF(
                    _dataSource!['fileDownloadUrl'], _dataSource!['fileName']);
              },
            ),
          ],
          // PDF查看器已内置双击缩放和手势缩放功能
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在加载PDF文件...', style: TextStyle(color: Colors.grey)),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red[400],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black54,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                _retryLoadFile();
              },
              child: const Text('重新加载'),
            ),
          ],
        ),
      );
    }

    // 优先使用PDF查看器
    if (_usePdfViewer && _pdfBytes != null) {
      return SfPdfViewer.memory(
        _pdfBytes!,
        key: _pdfViewerKey,
        enableDoubleTapZooming: true,
        enableTextSelection: true,
        onDocumentLoadFailed: (PdfDocumentLoadFailedDetails details) {
          debugPrint('PDF加载失败: ${details.error}');
          setState(() {
            _errorMessage = 'PDF文件格式错误或文件损坏';
            _isLoading = false;
          });
        },
        onDocumentLoaded: (PdfDocumentLoadedDetails details) {
          debugPrint('PDF加载成功，共 ${details.document.pages.count} 页');
        },
      );
    }

    // 回退到WebView
    if (_dataSource?['fileDownloadUrl'] != null && _webViewController != null) {
      return Stack(
        children: [
          WebViewWidget(controller: _webViewController!),
          if (_isLoading)
            Container(
              color: Colors.white.withValues(alpha: 0.8),
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      );
    }

    return const Center(
      child: Text(
        '未找到PDF文件',
        style: TextStyle(fontSize: 16, color: Colors.black54),
      ),
    );
  }

  void _initializeWebView() {
    // 根据平台选择合适的 WebView 实现
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }

    _webViewController = WebViewController.fromPlatformCreationParams(params);

    // 如果是 Android 平台，配置额外的 Android 特定设置
    if (_webViewController!.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(true);
      final AndroidWebViewController androidController =
          _webViewController!.platform as AndroidWebViewController;
      androidController.setMediaPlaybackRequiresUserGesture(false);

      // 添加更多Android特定配置以支持PDF查看
      androidController.setGeolocationPermissionsPromptCallbacks(
        onShowPrompt: (request) async {
          return const GeolocationPermissionsResponse(
            allow: false,
            retain: false,
          );
        },
      );
    }

    _webViewController!
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setUserAgent(_getPlatformUserAgent())
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            debugPrint('WebView页面开始加载: $url');
            _startLoadingTimeout();
            setState(() {
              _isLoading = true;
            });
          },
          onPageFinished: (String url) {
            debugPrint('WebView页面加载完成: $url');
            _cancelLoadingTimeout();
            setState(() {
              _isLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {
            debugPrint(
                'WebView资源加载错误: ${error.description}, 错误代码: ${error.errorCode}, URL: ${error.url}');
            _cancelLoadingTimeout();

            // PDF加载失败
            setState(() {
              _isLoading = false;
              _errorMessage =
                  'PDF加载失败: ${error.description}\n错误代码: ${error.errorCode}';
            });
          },
          onNavigationRequest: (NavigationRequest request) {
            debugPrint('WebView导航请求: ${request.url}');
            // 检查是否是PDF文件
            if (request.url.toLowerCase().endsWith('.pdf')) {
              debugPrint('检测到PDF文件导航请求');
            }
            return NavigationDecision.navigate;
          },
          onHttpError: (HttpResponseError error) {
            debugPrint('WebView HTTP错误: ${error.response?.statusCode}');
            _cancelLoadingTimeout();
            setState(() {
              _isLoading = false;
              _errorMessage = 'HTTP错误: ${error.response?.statusCode}';
            });
          },
        ),
      );
  }

  // 获取平台特定的User-Agent
  String _getPlatformUserAgent() {
    if (Platform.isAndroid) {
      // Android使用桌面端User-Agent以确保Google Docs Viewer正常工作
      return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
    } else if (Platform.isIOS) {
      // iOS使用Safari User-Agent
      return 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1';
    } else {
      // 其他平台使用桌面端User-Agent
      return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
    }
  }

  // 超时控制方法
  void _startLoadingTimeout() {
    _cancelLoadingTimeout();
    _loadingTimeoutTimer =
        Timer(const Duration(seconds: _loadingTimeoutSeconds), () {
      if (_isLoading) {
        debugPrint('加载超时，尝试重新加载');
        setState(() {
          _isLoading = false;
          _errorMessage = '加载超时，请检查网络连接后重试';
        });
      }
    });
  }

  void _cancelLoadingTimeout() {
    _loadingTimeoutTimer?.cancel();
    _loadingTimeoutTimer = null;
  }

  // 重试加载文件
  void _retryLoadFile() {
    if (_retryCount >= _maxRetries) {
      setState(() {
        _isLoading = false;
        _errorMessage = '多次重试失败，请检查网络连接或文件链接是否有效';
      });
      return;
    }

    _retryCount++;
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    if (widget.isDirectLoad) {
      // 直接加载模式
      if (widget.contractUrl != null && widget.contractUrl!.isNotEmpty) {
        _loadDirectPDF(widget.contractUrl!);
      } else {
        setState(() {
          _isLoading = false;
          _errorMessage = '未提供有效的文件链接';
        });
      }
    } else {
      if (_dataSource?['fileDownloadUrl'] != null) {
        // 如果已有数据，直接重新加载
        String url = _dataSource!['fileDownloadUrl'];
        _loadFileWithSmartLogic(url);
      } else {
        // 否则重新获取数据
        _loadContractPDF();
      }
    }
  }

  // 下载PDF文件并使用PDF查看器显示
  Future<void> _downloadAndLoadPDF(String pdfUrl) async {
    try {
      debugPrint('开始下载PDF文件: $pdfUrl');

      // 设置较长的超时时间，因为PDF文件可能较大
      final response = await http.get(
        Uri.parse(pdfUrl),
        headers: {
          'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        },
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        debugPrint('PDF文件下载成功，大小: ${response.bodyBytes.length} bytes');

        setState(() {
          _pdfBytes = response.bodyBytes;
          _usePdfViewer = true;
          _isLoading = false;
        });
      } else {
        throw Exception(
            'HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }
    } catch (e) {
      debugPrint('PDF下载失败: $e');
      // 下载失败，回退到WebView
      setState(() {
        _usePdfViewer = false;
      });
      _loadPDFInWebView(pdfUrl);
    }
  }

  void _loadPDFInWebView(String pdfUrl) {
    if (_webViewController != null) {
      debugPrint('使用WebView加载PDF: $pdfUrl');
      _webViewController!.loadRequest(Uri.parse(pdfUrl));
    }
  }

  // 直接加载URL（可以是PDF或其他类型）
  void _loadDirectPDF(String url) {
    debugPrint('直接加载文件: $url');

    // 设置数据
    setState(() {
      _dataSource = {
        'fileDownloadUrl': url,
        'fileName': _getFilenameFromUrl(url),
      };
    });

    // 统一使用智能加载方法
    _loadFileWithSmartLogic(url);
  }

  // 智能加载文件 - 根据文件类型选择加载方式
  void _loadFileWithSmartLogic(String url) {
    debugPrint('智能加载文件: $url');

    // 检查是否为PDF文件（通过URL后缀判断）
    if (_isPdfFile(url)) {
      debugPrint('检测到PDF文件，优先使用PDF查看器');
      // 首先尝试下载PDF并使用专用查看器
      _downloadAndLoadPDF(url);
    } else {
      debugPrint('非PDF文件，直接在WebView中加载');
      _loadUrlInWebView(url);
    }
  }

  // 检查是否为PDF文件 - 增强判断逻辑
  bool _isPdfFile(String url) {
    // 检查URL后缀
    if (url.toLowerCase().endsWith('.pdf')) {
      return true;
    }

    // 检查URL中是否包含PDF相关的MIME类型参数
    final uri = Uri.tryParse(url);
    if (uri != null) {
      // 检查查询参数中是否有content-type相关信息
      final contentType = uri.queryParameters['content-type'] ??
          uri.queryParameters['type'] ??
          uri.queryParameters['mime'];
      if (contentType != null && contentType.toLowerCase().contains('pdf')) {
        return true;
      }

      // 检查路径中是否包含.pdf
      if (uri.path.toLowerCase().contains('.pdf')) {
        return true;
      }
    }

    return false;
  }

  // 直接在WebView中加载URL
  void _loadUrlInWebView(String url) {
    if (_webViewController != null) {
      debugPrint('直接在WebView中加载URL: $url');
      _webViewController!.loadRequest(Uri.parse(url));
    }
  }

  // 从URL中提取文件名
  String _getFilenameFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;
      if (pathSegments.isNotEmpty) {
        return pathSegments.last;
      }
    } catch (e) {
      // 解析URL出错时
    }
    return '合同文件';
  }

  Future<void> _loadContractPDF() async {
    try {
      debugPrint('开始加载合同PDF，ID: ${widget.esignContractId}');

      // 添加API请求超时控制
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/contract/pdf',
        queryParameters: {'esignContractId': widget.esignContractId},
      ).timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          throw TimeoutException('API请求超时', const Duration(seconds: 15));
        },
      );

      debugPrint('API响应: $response');
      if (response != null && response['fileDownloadUrl'] != null) {
        debugPrint('文件下载URL: ${response['fileDownloadUrl']}');
        debugPrint('文件名: ${response['fileName']}');

        // 重置重试计数
        _retryCount = 0;

        setState(() {
          _dataSource = response;
        });

        // 检查文件类型并加载到WebView
        String url = response['fileDownloadUrl'];
        // 统一使用智能加载方法
        _loadFileWithSmartLogic(url);
      } else {
        setState(() {
          _isLoading = false;
          _errorMessage = '未获取到合同PDF数据或文件链接无效';
        });
      }
    } on TimeoutException catch (e) {
      debugPrint('API请求超时: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'API请求超时，请检查网络连接后重试';
      });
    } catch (e) {
      debugPrint('加载合同PDF失败: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = '加载失败: $e';
      });
    }
  }

  // 复制文件链接到剪贴板
  void _downloadPDF(String fileUrl, String fileName) {
    Clipboard.setData(ClipboardData(text: fileUrl));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('文件链接已复制到剪贴板'),
        action: SnackBarAction(
          label: '重新加载',
          onPressed: () {
            // 重置重试计数
            _retryCount = 0;
            setState(() {
              _isLoading = true;
              _errorMessage = null;
            });
            // 统一使用智能加载方法
            _loadFileWithSmartLogic(fileUrl);
          },
        ),
      ),
    );
  }
}
