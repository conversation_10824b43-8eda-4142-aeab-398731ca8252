import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/features/signature/contract_detail.dart';
import 'package:flutter_smarthome/features/signature/scan_contract_pdf.dart';
import 'package:flutter_smarthome/features/signature/signature_payment.dart';
import 'package:flutter_smarthome/shared/dialogs/discount_info_dialog.dart';
import 'package:flutter_smarthome/widgets/account_type_dialog.dart';
import 'package:flutter/cupertino.dart';

class NakedSignDialog extends StatefulWidget {
  final String? title;
  final Widget? content;
  final String? serviceId;
  final String? customerName;
  final String? customerAddress;
  final VoidCallback? onClose;

  const NakedSignDialog({
    Key? key,
    this.title,
    this.content,
    this.serviceId,
    this.customerName,
    this.customerAddress,
    this.onClose,
  }) : super(key: key);

  @override
  State<NakedSignDialog> createState() => _NakedSignDialogState();

  // 静态方法来显示弹框
  static Future<T?> show<T>({
    required BuildContext context,
    String? title,
    Widget? content,
    String? serviceId,
    String? customerName,
    String? customerAddress,
    VoidCallback? onClose,
    bool barrierDismissible = true,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isDismissible: barrierDismissible,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return NakedSignDialog(
          title: title,
          content: content,
          serviceId: serviceId,
          customerName: customerName,
          customerAddress: customerAddress,
          onClose: onClose,
        );
      },
    );
  }
}

class _NakedSignDialogState extends State<NakedSignDialog> {
  Map<String, dynamic>? firstDataSource;
  Map<String, dynamic>? secondDataSource;
  bool e1ContractStatus = false;
  bool e2ContractStatus = false;
  String? e1ContractId; //e签宝合同id
  String? e2ContractId; //e签宝合同id

  // 添加加载状态
  bool isLoading = true;
  bool isFirstContractLoading = true;
  bool isSecondContractLoading = true;

  @override
  void initState() {
    super.initState();
    if (widget.serviceId != null) {
      _loadInitialData();
    } else {
      // 如果没有 serviceId，直接设置为不加载状态
      setState(() {
        isLoading = false;
        isFirstContractLoading = false;
        isSecondContractLoading = false;
      });
    }
  }

  // 加载初始数据
  Future<void> _loadInitialData() async {
    // 为了更好地调试，我们分别等待每个请求
    try {
      // 并行加载数据以提高性能
      await Future.wait([
        _requestData(widget.serviceId!, "1"),
        _requestData(widget.serviceId!, "2"),
        _requestEContractStatus(widget.serviceId!, "1"),
        _requestEContractStatus(widget.serviceId!, "2"),
      ]);

      // 所有数据加载完成后更新状态
      if (mounted) {
        setState(() {
          isLoading = false;
          isFirstContractLoading = false;
          isSecondContractLoading = false;
        });
      }
    } catch (e) {
      // 处理错误
      if (mounted) {
        setState(() {
          isLoading = false;
          isFirstContractLoading = false;
          isSecondContractLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Material(
        color: Colors.transparent,
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.9,
            minHeight: MediaQuery.of(context).size.height * 0.3,
          ),
          margin: const EdgeInsets.all(0),
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.0),
              topRight: Radius.circular(20.0),
            ),
          ),
          child: Stack(
            children: [
              // 主要内容
              SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 20.h,
                      ),
                      Text(
                        '一期合同',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(
                        height: 16.h,
                      ),
                      // 合同信息行
                      _buildContractInfoRow(
                          '收款公司：',
                          firstDataSource?['incomeCompanyName'],
                          isFirstContractLoading),
                      SizedBox(
                        height: 8.h,
                      ),
                      _buildContractInfoRow(
                        '合同金额：',
                        firstDataSource?['contractPrice'] != null
                            ? '${firstDataSource!['contractPrice']} 元'
                            : null,
                        isFirstContractLoading,
                      ),
                      SizedBox(
                        height: 8.h,
                      ),
                      _buildContractInfoRow(
                          '合同开工日期：',
                          firstDataSource?['contractConstructionStartDate'],
                          isFirstContractLoading),
                      SizedBox(
                        height: 8.h,
                      ),
                      _buildContractInfoRow(
                          '合同完工日期：',
                          firstDataSource?['contractConstructionEndDate'],
                          isFirstContractLoading),
                      SizedBox(
                        height: 16.h,
                      ),
                      // 一期合同按钮
                      _buildFirstContractButtons(),
                      SizedBox(
                        height: 20.h,
                      ),
                      Divider(
                        height: 1.h,
                        color: Colors.grey[300],
                      ),
                      SizedBox(
                        height: 20.h,
                      ),
                      Text(
                        '二期合同',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(
                        height: 16.h,
                      ),
                      // 合同信息行
                      _buildContractInfoRow(
                          '收款公司：',
                          secondDataSource?['incomeCompanyName'],
                          isSecondContractLoading),
                      SizedBox(
                        height: 8.h,
                      ),
                      _buildContractInfoRow(
                        '合同金额：',
                        secondDataSource?['contractPrice'] != null
                            ? '${secondDataSource!['contractPrice']} 元'
                            : null,
                        isSecondContractLoading,
                      ),
                      SizedBox(
                        height: 8.h,
                      ),
                      _buildContractInfoRow(
                          '合同开工日期：',
                          secondDataSource?['contractConstructionStartDate'],
                          isSecondContractLoading),
                      SizedBox(
                        height: 8.h,
                      ),
                      _buildContractInfoRow(
                          '合同完工日期：',
                          secondDataSource?['contractConstructionEndDate'],
                          isSecondContractLoading),
                      SizedBox(
                        height: 16.h,
                      ),
                      // 二期合同按钮
                      _buildSecondContractButtons(),
                      SizedBox(
                        height: 20.h,
                      ),
                    ],
                  ),
                ),
              ),

              // 右上角关闭按钮
              Positioned(
                top: 16.0,
                right: 16.0,
                child: GestureDetector(
                  onTap: () {
                    if (widget.onClose != null) {
                      widget.onClose!();
                    } else {
                      Navigator.of(context).pop();
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(4.0),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.grey[200],
                    ),
                    child: const Icon(
                      Icons.close,
                      size: 20.0,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 刷新合同数据
  Future<void> _refreshContractData() async {
    if (widget.serviceId != null) {
      // 设置加载状态
      setState(() {
        isFirstContractLoading = true;
        isSecondContractLoading = true;
      });

      try {
        // 等待所有数据请求完成
        await Future.wait([
          _requestData(widget.serviceId!, "1"),
          _requestData(widget.serviceId!, "2"),
          _requestEContractStatus(widget.serviceId!, "1"),
          _requestEContractStatus(widget.serviceId!, "2"),
        ]);
      } finally {
        // 无论成功或失败都要结束加载状态
        if (mounted) {
          setState(() {
            isFirstContractLoading = false;
            isSecondContractLoading = false;
          });
        }
      }
    }
  }

  //合同详情
  Future<void> _requestData(String serviceId, String contractType) async {
    final apiManager = ApiManager();
    final response = await apiManager.get(
      '/api/signature/naked/contract',
      queryParameters: {'serviceId': serviceId, 'contractType': contractType},
    );

    if (response != null) {
      // 直接更新数据，不调用 setState，由调用方统一管理状态更新
      if (contractType == "1") {
        firstDataSource = response;
        if (firstDataSource?['customerProjectServiceId'] == null) {
          firstDataSource?['customerProjectServiceId'] = widget.serviceId ?? '';
          firstDataSource?['contractType'] = '1';
        }
      } else if (contractType == "2") {
        secondDataSource = response;
        secondDataSource?['customerProjectServiceId'] = widget.serviceId ?? '';
        secondDataSource?['contractType'] = '2';
      }
    }
  }

  // 构建一期合同按钮
  Widget _buildFirstContractButtons() {
    // 如果正在加载，显示加载状态
    if (isFirstContractLoading) {
      return SizedBox(
        height: 32.h,
        child: Center(
          child: SizedBox(
            width: 20.w,
            height: 20.h,
            child: const CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
            ),
          ),
        ),
      );
    }

    return _buildContractButtonsByStatus('1');
  }

  // 构建二期合同按钮
  Widget _buildSecondContractButtons() {
    // 如果正在加载，显示加载状态
    if (isSecondContractLoading) {
      return SizedBox(
        height: 32.h,
        child: Center(
          child: SizedBox(
            width: 20.w,
            height: 20.h,
            child: const CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
            ),
          ),
        ),
      );
    }

    return _buildContractButtonsByStatus('2');
  }

  // 根据状态构建合同按钮行
  Widget _buildContractButtonsByStatus(String contractType) {
    List<Widget> buttons = [];

    // 获取对应合同的数据源和状态
    Map<String, dynamic>? dataSource =
        contractType == '1' ? firstDataSource : secondDataSource;
    bool eContractStatus =
        contractType == '1' ? e1ContractStatus : e2ContractStatus;
    String? contractId = contractType == '1' ? e1ContractId : e2ContractId;

    // 根据不同状态添加按钮
    if (dataSource?['alreadySign'] == true) {
      // 状态1：已签约 - 显示查看合同和去支付按钮
      buttons.add(_buildDynamicButton('查看合同', () {
        _showContractPicPicker(
            dataSource?['contractPic'] as List<dynamic>?, contractId ?? '');
      }));
      buttons.add(_buildDynamicButton('去支付', () {
        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => SignaturePaymentWidget(
                    contractData: dataSource!,
                    customerName: widget.customerName,
                    customerAddress: widget.customerAddress,
                  )),
        );
      }));
    } else if (dataSource?['alreadySign'] == false && eContractStatus == true) {
      // 状态2：未签约但合同已填写 - 显示申请优惠、填写合同信息、查看合同、去签约按钮
      if (!_isContractAlreadySigned(contractType)) {
        buttons.add(_buildDynamicDiscountButton('申请优惠', contractType));
      }
      buttons.add(_buildDynamicButton('填写信息', () {
        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => ContractDetailWidget(
                    contractData: dataSource,
                    esignContractId: contractId,
                    onContractUpdated: _refreshContractData,
                  )),
        );
      }));
      buttons.add(_buildDynamicButton('查看合同', () {
        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => ScanContractPDFWidget(
                    esignContractId: contractId ?? '',
                    contractUrl: dataSource?['contractPic']?[0] ?? '',
                    isDirectLoad: dataSource?['alreadySign'] ?? false,
                  )),
        );
      }));
      buttons.add(_buildDynamicButton('去签约', () {
        showDialog(
          context: context,
          builder: (context) => AccountTypeDialog(
            initialValue: 'private',
            onConfirm: (selectedType,
                {String? operatorName, String? operatorPhone}) {
              // 调用签约接口
              _requestDirectSign(
                contractId ?? '',
                selectedType == 'private' ? '0' : '1',
                operatorName ?? '',
                operatorPhone ?? '',
              );
            },
          ),
        );
      }));
    } else {
      // 状态3：未签约且合同未填写 - 显示申请优惠和填写合同信息按钮
      if (!_isContractAlreadySigned(contractType)) {
        buttons.add(_buildDynamicDiscountButton('申请优惠', contractType));
      }
      buttons.add(_buildDynamicButton('填写合同信息', () {
        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => ContractDetailWidget(
                    contractData: dataSource,
                    esignContractId: contractId,
                    onContractUpdated: _refreshContractData,
                  )),
        );
      }));
    }

    return _buildDynamicButtonRow(buttons);
  }

  // 构建动态宽度的按钮行
  Widget _buildDynamicButtonRow(List<Widget> buttons) {
    if (buttons.isEmpty) return Container();

    return Row(
      children: buttons
          .asMap()
          .entries
          .map((entry) {
            int index = entry.key;
            Widget button = entry.value;

            return [
              if (index > 0) SizedBox(width: 8.w), // 按钮间距
              Expanded(child: button),
            ];
          })
          .expand((x) => x)
          .toList(),
    );
  }

  // 构建动态按钮（合同操作按钮样式）
  Widget _buildDynamicButton(String text, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 32.h, // 固定按钮高度
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFF4A90E2), width: 1),
          borderRadius: BorderRadius.circular(6.r),
          color: Colors.white,
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 13.sp,
              color: const Color(0xFF4A90E2),
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  // 构建动态申请优惠按钮（橙色样式）
  Widget _buildDynamicDiscountButton(String text, String contractType) {
    return GestureDetector(
      onTap: () {
        DiscountInfoDialog.show(
          widget.serviceId ?? '',
          contractType,
          context,
          originalPrice: _getOriginalPrice(contractType),
          onClose: () {
            // 对话框关闭时刷新合同数据
            _refreshContractData();
          },
        );
      },
      child: Container(
        height: 32.h, // 与其他按钮保持一致的高度
        decoration: BoxDecoration(
          color: const Color(0xFFFF6B35), // 橙色背景
          borderRadius: BorderRadius.circular(6.r),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFFF6B35).withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 13.sp,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  // 检查合同是否已签约
  bool _isContractAlreadySigned(String contractType) {
    if (contractType == '1') {
      return firstDataSource?['alreadySign'] == true;
    } else {
      return secondDataSource?['alreadySign'] == true;
    }
  }

  // 获取原始价格
  String? _getOriginalPrice(String contractType) {
    if (contractType == '1') {
      return firstDataSource?['contractPrice']?.toString();
    } else {
      return secondDataSource?['contractPrice']?.toString();
    }
  }

  //E 签宝合同状态
  Future<void> _requestEContractStatus(
      String serviceId, String contractType) async {
    final apiManager = ApiManager();
    final response = await apiManager.get(
      '/api/signature/contract/logic',
      queryParameters: {'serviceId': serviceId, 'contractType': contractType},
    );

    if (response != null) {
      // 直接更新数据，不调用 setState，由调用方统一管理状态更新
      if (contractType == "1") {
        e1ContractId = response['id'];
        // 修复：明确设置状态，而不只是在特定条件下设置为true
        e1ContractStatus =
            response['isSign'] == '0' || response['isSign'] == '1'; //已填写且未签约
      } else if (contractType == "2") {
        e2ContractId = response['id'];
        // 修复：明确设置状态，而不只是在特定条件下设置为true
        e2ContractStatus =
            response['isSign'] == '0' || response['isSign'] == '1'; //已填写且未签约
      }
    } else {
      // 即使响应为null，也要确保状态被正确设置
      if (contractType == "1") {
        e1ContractStatus = false;
        e1ContractId = null;
      } else if (contractType == "2") {
        e2ContractStatus = false;
        e2ContractId = null;
      }
    }
  }

  // 从URL中提取文件名
  String _getFilenameFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;
      if (pathSegments.isNotEmpty) {
        return pathSegments.last.length > 15
            ? '${pathSegments.last.substring(0, 15)}...'
            : pathSegments.last;
      }
    } catch (e) {
      // 解析URL出错时
    }
    return '未命名文件';
  }

  //去签约
  Future<void> _requestDirectSign(
      String eid, String type, String agentName, String agentPhone) async {
    if (widget.serviceId == null) return;

    final apiManager = ApiManager();
    try {
      final response = await apiManager.post(
        '/api/signature/direct/sign',
        data: {
          'esignContractId': eid,
          'psnOrOrg': type,
          'agentName': agentName,
          'agentPhone': agentPhone,
        },
      );

      if (response != null) {
        // 显示提示弹窗
        _showSuccessDialog();
      }
    } catch (e) {
      // 签约失败处理
    }
  }

  // 显示成功提示弹窗
  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('提示'),
          content: const Text('已将电子合同链接通过短信形式发送至客户手机，请及时提醒客户完成合同签署，链接有效期三小时'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭弹窗
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  // 显示合同图片选择器底部弹窗
  void _showContractPicPicker(
      List<dynamic>? contractPics, String esignContractId) {
    if (contractPics == null || contractPics.isEmpty) {
      // 如果没有合同图片，直接显示提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('没有可用的合同文件')),
      );
      return;
    }

    // 创建一个可以保存选中索引的变量
    int selectedIndex = 0;

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 250.h,
          padding: EdgeInsets.all(16.r),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '选择合同文件',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: const Icon(Icons.close),
                  ),
                ],
              ),
              const Divider(),
              Expanded(
                child: CupertinoPicker(
                  magnification: 1.2,
                  squeeze: 1.2,
                  useMagnifier: true,
                  itemExtent: 40,
                  scrollController: FixedExtentScrollController(initialItem: 0),
                  onSelectedItemChanged: (int index) {
                    // 保存选中的索引
                    selectedIndex = index;
                  },
                  children:
                      List<Widget>.generate(contractPics.length, (int index) {
                    final filename = _getFilenameFromUrl(contractPics[index]);
                    return Center(
                      child: Text(
                        '合同文件 ${index + 1}: $filename',
                        style: TextStyle(fontSize: 14.sp),
                      ),
                    );
                  }),
                ),
              ),
              SizedBox(height: 10.h),
              GestureDetector(
                onTap: () {
                  Navigator.pop(context); // 关闭选择器
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ScanContractPDFWidget(
                        esignContractId: esignContractId,
                        contractUrl: contractPics[selectedIndex],
                        isDirectLoad: true, // 直接加载模式
                      ),
                    ),
                  );
                },
                child: Container(
                  width: double.infinity,
                  height: 40.h,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Center(
                    child: Text(
                      '确定',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16.sp,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 构建合同信息行的辅助方法
  Widget _buildContractInfoRow(String label, String? value, bool isLoading) {
    return Row(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey,
          ),
        ),
        if (isLoading)
          SizedBox(
            width: 12.w,
            height: 12.h,
            child: const CircularProgressIndicator(
              strokeWidth: 1,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
            ),
          )
        else
          Expanded(
            child: Text(
              value ?? '-',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.black,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
      ],
    );
  }
}
