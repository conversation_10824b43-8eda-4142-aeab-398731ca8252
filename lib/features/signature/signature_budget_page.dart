import 'package:flutter/material.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/features/signature/esign_webview.dart';
import 'package:screenshot/screenshot.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:oktoast/oktoast.dart';

class SignatureBudgetPageWidget extends StatefulWidget {
  final String? budgetId;
  final String? packageType; //装修类型
  final String? contractType; //合同类型
  final String? packageId;
  final String? serviceId;
  final String? esignContractId;
  final VoidCallback? onSigningCompleted;

  const SignatureBudgetPageWidget({
    super.key,
    required this.budgetId,
    required this.packageType,
    required this.contractType,
    required this.packageId,
    this.serviceId,
    this.esignContractId,
    this.onSigningCompleted,
  });

  @override
  State<SignatureBudgetPageWidget> createState() =>
      _SignatureBudgetPageWidgetState();
}

class _SignatureBudgetPageWidgetState extends State<SignatureBudgetPageWidget> {
  Map<String, dynamic>? _baseInfoData;
  List<dynamic>? _roomParts;
  Map<String, dynamic>? _quoteData;
  Map<String, dynamic>? _personalizedQuoteData; // 添加个性化报价数据变量
  List<dynamic>? _materialDetail;
  List<dynamic>? _minutePackageData; // 微装套餐包数据
  Map<String, dynamic>? _minuteQuoteData; // 微装报价数据
  Map<String, dynamic>? _minutePersonalizedQuoteData; // 微装个性化报价数据
  List<dynamic>? _personalizedQuotaDetail; // 个性化定额明细数据
  List<dynamic>? _softPackageQuoteData; // 软装套餐报价数据
  List<dynamic>? _softPackagePersonalizedQuoteData; // 软装个性化报价数据
  int _selectedTabIndex = 0; // 0: 房间部位, 1: 报价, 2: 明细
  final Map<String, bool> _expandedRooms = {}; // 记录每个房间的展开状态
  final ScreenshotController _screenshotController = ScreenshotController();

  @override
  void initState() {
    super.initState();
    _getBaseInfoData();

    // 根据packageType和contractType决定调用哪个接口
    if (widget.packageType == 'minute') {
      // 微装类型
      if (widget.contractType == '1') {
        _getMinutePackageData();
        _getMinuteQuoteData();
      } else if (widget.contractType == '2') {
        _getMinutePersonalizedQuoteData();
      }
    } else if (widget.packageType == 'soft-loading') {
      // 软装类型
      if (widget.contractType == '1') {
        _getSoftPackageQuoteData();
      } else if (widget.contractType == '2') {
        _getSoftPackagePersonalizedQuoteData();
      }
    } else {
      // 整装/微整装类型
      if (widget.contractType == '1') {
        _getRoomParts();
        _getQuoteData();
      } else if (widget.contractType == '2') {
        _getPersonalizedQuoteData();
      }
    }

    // 当packageType为detail_list时，额外调用个性化定额明细接口，同时也要获取套餐费率数据
    if (widget.packageType == 'detail_list') {
      _getPersonalizedQuotaDetail();
      // 根据contractType获取对应的套餐费率数据
      if (widget.contractType == '1') {
        _getRoomParts();
        _getQuoteData();
      } else if (widget.contractType == '2') {
        _getPersonalizedQuoteData();
      }
    }

    _getMaterialDetail();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('预算详情'),
        backgroundColor: Colors.white,
        foregroundColor: const Color.fromRGBO(0, 0, 0, 1),
        elevation: 0,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
      ),
      body: Stack(
        children: [
          // 主要内容
          RefreshIndicator(
            onRefresh: _onRefresh,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                children: [
                  // 基本信息卡片
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16.0),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8.0),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          spreadRadius: 1,
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 标题行
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                Container(
                                  width: 4,
                                  height: 18,
                                  decoration: BoxDecoration(
                                    color: Colors.orange,
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  '基本信息',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black,
                                  ),
                                ),
                              ],
                            ),
                            Text(
                              _baseInfoData?['projectNumber'] ?? '',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        // 信息列表
                        _buildInfoRow('姓名/电话', _getNamePhone()),
                        _buildInfoRow('户型/面积', _getRoomTypeArea()),
                        _buildInfoRow('预算类型', _getBudgetType()),
                        _buildInfoRow(
                            '门店', _baseInfoData?['designDeptName'] ?? ''),
                        _buildInfoRow('设计师/设计师电话', _getDesignerInfo()),
                        _buildInfoRow('预算员/预算员电话', _getBudgeterInfo()),
                        _buildInfoRow('客服/客服电话', _getCustomerManagerInfo()),
                        _buildInfoRow('地址', _baseInfoData?['address'] ?? '',
                            isLast: true),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  // 当packageType为detail_list时，显示套餐基价
                  if (widget.packageType == 'detail_list') ...[
                    _buildPackagePriceCard(),
                    const SizedBox(height: 16),
                  ],
                  // 根据packageType和contractType显示不同内容
                  if (widget.packageType == 'minute') ...[
                    // 微装类型
                    if (widget.contractType == '1') ...[
                      _buildMinutePackageCard(),
                      const SizedBox(height: 16),
                    ] else if (widget.contractType == '2') ...[
                      _buildMinutePersonalizedQuoteCard(),
                      const SizedBox(height: 16),
                    ],
                  ] else if (widget.packageType == 'soft-loading') ...[
                    // 软装类型 - 直接显示套餐费率
                    _buildSoftPackageQuoteCard(),
                    const SizedBox(height: 16),
                  ] else ...[
                    // 整装/微整装类型
                    if (widget.contractType == '1') ...[
                      _buildRoomPartsCard(),
                      const SizedBox(height: 16),
                    ] else if (widget.contractType == '2') ...[
                      _buildPersonalizedQuoteCard(),
                      const SizedBox(height: 16),
                    ],
                  ],
                  // 材料明细卡片
                  _buildMaterialDetailCard(),
                  const SizedBox(height: 100), // 为底部按钮留出空间
                ],
              ),
            ),
          ),
          // 截图内容（位置在屏幕外）
          Positioned(
            left: -1000,
            top: -1000,
            child: Screenshot(
              controller: _screenshotController,
              child: Container(
                color: Colors.white,
                child: _buildScreenshotContent(),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        padding: EdgeInsets.only(
          left: 16.0,
          right: 16.0,
          top: 12.0,
          bottom: MediaQuery.of(context).padding.bottom + 12.0, // 安全区
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.2),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  // 预览按钮点击事件
                  _onPreviewPressed();
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 14.0),
                  side: const BorderSide(color: Colors.black),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                ),
                child: const Text(
                  '预览',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.black,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12.0),
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  // 生成合同按钮点击事件
                  _onGenerateContractPressed();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.black,
                  padding: const EdgeInsets.symmetric(vertical: 14.0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  elevation: 0,
                ),
                child: const Text(
                  '生成合同',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建房间部位卡片
  Widget _buildRoomPartsCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tab指示器
          _buildTabIndicator(),
          // 分割线
          Container(
            height: 1,
            color: Colors.grey.withValues(alpha: 0.2),
          ),
          // 内容区域
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _selectedTabIndex == 0
                ? _buildRoomPartsContent()
                : _buildQuoteContent(),
          ),
        ],
      ),
    );
  }

  // 构建套餐基价卡片
  Widget _buildPackagePriceCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Container(
                  width: 4,
                  height: 18,
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 8),
                const Text(
                  '套餐基价',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
          // 分割线
          Container(
            height: 1,
            color: Colors.grey.withValues(alpha: 0.2),
          ),
          // 内容区域
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildPackagePriceContent(),
          ),
        ],
      ),
    );
  }

  // 构建套餐基价内容
  Widget _buildPackagePriceContent() {
    if (_personalizedQuotaDetail == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_personalizedQuotaDetail!.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Text(
            '暂无套餐基价数据',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    return Column(
      children: [
        // 表格头部
        _buildPackagePriceTableHeader(),
        const SizedBox(height: 8),
        // 表格内容 - 遍历所有房间
        ..._personalizedQuotaDetail!.map<Widget>((room) {
          final roomName = room['roomName'] ?? '';
          final mainAndAuxMoney = (room['mainAndAuxMoney'] ?? 0).toDouble();
          final laborMoney = (room['laborMoney'] ?? 0).toDouble();
          final combinedMoney = (room['combinedMoney'] ?? 0).toDouble();

          return _buildPackagePriceTableRow(
            roomName,
            mainAndAuxMoney,
            laborMoney,
            combinedMoney,
          );
        }).toList(),
      ],
    );
  }

  // 构建套餐基价表格头部
  Widget _buildPackagePriceTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child: const Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              '房间部位',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '主辅材合计',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '人工合计',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '合计',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建套餐基价表格行
  Widget _buildPackagePriceTableRow(String roomName, double mainAndAuxMoney,
      double laborMoney, double combinedMoney) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
      decoration: BoxDecoration(
        border: Border(
          left: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
          right: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              roomName,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              mainAndAuxMoney.toStringAsFixed(0),
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              laborMoney.toStringAsFixed(0),
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              combinedMoney.toStringAsFixed(0),
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建软装套餐费率卡片
  Widget _buildSoftPackageQuoteCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Container(
                  width: 4,
                  height: 18,
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 8),
                const Text(
                  '套餐费率',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
          // 分割线
          Container(
            height: 1,
            color: Colors.grey.withValues(alpha: 0.2),
          ),
          // 内容区域
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildSoftPackageQuoteContent(),
          ),
        ],
      ),
    );
  }

  // 构建软装套餐费率内容
  Widget _buildSoftPackageQuoteContent() {
    List<dynamic>? priceInfo;

    // 根据contractType获取对应的数据
    if (widget.contractType == '1') {
      priceInfo = _softPackageQuoteData;
    } else if (widget.contractType == '2') {
      priceInfo = _softPackagePersonalizedQuoteData;
    }

    if (priceInfo == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (priceInfo.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Text(
            '暂无套餐费率数据',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 费率项目列表
        ...priceInfo.map<Widget>((item) => _buildPriceRateItem(item)).toList(),
      ],
    );
  }

  // 构建个性化报价卡片 - 只显示套餐费率
  Widget _buildPersonalizedQuoteCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Container(
                  width: 4,
                  height: 18,
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 8),
                const Text(
                  '套餐费率',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
          // 分割线
          Container(
            height: 1,
            color: Colors.grey.withValues(alpha: 0.2),
          ),
          // 内容区域
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildPersonalizedQuoteContent(),
          ),
        ],
      ),
    );
  }

  // 构建个性化报价内容
  Widget _buildPersonalizedQuoteContent() {
    if (_personalizedQuoteData == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final priceInfo =
        _personalizedQuoteData!['priceInfo'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 套餐费率 - 不显示标题，因为卡片已有标题
        _buildPriceRateSection(priceInfo, showTitle: false),
      ],
    );
  }

  // 构建微装套餐包卡片
  Widget _buildMinutePackageCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Container(
                  width: 4,
                  height: 18,
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 8),
                const Text(
                  '微装套餐包',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
          // 分割线
          Container(
            height: 1,
            color: Colors.grey.withValues(alpha: 0.2),
          ),
          // 内容区域
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildMinutePackageContent(),
          ),
        ],
      ),
    );
  }

  // 构建微装个性化报价卡片
  Widget _buildMinutePersonalizedQuoteCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Container(
                  width: 4,
                  height: 18,
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 8),
                const Text(
                  '套餐费率',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
          // 分割线
          Container(
            height: 1,
            color: Colors.grey.withValues(alpha: 0.2),
          ),
          // 内容区域
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildMinutePersonalizedQuoteContent(),
          ),
        ],
      ),
    );
  }

  // 构建微装套餐包内容
  Widget _buildMinutePackageContent() {
    if (_minutePackageData == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final priceInfo = _minuteQuoteData?['priceInfo'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 微装套餐包表格
        _buildMinutePackageTableHeader(),
        const SizedBox(height: 8),
        ..._minutePackageData!
            .map<Widget>((item) => _buildMinutePackageTableRow(item))
            .toList(),
        // 如果有套餐费率数据，显示套餐费率部分
        if (priceInfo.isNotEmpty) ...[
          const SizedBox(height: 24),
          _buildPriceRateSection(priceInfo, showTitle: true),
        ],
      ],
    );
  }

  // 构建微装个性化报价内容
  Widget _buildMinutePersonalizedQuoteContent() {
    if (_minutePersonalizedQuoteData == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final priceInfo =
        _minutePersonalizedQuoteData!['priceInfo'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 套餐费率 - 不显示标题，因为卡片已有标题
        _buildPriceRateSection(priceInfo, showTitle: false),
      ],
    );
  }

  // 构建Tab指示器
  Widget _buildTabIndicator() {
    return SizedBox(
      height: 40,
      child: Row(
        children: [
          const SizedBox(width: 16), // 左边距
          GestureDetector(
            onTap: () {
              setState(() {
                _selectedTabIndex = 0;
              });
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '房间部位',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: _selectedTabIndex == 0
                        ? FontWeight.bold
                        : FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: 60, // 固定宽度的指示器
                  height: 2,
                  color: _selectedTabIndex == 0
                      ? Colors.black
                      : Colors.transparent,
                ),
              ],
            ),
          ),
          const SizedBox(width: 32), // tab之间的间距
          GestureDetector(
            onTap: () {
              setState(() {
                _selectedTabIndex = 1;
              });
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '报价',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: _selectedTabIndex == 1
                        ? FontWeight.bold
                        : FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: 40, // 固定宽度的指示器
                  height: 2,
                  color: _selectedTabIndex == 1
                      ? Colors.black
                      : Colors.transparent,
                ),
              ],
            ),
          ),
          const Spacer(), // 右侧填充剩余空间
        ],
      ),
    );
  }

  // 构建房间部位内容
  Widget _buildRoomPartsContent() {
    return Column(
      children: [
        _buildTableHeader(),
        const SizedBox(height: 8),
        if (_roomParts != null) ..._buildRoomRows(),
      ],
    );
  }

  // 构建报价内容
  Widget _buildQuoteContent() {
    if (_quoteData == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    final packageInfo = _quoteData!['packageInfo'] as List<dynamic>? ?? [];
    final priceInfo = _quoteData!['priceInfo'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 报价表格
        _buildQuoteTable(packageInfo),
        const SizedBox(height: 24),
        // 套餐费率
        _buildPriceRateSection(priceInfo),
      ],
    );
  }

  // 构建报价表格
  Widget _buildQuoteTable(List<dynamic> packageInfo) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 表格头部
        _buildQuoteTableHeader(),
        const SizedBox(height: 8),
        // 表格内容
        ...packageInfo
            .map<Widget>((item) => _buildQuoteTableRow(item))
            .toList(),
      ],
    );
  }

  // 构建报价表格头部
  Widget _buildQuoteTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child: const Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              '套餐基价',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '单价',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '套餐基数',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '实际数量',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '实际金额',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建报价表格行
  Widget _buildQuoteTableRow(Map<String, dynamic> item) {
    final name = item['name'] ?? '';
    final price = item['price'];
    final cardinalNumber = item['cardinalNumber']?.toString() ?? '0';
    final actualQuantity = item['actualQuantity']?.toString() ?? '0';
    final actualPrice = item['actualPrice']?.toString() ?? '0';

    // 处理价格显示，如果是"--"则直接显示，否则显示数字
    String priceText = '';
    if (price == '--' || price == null) {
      priceText = '--';
    } else {
      priceText = price.toString();
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
      decoration: BoxDecoration(
        border: Border(
          left: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
          right: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              name,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              priceText,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              cardinalNumber,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              actualQuantity,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              actualPrice,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建套餐费率部分
  Widget _buildPriceRateSection(List<dynamic> priceInfo,
      {bool showTitle = true}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 套餐费率标题 - 根据参数决定是否显示
        if (showTitle) ...[
          const Text(
            '套餐费率',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 12),
        ],
        // 费率项目列表
        ...priceInfo.map<Widget>((item) => _buildPriceRateItem(item)).toList(),
      ],
    );
  }

  // 构建微装套餐包表格头部
  Widget _buildMinutePackageTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child: const Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              '名称',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '数量',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '单价',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '总额',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '备注',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建微装套餐包表格行
  Widget _buildMinutePackageTableRow(Map<String, dynamic> item) {
    final name = item['name'] ?? '';
    final number = item['number']?.toString() ?? '0';
    final price = item['price']?.toString() ?? '0';
    final totalPrice = item['totalPrice']?.toString() ?? '0';
    final remark = item['remark'] ?? '';

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
      decoration: BoxDecoration(
        border: Border(
          left: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
          right: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              name,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              number,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              price,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              totalPrice,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              remark,
              textAlign: TextAlign.left,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              // 移除maxLines和overflow限制，显示完整备注
            ),
          ),
        ],
      ),
    );
  }

  // 构建单个费率项目
  Widget _buildPriceRateItem(Map<String, dynamic> item) {
    final name = item['name'] ?? '';
    final price = item['price'] ?? item['value']; // 支持两种字段名
    final description = item['description'] ?? '';

    // 格式化价格显示
    String priceText = '';
    if (price is num) {
      if (price == 0) {
        priceText = '0';
      } else {
        priceText = price.toStringAsFixed(2);
      }
    } else if (price is String && price.isNotEmpty) {
      priceText = price;
    } else {
      priceText = '0';
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (description.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),
          Text(
            priceText,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // 构建表头
  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.3)),
        ),
      ),
      child: const Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              '房间名称',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '单位',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '地面积',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '墙面积',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建房间行列表
  List<Widget> _buildRoomRows() {
    return _roomParts!.map<Widget>((room) => _buildRoomRow(room)).toList();
  }

  // 构建单个房间行
  Widget _buildRoomRow(Map<String, dynamic> room) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.1)),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              room['roomName'] ?? '',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
          const Expanded(
            flex: 1,
            child: Text(
              '平方',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '${room['landArea'] ?? 0}',
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              '${room['wallArea'] ?? 0}',
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建信息行的辅助方法
  Widget _buildInfoRow(String label, String value, {bool isLast = false}) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 120,
              child: Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
            Expanded(
              child: Text(
                value,
                textAlign: TextAlign.right,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        if (!isLast) const SizedBox(height: 12),
      ],
    );
  }

  void _onPreviewPressed() async {
    // 检查数据是否已加载
    if (_baseInfoData == null) {
      showToast('数据加载中，请稍后再试');
      return;
    }

    try {
      // 等待更长时间确保UI完全渲染完成
      await Future.delayed(const Duration(milliseconds: 300));

      // 强制刷新UI
      if (mounted) {
        setState(() {});
        await Future.delayed(const Duration(milliseconds: 200));
      }

      final Uint8List? image = await _screenshotController.capture(
        delay: const Duration(milliseconds: 300),
        pixelRatio: 2.0,
      );

      if (image != null && image.isNotEmpty) {
        // 显示预览对话框
        _showPreviewDialog(image);
      } else {
        showToast('截图失败：无法生成图片');
      }
    } catch (e) {
      showToast('截图失败：${e.toString()}');
      debugPrint('截图失败: $e');
    }
  }

  // 显示预览对话框
  void _showPreviewDialog(Uint8List imageBytes) {
    showDialog(
      context: context,
      barrierColor: Colors.black.withValues(alpha: 0.8),
      builder: (BuildContext context) {
        return GestureDetector(
          onTap: () {
            Navigator.of(context).pop();
          },
          child: Dialog(
            backgroundColor: Colors.transparent,
            elevation: 0,
            child: GestureDetector(
              onTap: () {}, // 防止点击图片时关闭对话框
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.9,
                  maxHeight: MediaQuery.of(context).size.height * 0.8,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 预览图片
                    Flexible(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.memory(
                            imageBytes,
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                    // 操作按钮
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ElevatedButton.icon(
                          onPressed: () async {
                            // 保存图片
                            try {
                              final directory =
                                  await getApplicationDocumentsDirectory();
                              final imagePath =
                                  '${directory.path}/budget_preview_${DateTime.now().millisecondsSinceEpoch}.png';
                              final imageFile = File(imagePath);
                              await imageFile.writeAsBytes(imageBytes);
                              showToast('截图已保存');
                            } catch (e) {
                              showToast('保存失败');
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: Colors.black,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 20, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          icon: const Icon(Icons.save_alt, size: 20),
                          label: const Text('保存'),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton.icon(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[600],
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 20, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          icon: const Icon(Icons.close, size: 20),
                          label: const Text('关闭'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _onGenerateContractPressed() async {
    // 检查数据是否已加载
    if (_baseInfoData == null) {
      showToast('数据加载中，请稍后再试');
      return;
    }

    try {
      // 等待更长时间确保UI完全渲染完成
      await Future.delayed(const Duration(milliseconds: 300));

      // 强制刷新UI
      if (mounted) {
        setState(() {});
        await Future.delayed(const Duration(milliseconds: 200));
      }

      // 静默生成截图
      final Uint8List? image = await _screenshotController.capture(
        delay: const Duration(milliseconds: 300),
        pixelRatio: 2.0,
      );

      if (image != null && image.isNotEmpty) {
        // 将图片转换为base64
        final String base64Image = base64Encode(image);

        // 调用上传预算书接口
        await _uploadBudgetBook(base64Image);

        showToast('合同生成成功');
      } else {
        showToast('截图失败，无法生成合同');
      }
    } catch (e) {
      showToast('生成合同失败：${e.toString()}');
      debugPrint('生成合同失败: $e');
    }
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    List<Future<void>> futures = [
      _getBaseInfoData(),
      _getMaterialDetail(),
    ];

    // 根据packageType和contractType决定调用哪个接口
    if (widget.packageType == 'minute') {
      // 微装类型
      if (widget.contractType == '1') {
        futures.addAll([
          _getMinutePackageData(),
          _getMinuteQuoteData(),
        ]);
      } else if (widget.contractType == '2') {
        futures.add(_getMinutePersonalizedQuoteData());
      }
    } else if (widget.packageType == 'soft-loading') {
      // 软装类型
      if (widget.contractType == '1') {
        futures.add(_getSoftPackageQuoteData());
      } else if (widget.contractType == '2') {
        futures.add(_getSoftPackagePersonalizedQuoteData());
      }
    } else {
      // 整装/微整装类型
      if (widget.contractType == '1') {
        futures.addAll([
          _getRoomParts(),
          _getQuoteData(),
        ]);
      } else if (widget.contractType == '2') {
        futures.add(_getPersonalizedQuoteData());
      }
    }

    // 当packageType为detail_list时，额外调用个性化定额明细接口
    if (widget.packageType == 'detail_list') {
      futures.add(_getPersonalizedQuotaDetail());
    }

    await Future.wait(futures);
  }

  //获取基本信息数据
  Future<void> _getBaseInfoData() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/budget/basic/info',
        queryParameters: {
          'budgetId': widget.budgetId,
        },
      );
      if (response != null && mounted) {
        setState(() {
          _baseInfoData = response;
        });
      }
    } catch (e) {
      debugPrint('获取基本信息数据失败: $e');
    }
  }

  //获取房间部位
  Future<void> _getRoomParts() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/budget/room/info',
        queryParameters: {
          'packageId': widget.packageId ?? '',
          'type': (int.parse(widget.contractType ?? '1') - 1).toString(),
        },
      );
      if (response != null && mounted) {
        setState(() {
          _roomParts = response;
        });
      }
    } catch (e) {
      debugPrint('获取房间部位数据失败: $e');
    }
  }

  //整装/微整装套餐报价
  Future<void> _getQuoteData() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/budget/whole/package/price/info',
        queryParameters: {
          'actualPackageId': widget.packageId ?? '',
        },
      );
      if (response != null && mounted) {
        setState(() {
          _quoteData = response;
        });
      }
    } catch (e) {
      debugPrint('获取报价数据失败: $e');
    }
  }

  //整装/微整装个性化报价
  Future<void> _getPersonalizedQuoteData() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/budget/whole/custom/price/info',
        queryParameters: {'actualPackageId': widget.packageId ?? ''},
      );
      if (response != null && mounted) {
        setState(() {
          // 如果返回的是List，需要包装成Map格式
          if (response is List) {
            _personalizedQuoteData = {'priceInfo': response};
          } else {
            _personalizedQuoteData = response;
          }
        });
      }
      debugPrint('个性化报价数据: $_personalizedQuoteData');
    } catch (e) {
      debugPrint('获取个性化报价数据失败: $e');
    }
  }

  //微装套餐包
  Future<void> _getMinutePackageData() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/budget/micro/package/list',
        queryParameters: {'actualPackageId': widget.packageId ?? ''},
      );
      if (response != null && mounted) {
        setState(() {
          _minutePackageData = response;
        });
      }
      debugPrint('微装套餐包数据: $_minutePackageData');
    } catch (e) {
      debugPrint('获取微装套餐包数据失败: $e');
    }
  }

  //微装套餐包报价
  Future<void> _getMinuteQuoteData() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/budget/micro/package/price/info',
        queryParameters: {'actualPackageId': widget.packageId ?? ''},
      );
      if (response != null && mounted) {
        setState(() {
          // 包装成Map格式，确保priceInfo字段存在
          if (response is List) {
            _minuteQuoteData = {'priceInfo': response};
          } else if (response is Map && response.containsKey('data')) {
            _minuteQuoteData = {'priceInfo': response['data']};
          } else {
            _minuteQuoteData = {'priceInfo': response};
          }
        });
      }
      debugPrint('微装报价数据: $_minuteQuoteData');
    } catch (e) {
      debugPrint('获取微装报价数据失败: $e');
    }
  }

  //微装个性化报价
  Future<void> _getMinutePersonalizedQuoteData() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/budget/micro/custom/price/info',
        queryParameters: {'actualPackageId': widget.packageId ?? ''},
      );
      if (response != null && mounted) {
        setState(() {
          // 包装成Map格式，确保priceInfo字段存在
          if (response is List) {
            _minutePersonalizedQuoteData = {'priceInfo': response};
          } else if (response is Map && response.containsKey('data')) {
            _minutePersonalizedQuoteData = {'priceInfo': response['data']};
          } else {
            _minutePersonalizedQuoteData = {'priceInfo': response};
          }
        });
      }
      debugPrint('微装个性化报价数据: $_minutePersonalizedQuoteData');
    } catch (e) {
      debugPrint('获取微装个性化报价数据失败: $e');
    }
  }

  //材料明细
  Future<void> _getMaterialDetail() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/budget/material/info',
        queryParameters: {
          'packageId': widget.packageId ?? '',
          'type': (int.parse(widget.contractType ?? '1') - 1).toString(),
        },
      );
      if (response != null && mounted) {
        setState(() {
          _materialDetail = response;
        });
      }
    } catch (e) {
      debugPrint('获取材料明细数据失败: $e');
    }
  }

  //个性化定额明细
  Future<void> _getPersonalizedQuotaDetail() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/budget/custom/info',
        queryParameters: {
          'actualPackageId': widget.packageId ?? '',
        },
      );
      if (response != null && mounted) {
        setState(() {
          _personalizedQuotaDetail = response;
        });
      }
    } catch (e) {
      debugPrint('获取个性化定额明细失败: $e');
    }
  }

  // 软装套餐报价
  Future<void> _getSoftPackageQuoteData() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/budget/soft/package/price/info',
        queryParameters: {
          'actualPackageId': widget.packageId ?? '',
        },
      );
      if (response != null && mounted) {
        setState(() {
          _softPackageQuoteData = response;
        });
      }
    } catch (e) {
      debugPrint('获取软装套餐报价数据失败: $e');
    }
  }

  // 软装个性化报价
  Future<void> _getSoftPackagePersonalizedQuoteData() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/budget/soft/custom/price/info',
        queryParameters: {
          'actualPackageId': widget.packageId ?? '',
        },
      );
      if (response != null && mounted) {
        setState(() {
          _softPackagePersonalizedQuoteData = response;
        });
      }
    } catch (e) {
      debugPrint('获取软装套餐个性化报价数据失败: $e');
    }
  }

  //上传预算书
  Future<void> _uploadBudgetBook(String budgetBookBase64) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.post(
        '/api/signature/budget/upload',
        data: {
          'budgetBookFiletype': widget.contractType,
          'budgetId': widget.budgetId ?? '',
          'budgetBookFile': budgetBookBase64,
        },
      );
      if (response != null && mounted) {
        // 关闭加载对话框
        // Navigator.pop(context);
        // // 导航到电子签约页面
        await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ESignWebViewWidget(
              contractType: widget.contractType,
              serviceId: widget.serviceId,
              esignContractId: widget.esignContractId,
              budgetId: widget.budgetId,
            ),
          ),
        );

        // // 电子签约完成后执行回调
        if (mounted && widget.onSigningCompleted != null) {
          widget.onSigningCompleted!();
        }
      }
    } catch (e) {
      debugPrint('上传预算书失败: $e');
    }
  }

  // 获取姓名/电话信息
  String _getNamePhone() {
    if (_baseInfoData == null) return '';
    final name = _baseInfoData!['name'] ?? '';
    final phone = _baseInfoData!['phone'] ?? '';
    return '$name/$phone';
  }

  // 获取户型/面积信息
  String _getRoomTypeArea() {
    if (_baseInfoData == null) return '';
    final bedroomNumber = _baseInfoData!['bedroomNumber'] ?? 0;
    final livingRoomNumber = _baseInfoData!['livingRoomNumber'] ?? 0;
    final kitchenRoomNumber = _baseInfoData!['kitchenRoomNumber'] ?? 0;
    final toiletRoomNumber = _baseInfoData!['toiletRoomNumber'] ?? 0;
    final area = _baseInfoData!['calculatedArea'] ?? 0;

    return '$bedroomNumber室$livingRoomNumber厅$kitchenRoomNumber厨$toiletRoomNumber卫/$area m²';
  }

  // 获取预算类型信息
  String _getBudgetType() {
    if (_baseInfoData == null) return '';
    return _baseInfoData!['packageName'] ?? '';
  }

  // 获取设计师信息
  String _getDesignerInfo() {
    if (_baseInfoData == null) return '';
    final designerName = _baseInfoData!['designerName'] ?? '';
    final designerPhone = _baseInfoData!['designerPhone'] ?? '';
    return '$designerName/$designerPhone';
  }

  // 获取预算员信息
  String _getBudgeterInfo() {
    if (_baseInfoData == null) return '';
    final budgeterName = _baseInfoData!['budgeterName'] ?? '';
    final budgeterPhone = _baseInfoData!['budgeterPhone'] ?? '';
    return '$budgeterName/$budgeterPhone';
  }

  // 获取客服信息
  String _getCustomerManagerInfo() {
    if (_baseInfoData == null) return '';
    final customerManagerName = _baseInfoData!['customerManagerName'] ?? '';
    final customerManagerPhone = _baseInfoData!['customerManagerPhone'] ?? '';
    return '$customerManagerName/$customerManagerPhone';
  }

  // 构建截图内容
  Widget _buildScreenshotContent() {
    // 如果数据还没加载完成，返回空容器
    if (_baseInfoData == null) {
      return Container(
        width: 400,
        height: 600,
        color: Colors.white,
        child: const Center(
          child: Text('数据加载中...'),
        ),
      );
    }

    return Container(
      width: 400, // 固定宽度
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 基本信息卡片
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.0),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题行
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Container(
                          width: 4,
                          height: 18,
                          decoration: BoxDecoration(
                            color: Colors.orange,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Text(
                          '基本信息',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      _baseInfoData?['projectNumber'] ?? '',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // 信息列表
                _buildInfoRow('姓名/电话', _getNamePhone()),
                _buildInfoRow('户型/面积', _getRoomTypeArea()),
                _buildInfoRow('预算类型', _getBudgetType()),
                _buildInfoRow('门店', _baseInfoData?['designDeptName'] ?? ''),
                _buildInfoRow('设计师/设计师电话', _getDesignerInfo()),
                _buildInfoRow('预算员/预算员电话', _getBudgeterInfo()),
                _buildInfoRow('客服/客服电话', _getCustomerManagerInfo()),
                _buildInfoRow('地址', _baseInfoData?['address'] ?? '',
                    isLast: true),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // 报价卡片
          _buildScreenshotQuoteCard(),
          const SizedBox(height: 24),
          // 底部签名区域
          _buildSignatureSection(),
        ],
      ),
    );
  }

  // 构建截图用的报价卡片
  Widget _buildScreenshotQuoteCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 报价标题
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Container(
                  width: 4,
                  height: 18,
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  _getScreenshotCardTitle(),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
          // 分割线
          Container(
            height: 1,
            color: Colors.grey.withValues(alpha: 0.2),
          ),
          // 报价内容
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildScreenshotQuoteContent(),
          ),
        ],
      ),
    );
  }

  // 构建截图报价内容 - 根据packageType和contractType显示不同内容
  Widget _buildScreenshotQuoteContent() {
    if (widget.packageType == 'detail_list') {
      // detail_list类型 - 显示套餐基价和套餐费率
      return _buildDetailListScreenshotContent();
    } else if (widget.packageType == 'minute') {
      // 微装类型
      if (widget.contractType == '1') {
        return _buildMinutePackageContent();
      } else if (widget.contractType == '2') {
        return _buildMinutePersonalizedQuoteContent();
      }
    } else if (widget.packageType == 'soft-loading') {
      // 软装类型
      return _buildSoftPackageQuoteContent();
    } else {
      // 整装/微整装类型
      if (widget.contractType == '1') {
        return _buildQuoteContent();
      } else if (widget.contractType == '2') {
        return _buildPersonalizedQuoteContent();
      }
    }

    return Container();
  }

  // 构建detail_list类型的截图内容 - 包含套餐基价和套餐费率
  Widget _buildDetailListScreenshotContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 套餐基价部分
        _buildPackagePriceContent(),
        const SizedBox(height: 24),
        // 套餐费率部分
        if (widget.contractType == '2') ...[
          _buildPersonalizedQuoteContent(),
        ] else ...[
          _buildQuoteContent(),
        ],
      ],
    );
  }

  // 获取截图卡片标题
  String _getScreenshotCardTitle() {
    if (widget.packageType == 'detail_list') {
      // detail_list类型
      return '套餐基价';
    } else if (widget.packageType == 'minute') {
      // 微装类型
      if (widget.contractType == '1') {
        return '微装套餐包';
      } else if (widget.contractType == '2') {
        return '套餐费率';
      }
    } else if (widget.packageType == 'soft-loading') {
      // 软装类型
      return '套餐费率';
    } else {
      // 整装/微整装类型
      if (widget.contractType == '1') {
        return '报价';
      } else if (widget.contractType == '2') {
        return '套餐费率';
      }
    }
    return '报价';
  }

  // 构建底部签名区域
  Widget _buildSignatureSection() {
    final now = DateTime.now();
    final dateString =
        '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '甲方（签字）：',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '签约日期：$dateString',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建材料明细卡片
  Widget _buildMaterialDetailCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Container(
                  width: 4,
                  height: 18,
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 8),
                const Text(
                  '明细',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
          // 分割线
          Container(
            height: 1,
            color: Colors.grey.withValues(alpha: 0.2),
          ),
          // 内容区域
          _buildMaterialDetailContent(),
        ],
      ),
    );
  }

  // 构建材料明细内容
  Widget _buildMaterialDetailContent() {
    // 当packageType为detail_list时，显示个性化定额明细
    if (widget.packageType == 'detail_list') {
      return _buildPersonalizedQuotaDetailContent();
    }

    // 其他情况显示原有的材料明细
    if (_materialDetail == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_materialDetail!.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Text(
            '暂无材料明细',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: _materialDetail!
            .map<Widget>((room) => _buildRoomMaterialItem(room))
            .toList(),
      ),
    );
  }

  // 构建个性化定额明细内容
  Widget _buildPersonalizedQuotaDetailContent() {
    if (_personalizedQuotaDetail == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_personalizedQuotaDetail!.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: const Center(
          child: Text(
            '暂无个性化定额明细',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: _personalizedQuotaDetail!
            .map<Widget>((room) => _buildPersonalizedQuotaRoomItem(room))
            .toList(),
      ),
    );
  }

  // 构建个性化定额房间项目
  Widget _buildPersonalizedQuotaRoomItem(Map<String, dynamic> room) {
    final roomName = room['roomName'] ?? '';
    final rows = room['rows'] as List<dynamic>? ?? [];
    final isExpanded = _expandedRooms[roomName] ?? false;

    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        children: [
          // 房间名称和展开/折叠按钮
          InkWell(
            onTap: () {
              setState(() {
                _expandedRooms[roomName] = !isExpanded;
              });
            },
            child: Container(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    roomName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                  ),
                  Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: Colors.grey[600],
                  ),
                ],
              ),
            ),
          ),
          // 展开的定额明细列表
          if (isExpanded) ...[
            Container(
              height: 1,
              color: Colors.grey.withValues(alpha: 0.2),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: rows
                    .map<Widget>(
                        (item) => _buildPersonalizedQuotaDetailItem(item))
                    .toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // 构建个性化定额明细项目
  Widget _buildPersonalizedQuotaDetailItem(Map<String, dynamic> item) {
    final quotaName = item['quotaName'] ?? '';
    final quotaUnit = item['quotaUnit'] ?? '';
    final number = item['number']?.toString() ?? '0';
    final mainMaterialPrice = item['mainMaterialPrice']?.toString() ?? '0';
    final mainMaterialDescription = item['mainMaterialDescription'] ?? '';
    final auxiliaryMaterialPrice =
        item['auxiliaryMaterialPrice']?.toString() ?? '0';
    final auxiliaryMaterialDescription =
        item['auxiliaryMaterialDescription'] ?? '';
    final mainAndAuxTotalPrice =
        item['mainAndAuxTotalPrice']?.toString() ?? '0';
    final laborPrice = item['laborPrice']?.toString() ?? '0';
    final laborTotalPrice = item['laborTotalPrice']?.toString() ?? '0';
    final combinedPrice = item['combinedPrice']?.toString() ?? '0';

    return Container(
      margin: const EdgeInsets.only(bottom: 16.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 项目名称
          _buildDetailInfoRow('项目名称', quotaName),
          const SizedBox(height: 8),
          // 单位
          _buildDetailInfoRow('单位', quotaUnit),
          const SizedBox(height: 8),
          // 数量
          _buildDetailInfoRow('数量', number),
          const SizedBox(height: 8),
          // 主材单价
          _buildDetailInfoRow('主材单价', mainMaterialPrice),
          const SizedBox(height: 8),
          // 主材说明
          if (mainMaterialDescription.isNotEmpty) ...[
            _buildDetailInfoRow('主材说明', mainMaterialDescription,
                isDescription: true),
            const SizedBox(height: 8),
          ],
          // 辅材单价
          _buildDetailInfoRow('辅材单价', auxiliaryMaterialPrice),
          const SizedBox(height: 8),
          // 辅材说明
          _buildDetailInfoRow(
              '辅材说明',
              auxiliaryMaterialDescription.isNotEmpty
                  ? auxiliaryMaterialDescription
                  : '-'),
          const SizedBox(height: 8),
          // 主辅材合计
          _buildDetailInfoRow('主辅材合计', mainAndAuxTotalPrice),
          const SizedBox(height: 8),
          // 人工单价
          _buildDetailInfoRow('人工单价', laborPrice),
          const SizedBox(height: 8),
          // 人工合价
          _buildDetailInfoRow('人工合价', laborTotalPrice),
          const SizedBox(height: 8),
          // 合价
          _buildDetailInfoRow('合价', combinedPrice, isTotal: true),
        ],
      ),
    );
  }

  // 构建明细信息行
  Widget _buildDetailInfoRow(String label, String value,
      {bool isDescription = false, bool isTotal = false}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: isTotal ? Colors.red : Colors.black,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
            ),
            textAlign: isDescription ? TextAlign.left : TextAlign.right,
          ),
        ),
      ],
    );
  }

  // 构建单个房间的材料项目
  Widget _buildRoomMaterialItem(Map<String, dynamic> room) {
    final roomName = room['roomName'] ?? '';
    final rows = room['rows'] as List<dynamic>? ?? [];
    final isExpanded = _expandedRooms[roomName] ?? false;

    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        children: [
          // 房间名称和展开/折叠按钮
          InkWell(
            onTap: () {
              setState(() {
                _expandedRooms[roomName] = !isExpanded;
              });
            },
            child: Container(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    roomName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                    ),
                  ),
                  Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: Colors.grey[600],
                  ),
                ],
              ),
            ),
          ),
          // 展开的材料列表
          if (isExpanded) ...[
            Container(
              height: 1,
              color: Colors.grey.withValues(alpha: 0.2),
            ),
            ...rows.asMap().entries.map<Widget>((entry) {
              final index = entry.key;
              final material = entry.value;
              return Column(
                children: [
                  _buildMaterialItem(material),
                  // 如果不是最后一项，添加分割线
                  if (index < rows.length - 1)
                    Container(
                      height: 1,
                      color: Colors.grey.withValues(alpha: 0.2),
                      margin: const EdgeInsets.symmetric(horizontal: 16.0),
                    ),
                ],
              );
            }).toList(),
          ],
        ],
      ),
    );
  }

  // 构建单个材料项目
  Widget _buildMaterialItem(Map<String, dynamic> material) {
    final materialName = material['materialName'] ?? '';
    final unit = material['unit'] ?? '';
    final materialTypeNo = material['materialTypeNo'] ?? '';
    final brandName = material['brandName'] ?? '';
    final materialRemark = material['materialRemark'] ?? '';
    final sku = material['sku'] ?? '';
    final skuPic = material['skuPic'] ?? '';
    final quotaUsage = material['quotaUsage']?.toString() ?? '';

    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // 图片行
          Row(
            children: [
              const SizedBox(
                width: 60,
                child: Text(
                  '图片',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ),
              Expanded(
                child: skuPic.isNotEmpty
                    ? Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                              color: Colors.grey.withValues(alpha: 0.3)),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: Image.network(
                            skuPic,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey.withValues(alpha: 0.1),
                                child: const Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey,
                                ),
                              );
                            },
                          ),
                        ),
                      )
                    : Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          color: Colors.grey.withValues(alpha: 0.1),
                        ),
                        child: const Icon(
                          Icons.image,
                          color: Colors.grey,
                        ),
                      ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // 材料信息行
          _buildMaterialInfoRow(
              '型号', materialTypeNo.isEmpty ? '-' : materialTypeNo),
          _buildMaterialInfoRow(
              '名称', materialName.isEmpty ? '-' : materialName),
          _buildMaterialInfoRow('品牌', brandName.isEmpty ? '-' : brandName),
          _buildMaterialInfoRow('规格', sku.isEmpty ? '-' : sku),
          _buildMaterialInfoRow('单位', unit.isEmpty ? '-' : unit),
          _buildMaterialInfoRow('数量', quotaUsage.isEmpty ? '-' : quotaUsage),
          _buildMaterialInfoRow(
              '产品描述', materialRemark.isEmpty ? '-' : materialRemark,
              isLast: true),
        ],
      ),
    );
  }

  // 构建材料信息行
  Widget _buildMaterialInfoRow(String label, String value,
      {bool isLast = false}) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start, // 顶部对齐
          children: [
            SizedBox(
              width: 60,
              child: Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
            ),
            Expanded(
              child: Text(
                value,
                textAlign: TextAlign.right,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                ),
              ),
            ),
          ],
        ),
        if (!isLast) const SizedBox(height: 8),
      ],
    );
  }
}
