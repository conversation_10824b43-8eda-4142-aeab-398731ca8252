import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/empty_state.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';
import 'package:flutter_smarthome/features/signature/signature_payment.dart';
import 'package:flutter_smarthome/shared/dialogs/discount_info_dialog.dart';
import 'package:flutter_smarthome/shared/dialogs/payment_ratio_dialog.dart';
import 'package:flutter_smarthome/widgets/account_type_dialog.dart';
import 'package:flutter_smarthome/features/signature/scan_contract_pdf.dart';
import 'package:flutter/cupertino.dart';

// 快速签约状态管理单例类
class QuickSigningStateManager {
  static final QuickSigningStateManager _instance =
      QuickSigningStateManager._internal();
  factory QuickSigningStateManager() => _instance;
  QuickSigningStateManager._internal();

  // 状态变量
  final TextEditingController phoneController = TextEditingController();
  List<ProjectAddressData> projectAddressList = [];
  ProjectAddressData? selectedAddress;
  bool isLoading = false;
  Map<String, dynamic>? budgetInfo;
  Map<String, dynamic>? packageDiscountInfo; // contractType = 1 套餐包优惠信息
  Map<String, dynamic>? personalDiscountInfo; // contractType = 2 个性化优惠信息
  Map<String, dynamic>? firstDataSource; // contractType = 1 合同详情
  Map<String, dynamic>? secondDataSource; // contractType = 2 合同详情

  // 新增：缓存合同状态数据
  Map<String, dynamic>? packageContractStatus; // contractType = 1 合同状态
  Map<String, dynamic>? personalContractStatus; // contractType = 2 合同状态

  // 清空所有状态（可选，用于完全重置）
  void clearAll() {
    phoneController.clear();
    projectAddressList.clear();
    selectedAddress = null;
    isLoading = false;
    budgetInfo = null;
    packageDiscountInfo = null;
    personalDiscountInfo = null;
    firstDataSource = null;
    secondDataSource = null;
    packageContractStatus = null;
    personalContractStatus = null;
  }

  // 清空搜索相关状态（保留手机号）
  void clearSearchResults() {
    projectAddressList.clear();
    selectedAddress = null;
    budgetInfo = null;
    packageDiscountInfo = null;
    personalDiscountInfo = null;
    firstDataSource = null;
    secondDataSource = null;
    packageContractStatus = null;
    personalContractStatus = null;
  }

  void dispose() {
    phoneController.dispose();
  }
}

class QuickSigningBottomSheet extends StatefulWidget {
  const QuickSigningBottomSheet({super.key});

  @override
  State<QuickSigningBottomSheet> createState() =>
      _QuickSigningBottomSheetState();
}

// 项目地址数据模型
class ProjectAddressData {
  final String serviceId;
  final String projectId;
  final String designDeptName;
  final String? designerUserId;
  final String? designerUserName;
  final String? customerServiceUserId;
  final String? customerServiceUserName;
  final String address;

  ProjectAddressData({
    required this.serviceId,
    required this.projectId,
    required this.designDeptName,
    this.designerUserId,
    this.designerUserName,
    this.customerServiceUserId,
    this.customerServiceUserName,
    required this.address,
  });

  factory ProjectAddressData.fromJson(Map<String, dynamic> json) {
    return ProjectAddressData(
      serviceId: json['serviceId']?.toString() ?? '',
      projectId: json['projectId']?.toString() ?? '',
      designDeptName: json['designDeptName']?.toString() ?? '',
      designerUserId: json['designerUserId']?.toString(),
      designerUserName: json['designerUserName']?.toString(),
      customerServiceUserId: json['customerServiceUserId']?.toString(),
      customerServiceUserName: json['customerServiceUserName']?.toString(),
      address: json['address']?.toString() ?? '',
    );
  }
}

class _QuickSigningBottomSheetState extends State<QuickSigningBottomSheet>
    with WidgetsBindingObserver {
  late final QuickSigningStateManager _stateManager;
  String? _e1signContractId;
  String? _e2signContractId;
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _stateManager = QuickSigningStateManager();
    WidgetsBinding.instance.addObserver(this);

    // 确保页面初始化时键盘不会自动弹起
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        FocusScope.of(context).unfocus();
        _searchFocusNode.unfocus();
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _searchFocusNode.dispose();
    // 不在这里dispose状态管理器，因为它是单例
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // 当应用恢复时（从其他页面返回），确保焦点被移除，防止键盘弹起
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          FocusScope.of(context).unfocus();
          _searchFocusNode.unfocus();
        }
      });
    }
  }

  // 添加一个通用的键盘收起方法
  void _dismissKeyboard() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        FocusScope.of(context).unfocus();
        _searchFocusNode.unfocus();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // 点击空白区域收起键盘
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: ClipRRect(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
            minHeight: 400.h,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部拖拽指示器
              _buildDragHandle(),

              // 标题栏
              _buildHeader(),

              // 内容区域
              Expanded(
                child: _buildContent(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建拖拽指示器
  Widget _buildDragHandle() {
    return Container(
      width: 36.w,
      height: 4.h,
      margin: EdgeInsets.only(top: 8.h),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2.r),
      ),
    );
  }

  // 构建标题栏
  Widget _buildHeader() {
    return Container(
      height: 60.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey[200]!,
            width: 1.h,
          ),
        ),
      ),
      child: Row(
        children: [
          // 左侧标题
          Text(
            '快速签约',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),

          // 刷新按钮
          SizedBox(width: 8.w),
          InkWell(
            onTap: _stateManager.isLoading ? null : _handleRefresh,
            borderRadius: BorderRadius.circular(12.r),
            child: Padding(
              padding: EdgeInsets.all(4.w),
              child: _stateManager.isLoading
                  ? SizedBox(
                      width: 16.w,
                      height: 16.w,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.grey[500]!,
                        ),
                      ),
                    )
                  : Icon(
                      Icons.refresh,
                      size: 16.w,
                      color: Colors.grey[500],
                    ),
            ),
          ),

          const Spacer(),

          // 右侧关闭按钮
          InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            borderRadius: BorderRadius.circular(20.r),
            child: Icon(
              Icons.close,
              size: 24.w,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  // 构建内容区域
  Widget _buildContent() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 搜索框
          _buildSearchBox(),

          SizedBox(height: 16.h),

          // 地址信息
          _buildAddressInfo(),

          SizedBox(height: 16.h),

          // 预算信息
          _buildBudgetInfo(),

          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  // 构建搜索框
  Widget _buildSearchBox() {
    return Container(
      height: 44.h,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          SizedBox(width: 12.w),
          Icon(
            Icons.search,
            size: 20.w,
            color: Colors.grey[500],
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: TextField(
              controller: _stateManager.phoneController,
              focusNode: _searchFocusNode,
              decoration: InputDecoration(
                hintText: '输入手机号查询',
                hintStyle: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.black,
              ),
              keyboardType: TextInputType.phone,
              textInputAction: TextInputAction.search,
              onSubmitted: (value) {
                // 当用户点击搜索按钮时，执行搜索并收起键盘
                _handleSearch(value.trim());
                FocusScope.of(context).unfocus();
              },
              onChanged: (value) {
                // 当输入内容变化时，触发UI更新以显示/隐藏清除按钮
                setState(() {});
              },
            ),
          ),
          // 一键清除按钮 - 只有当输入框有内容时才显示
          if (_stateManager.phoneController.text.isNotEmpty)
            GestureDetector(
              onTap: () {
                // 清除输入框内容
                _stateManager.phoneController.clear();
                // 重置到初始状态
                _resetToInitialState();
                // 触发UI更新
                setState(() {});
              },
              child: Container(
                margin: EdgeInsets.only(right: 8.w),
                padding: EdgeInsets.all(6.w),
                child: Icon(
                  Icons.clear,
                  size: 18.w,
                  color: Colors.grey[500],
                ),
              ),
            ),
          GestureDetector(
            onTap: () {
              // 先收起键盘，再执行搜索
              FocusScope.of(context).unfocus();
              _handleSearch(_stateManager.phoneController.text.trim());
            },
            child: Container(
              margin: EdgeInsets.only(right: 8.w),
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: HexColor('#FFB26D'),
                borderRadius: BorderRadius.circular(6.r),
              ),
              child: _stateManager.isLoading
                  ? SizedBox(
                      width: 16.w,
                      height: 16.h,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      '搜索',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建地址信息
  Widget _buildAddressInfo() {
    return GestureDetector(
      onTap: () {
        debugPrint(
            '地址信息被点击，地址列表长度: ${_stateManager.projectAddressList.length}');
        if (_stateManager.projectAddressList.isNotEmpty) {
          _showAddressSelectionDialog();
        } else {
          _showMessage('请先搜索手机号查询项目地址');
        }
      },
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: Colors.grey[200]!,
            width: 1.w,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.location_on,
              size: 16.w,
              color: Colors.grey[600],
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                _stateManager.selectedAddress?.address ?? '请先搜索手机号查询项目地址',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: _stateManager.selectedAddress != null
                      ? Colors.black
                      : Colors.grey[500],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (_stateManager.projectAddressList.isNotEmpty)
              Icon(
                Icons.arrow_forward_ios,
                size: 14.w,
                color: Colors.grey[400],
              ),
          ],
        ),
      ),
    );
  }

  // 构建预算信息
  Widget _buildBudgetInfo() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1.w,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 4.w,
                height: 16.h,
                decoration: BoxDecoration(
                  color: HexColor('#FFB26D'),
                  borderRadius: BorderRadius.circular(2.r),
                ),
              ),
              SizedBox(width: 8.w),
              Text(
                '预算信息',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
              const Spacer(),
              if (_stateManager.budgetInfo != null)
                Text(
                  '客户编号：${_stateManager.budgetInfo!['projectNumber'] ?? ''}',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: HexColor('#FFB26D'),
                  ),
                ),
            ],
          ),
          SizedBox(height: 16.h),
          if (_stateManager.budgetInfo == null)
            EmptyStateWidget(onRefresh: () {
              if (_stateManager.selectedAddress != null) {
                _loadBudgetInfo(_stateManager.selectedAddress!.serviceId);
              }
            })
          else
            ..._buildBudgetRows(),
        ],
      ),
    );
  }

  // 构建预算信息行列表
  List<Widget> _buildBudgetRows() {
    if (_stateManager.budgetInfo == null) return [];

    final budget = _stateManager.budgetInfo!;
    List<Widget> rows = [];

    // 预算名称（高亮显示）
    if (budget['packageName'] != null) {
      rows.add(_buildBudgetRow('预算名称', budget['packageName'],
          isHighlight: true,
          packageGroupType: budget['packageGroupTypeDisplay']));
      rows.add(SizedBox(height: 8.h));
    }

    // 客户姓名
    if (budget['name'] != null) {
      rows.add(_buildBudgetRow('客户姓名', budget['name']));
      rows.add(SizedBox(height: 8.h));
    }

    // 设计师
    if (budget['designerName'] != null) {
      rows.add(_buildBudgetRow('设计师', budget['designerName']));
      rows.add(SizedBox(height: 8.h));
    }

    // 设计部门
    if (budget['designDeptName'] != null) {
      rows.add(_buildBudgetRow('设计部门', budget['designDeptName']));
      rows.add(SizedBox(height: 8.h));
    }

    // 预算员工
    if (budget['budgeterName'] != null) {
      rows.add(_buildBudgetRow('预算员工', budget['budgeterName']));
      rows.add(SizedBox(height: 8.h));
    }

    // 房型/户型
    String roomTypeText = _buildRoomTypeText(budget);
    if (roomTypeText.isNotEmpty) {
      rows.add(_buildBudgetRow('房型/户型', roomTypeText));
      rows.add(SizedBox(height: 8.h));
    }

    // 预算面积
    if (budget['calculatedArea'] != null) {
      rows.add(_buildBudgetRow('预算面积', '${budget['calculatedArea']}㎡'));
      rows.add(SizedBox(height: 8.h));
    }

    // 预算时间
    if (budget['createTime'] != null) {
      rows.add(_buildBudgetRow('预算时间', budget['createTime']));
      rows.add(SizedBox(height: 8.h));
    }

    // 预算合计 - 计算 packageBudgetPrice 和 personalBudgetPrice 的总和
    double totalBudget = 0.0;

    // 获取套餐预算价格
    if (budget['packageBudgetPrice'] != null) {
      double packagePrice = 0.0;
      if (budget['packageBudgetPrice'] is num) {
        packagePrice = budget['packageBudgetPrice'].toDouble();
      } else if (budget['packageBudgetPrice'] is String) {
        packagePrice = double.tryParse(budget['packageBudgetPrice']) ?? 0.0;
      }
      totalBudget += packagePrice;
    }

    // 获取个人预算价格
    if (budget['personalBudgetPrice'] != null) {
      double personalPrice = 0.0;
      if (budget['personalBudgetPrice'] is num) {
        personalPrice = budget['personalBudgetPrice'].toDouble();
      } else if (budget['personalBudgetPrice'] is String) {
        personalPrice = double.tryParse(budget['personalBudgetPrice']) ?? 0.0;
      }
      totalBudget += personalPrice;
    }

    // 只有当总预算大于0时才显示
    if (totalBudget > 0) {
      String priceText = _formatPrice(totalBudget);
      rows.add(_buildBudgetRow('预算合计', priceText, isRedPrice: true));
      rows.add(SizedBox(height: 16.h));

      // 添加详细预算信息UI
      rows.add(_buildDetailedBudgetInfo(budget));
    }

    return rows;
  }

  // 构建房型文本
  String _buildRoomTypeText(Map<String, dynamic> budget) {
    List<String> parts = [];

    // 使用 roomTypeDisplay 显示房型类型
    if (budget['roomTypeDisplay'] != null &&
        budget['roomTypeDisplay'].toString().isNotEmpty) {
      parts.add(budget['roomTypeDisplay'].toString());
    }

    // 户型信息 - 始终显示所有房间数量
    List<String> roomInfo = [];

    // 卧室数量
    int bedroomCount = budget['bedroomNumber'] ?? 0;
    roomInfo.add('$bedroomCount室');

    // 客厅数量
    int livingRoomCount = budget['livingRoomNumber'] ?? 0;
    roomInfo.add('$livingRoomCount厅');

    // 厨房数量
    int kitchenCount = budget['kitchenRoomNumber'] ?? 0;
    roomInfo.add('$kitchenCount厨');

    // 卫生间数量
    int toiletCount = budget['toiletRoomNumber'] ?? 0;
    roomInfo.add('$toiletCount卫');

    // 将房间信息组合
    if (roomInfo.isNotEmpty) {
      parts.add(roomInfo.join(''));
    }

    return parts.join('/');
  }

  // 格式化价格
  String _formatPrice(dynamic price, {int decimalPlaces = 0}) {
    if (price == null) return '';

    // 转换为数字
    num priceNum = 0;
    if (price is num) {
      priceNum = price;
    } else if (price is String) {
      priceNum = num.tryParse(price) ?? 0;
    }

    // 格式化为指定小数位数
    String priceStr = priceNum.toStringAsFixed(decimalPlaces);

    // 分离整数部分和小数部分
    List<String> parts = priceStr.split('.');
    String integerPart = parts[0];
    String decimalPart = parts.length > 1 ? parts[1] : '';

    // 格式化整数部分为千分位
    String formatted = '';
    int count = 0;

    for (int i = integerPart.length - 1; i >= 0; i--) {
      if (count > 0 && count % 3 == 0) {
        formatted = ',$formatted';
      }
      formatted = integerPart[i] + formatted;
      count++;
    }

    // 如果有小数部分，添加小数点和小数部分
    if (decimalPlaces > 0) {
      formatted = '$formatted.$decimalPart';
    }

    return '$formatted元';
  }

  // 构建预算行
  Widget _buildBudgetRow(String label, String value,
      {bool isHighlight = false,
      String? packageGroupType,
      bool isRedPrice = false}) {
    return Row(
      children: [
        SizedBox(
          width: 80.w,
          child: Text(
            '$label：',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              color: isRedPrice
                  ? Colors.red
                  : (isHighlight ? HexColor('#FFB26D') : Colors.black),
              fontWeight: (isHighlight || isRedPrice)
                  ? FontWeight.w600
                  : FontWeight.normal,
            ),
          ),
        ),
        if (isHighlight)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: HexColor('#FFB26D'),
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Text(
              packageGroupType ?? '',
              style: TextStyle(
                fontSize: 10.sp,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }

  // 构建详细预算信息UI
  Widget _buildDetailedBudgetInfo(Map<String, dynamic> budget) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 套餐包部分
          _buildPackageSection(budget),

          SizedBox(height: 24.h),

          // 个性化部分
          _buildPersonalizedSection(budget),
        ],
      ),
    );
  }

  // 构建套餐包部分
  Widget _buildPackageSection(Map<String, dynamic> budget) {
    double packagePrice = 0.0;
    if (budget['packageBudgetPrice'] != null) {
      if (budget['packageBudgetPrice'] is num) {
        packagePrice = budget['packageBudgetPrice'].toDouble();
      } else if (budget['packageBudgetPrice'] is String) {
        packagePrice = double.tryParse(budget['packageBudgetPrice']) ?? 0.0;
      }
    }

    // 获取套餐包优惠金额，默认为0
    double packageDiscountAmount = 0.0;
    if (_stateManager.packageDiscountInfo != null &&
        _stateManager.packageDiscountInfo!['discountPrice'] != null) {
      if (_stateManager.packageDiscountInfo!['discountPrice'] is num) {
        packageDiscountAmount =
            _stateManager.packageDiscountInfo!['discountPrice'].toDouble();
      } else if (_stateManager.packageDiscountInfo!['discountPrice']
          is String) {
        String discountPriceStr =
            _stateManager.packageDiscountInfo!['discountPrice'];
        if (discountPriceStr.isNotEmpty) {
          packageDiscountAmount = double.tryParse(discountPriceStr) ?? 0.0;
        }
      }
    }

    // 计算套餐包合计：金额 - 优惠金额
    double packageTotal = packagePrice - packageDiscountAmount;

    // 检查套餐包预算价格是否为null或0，如果是则不显示按钮
    bool shouldHideButtons = false;
    if (_stateManager.budgetInfo != null) {
      double packageBudgetPrice = 0.0;
      if (_stateManager.budgetInfo!['packageBudgetPrice'] != null) {
        if (_stateManager.budgetInfo!['packageBudgetPrice'] is num) {
          packageBudgetPrice =
              _stateManager.budgetInfo!['packageBudgetPrice'].toDouble();
        } else if (_stateManager.budgetInfo!['packageBudgetPrice'] is String) {
          packageBudgetPrice = double.tryParse(
                  _stateManager.budgetInfo!['packageBudgetPrice']) ??
              0.0;
        }
      }
      shouldHideButtons = packageBudgetPrice == 0.0;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 套餐包标题 - 居中显示
        Center(
          child: Text(
            '套餐包',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
        ),
        SizedBox(height: 16.h),

        // 金额行
        _buildBudgetDetailRow('金额', _formatPrice(packagePrice)),
        SizedBox(height: 12.h),

        // 优惠金额行
        _buildBudgetDetailRow(
            '优惠金额', _formatPrice(packageDiscountAmount, decimalPlaces: 2)),
        SizedBox(height: 12.h),

        // 合计行
        Row(
          children: [
            Text(
              '合计：',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
            Text(
              _formatPrice(packageTotal),
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFFE53E3E), // 使用更鲜艳的红色
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),

        // 当套餐包预算价格不为null或0时才显示按钮
        if (!shouldHideButtons) _buildCombinedButtonRow('1'),
      ],
    );
  }

  // 构建个性化部分
  Widget _buildPersonalizedSection(Map<String, dynamic> budget) {
    double personalPrice = 0.0;
    if (budget['personalBudgetPrice'] != null) {
      if (budget['personalBudgetPrice'] is num) {
        personalPrice = budget['personalBudgetPrice'].toDouble();
      } else if (budget['personalBudgetPrice'] is String) {
        personalPrice = double.tryParse(budget['personalBudgetPrice']) ?? 0.0;
      }
    }

    // 获取个性化优惠金额，默认为0
    double personalDiscountAmount = 0.0;
    if (_stateManager.personalDiscountInfo != null &&
        _stateManager.personalDiscountInfo!['discountPrice'] != null) {
      if (_stateManager.personalDiscountInfo!['discountPrice'] is num) {
        personalDiscountAmount =
            _stateManager.personalDiscountInfo!['discountPrice'].toDouble();
      } else if (_stateManager.personalDiscountInfo!['discountPrice']
          is String) {
        String discountPriceStr =
            _stateManager.personalDiscountInfo!['discountPrice'];
        if (discountPriceStr.isNotEmpty) {
          personalDiscountAmount = double.tryParse(discountPriceStr) ?? 0.0;
        }
      }
    }

    // 计算个性化合计：金额 - 优惠金额
    double personalTotal = personalPrice - personalDiscountAmount;

    // 检查个性化预算价格是否为null或0，如果是则不显示按钮
    bool shouldHidePersonalButtons = personalPrice == 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 个性化标题 - 居中显示
        Center(
          child: Text(
            '个性化',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
        ),
        SizedBox(height: 16.h),

        // 金额行
        _buildBudgetDetailRow('金额', _formatPrice(personalPrice)),
        SizedBox(height: 12.h),

        // 优惠金额行
        _buildBudgetDetailRow(
            '优惠金额', _formatPrice(personalDiscountAmount, decimalPlaces: 2)),
        SizedBox(height: 12.h),

        // 合计行
        Row(
          children: [
            Text(
              '合计：',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
            Text(
              _formatPrice(personalTotal),
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFFE53E3E), // 使用更鲜艳的红色
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        SizedBox(height: 16.h),

        // 当个性化预算价格不为null或0时才显示按钮
        if (!shouldHidePersonalButtons) _buildCombinedButtonRow('2'),
      ],
    );
  }

  // 构建预算详情行
  Widget _buildBudgetDetailRow(String label, String value) {
    return Row(
      children: [
        Text(
          '$label：',
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[600],
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[800],
            fontWeight: FontWeight.w400,
          ),
        ),
      ],
    );
  }

  // 检查合同是否已签约
  bool _isContractAlreadySigned(String contractType) {
    if (_stateManager.budgetInfo == null) return false;

    if (contractType == '1') {
      // 一期：budgetData['firstStatus'] == '1' 等于 alreadySign == false
      return _stateManager.budgetInfo!['firstStatus'] != '1';
    } else if (contractType == '2') {
      // 二期：budgetData['secondStatus'] == '1' 等于 alreadySign == false
      return _stateManager.budgetInfo!['secondStatus'] != '1';
    }
    return false;
  }

  // 构建组合按钮行（申请优惠 + 合同操作按钮）
  Widget _buildCombinedButtonRow(String contractType) {
    // 直接使用缓存的状态数据构建按钮，避免FutureBuilder重复执行
    return _buildCombinedButtonsSync(contractType);
  }

  // 构建组合按钮逻辑（同步版本，使用缓存的状态数据）
  Widget _buildCombinedButtonsSync(String contractType) {
    if (_stateManager.selectedAddress == null ||
        _stateManager.budgetInfo == null) {
      return Container();
    }

    // 使用缓存的合同状态数据
    Map<String, dynamic>? contractStatus;
    if (contractType == '1') {
      contractStatus = _stateManager.packageContractStatus;
    } else if (contractType == '2') {
      contractStatus = _stateManager.personalContractStatus;
    }

    // 判断是否已签约
    bool alreadySign = false;
    if (contractType == '1') {
      // 一期：budgetData['firstStatus'] == '1' 等于 alreadySign == false
      alreadySign = _stateManager.budgetInfo!['firstStatus'] != '1';
    } else if (contractType == '2') {
      // 二期：budgetData['secondStatus'] == '1' 等于 alreadySign == false
      alreadySign = _stateManager.budgetInfo!['secondStatus'] != '1';
    }

    // 判断E签宝合同状态
    bool eContractStatus = false;
    if (contractStatus != null) {
      eContractStatus =
          contractStatus['isSign'] == '0' || contractStatus['isSign'] == '1';
    }

    return _buildCombinedButtonsByStatus(
        alreadySign, eContractStatus, contractType);
  }

  // 根据状态构建组合按钮行
  Widget _buildCombinedButtonsByStatus(
      bool alreadySign, bool eContractStatus, String contractType) {
    List<Widget> buttons = [];

    // 根据不同状态添加按钮
    if (alreadySign) {
      // 状态1：已签约 - 显示查看合同和去支付按钮
      buttons.add(_buildDynamicButton('查看合同', () {
        _handleViewContract(contractType, true);
      }));
      buttons.add(_buildDynamicButton('去支付', () async {
        // 点击去支付按钮时先收起键盘
        _dismissKeyboard();

        final contractData = await _requestData(
            _stateManager.selectedAddress!.serviceId, contractType);

        if (contractData == null) {
          _showMessage('获取合同详情失败，请重试');
          return;
        }

        if (!mounted) return;

        final budget = _stateManager.budgetInfo!;

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SignaturePaymentWidget(
              contractData: contractData,
              customerName: budget['name'],
              customerAddress: budget['address'],
            ),
          ),
        ).then((_) {
          // 从支付页面返回后，确保键盘不会自动弹起
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              FocusScope.of(context).unfocus();
              _searchFocusNode.unfocus();
            }
          });
          // 刷新合同状态
          _refreshContractStatus();
        });
      }));
    } else if (!alreadySign && eContractStatus) {
      // 状态2：未签约但合同已填写 - 显示申请优惠、拟合同、查看合同、去签约按钮
      if (!_isContractAlreadySigned(contractType)) {
        buttons.add(_buildDynamicDiscountButton('申请优惠', contractType));
      }
      buttons.add(_buildDynamicButton('拟合同', () {
        _handleContractDraft(contractType);
      }));
      buttons.add(_buildDynamicButton('查看合同', () {
        _handleViewContract(contractType, false);
      }));
      buttons.add(_buildDynamicButton('去签约', () {
        // 点击去签约按钮时先收起键盘
        _dismissKeyboard();

        showDialog(
          context: context,
          builder: (context) => AccountTypeDialog(
            initialValue: 'private',
            onConfirm: (selectedType,
                {String? operatorName, String? operatorPhone}) {
              // 处理确认逻辑
              debugPrint('选择的账户类型：$selectedType');
              if (selectedType == 'public') {
                debugPrint('经办人姓名：$operatorName');
                debugPrint('经办人电话：$operatorPhone');
              }
              // 调用签约接口
              _requestDirectSign(
                contractType == '1'
                    ? _e1signContractId ?? ''
                    : _e2signContractId ?? '',
                selectedType == 'private' ? '0' : '1',
                operatorName ?? '',
                operatorPhone ?? '',
              );
            },
          ),
        ).then((_) {
          // 对话框关闭后确保键盘不弹起
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              FocusScope.of(context).unfocus();
              _searchFocusNode.unfocus();
            }
          });
        });
      }));
    } else {
      // 状态3：未签约且合同未填写 - 显示申请优惠和拟合同按钮
      if (!_isContractAlreadySigned(contractType)) {
        buttons.add(_buildDynamicDiscountButton('申请优惠', contractType));
      }
      buttons.add(_buildDynamicButton('拟合同', () {
        _handleContractDraft(contractType);
      }));
    }

    return _buildDynamicButtonRow(buttons);
  }

  // 构建动态宽度的按钮行
  Widget _buildDynamicButtonRow(List<Widget> buttons) {
    if (buttons.isEmpty) return Container();

    return Row(
      children: buttons
          .asMap()
          .entries
          .map((entry) {
            int index = entry.key;
            Widget button = entry.value;

            return [
              if (index > 0) SizedBox(width: 8.w), // 按钮间距
              Expanded(child: button),
            ];
          })
          .expand((x) => x)
          .toList(),
    );
  }

  // 构建动态按钮（合同操作按钮样式）
  Widget _buildDynamicButton(String text, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 32.h, // 固定按钮高度
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFF4A90E2), width: 1),
          borderRadius: BorderRadius.circular(6.r),
          color: Colors.white,
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 13.sp,
              color: const Color(0xFF4A90E2),
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  // 构建动态申请优惠按钮（橙色样式）
  Widget _buildDynamicDiscountButton(String text, String discountTypeValue) {
    return GestureDetector(
      onTap: () {
        // 点击申请优惠按钮时先收起键盘
        _dismissKeyboard();

        DiscountInfoDialog.show(
          _stateManager.selectedAddress?.serviceId ?? '',
          discountTypeValue,
          originalPrice: discountTypeValue == '1'
              ? _stateManager.budgetInfo!['packageBudgetPrice']?.toString()
              : _stateManager.budgetInfo!['personalBudgetPrice']?.toString(),
          context,
          onSubmit:
              (discountType, packageType, discountAmount, discountContent) {},
          onClose: () {
            // 对话框关闭时刷新预算数据并确保键盘不弹起
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                FocusScope.of(context).unfocus();
                _searchFocusNode.unfocus();
              }
            });
            if (_stateManager.selectedAddress != null) {
              _loadBudgetInfo(_stateManager.selectedAddress!.serviceId);
            }
          },
        );
      },
      child: Container(
        height: 32.h, // 与其他按钮保持一致的高度
        decoration: BoxDecoration(
          color: const Color(0xFFFF6B35), // 橙色背景
          borderRadius: BorderRadius.circular(6.r),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFFF6B35).withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 13.sp,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  // 处理拟合同按钮点击
  void _handleContractDraft(String contractType) async {
    if (_stateManager.budgetInfo == null ||
        _stateManager.selectedAddress == null) {
      _showMessage('请先选择项目地址并获取预算信息');
      return;
    }

    // 点击拟合同按钮时先收起键盘
    _dismissKeyboard();

    try {
      // 获取合同详情
      final contractData = await _requestData(
          _stateManager.selectedAddress!.serviceId, contractType);

      if (contractData == null) {
        _showMessage('获取合同详情失败，请重试');
        return;
      }

      // 计算优惠后的金额（预算金额 - 优惠金额）
      double totalAmount = _calculateDiscountedAmount(contractType);

      String companyName = contractData['incomeCompanyName']?.toString() ?? '';
      String companyId = contractData['incomeCompany']?.toString() ?? '';
      String budgetId = _stateManager.budgetInfo!['budgetId']?.toString() ?? '';

      // 检查组件是否仍然挂载
      if (!mounted) return;

      PaymentRatioDialog.show(
        context,
        totalAmount: totalAmount,
        contractType: contractType,
        companyName: companyName,
        companyId: companyId,
        budgetId: budgetId,
        packageType:
            _stateManager.budgetInfo!['packageGroupType']?.toString() ?? '',
        packageId:
            _stateManager.budgetInfo!['actualPackageId']?.toString() ?? '',
        esignContractId:
            contractType == '1' ? _e1signContractId : _e2signContractId,
        serviceId: _stateManager.selectedAddress!.serviceId,
        startDate:
            contractData['contractConstructionStartDate']?.toString() ?? '',
        endDate: contractData['contractConstructionEndDate']?.toString() ?? '',
        payType: contractData['payType']?.toString() ?? '',
        paymentList: _convertPaymentList(contractData['contractPaymentList']),
        // 签约完成后刷新底部按钮状态
        onSigningCompleted: () {
          // 签约完成后刷新底部按钮状态并确保键盘不弹起
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              FocusScope.of(context).unfocus();
              _searchFocusNode.unfocus();
            }
          });
          _refreshContractStatus();
        },
      ).then((_) {
        // 对话框关闭后确保键盘不弹起
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            FocusScope.of(context).unfocus();
            _searchFocusNode.unfocus();
          }
        });
      });
    } catch (e) {
      debugPrint('获取合同详情失败: $e');
      _showMessage('获取合同详情失败，请重试');
    }
  }

  // 安全转换 paymentList
  List<Map<String, dynamic>>? _convertPaymentList(dynamic paymentList) {
    if (paymentList == null) return null;

    try {
      if (paymentList is List) {
        return paymentList.map((item) {
          if (item is Map<String, dynamic>) {
            return item;
          } else if (item is Map) {
            // 转换 Map<dynamic, dynamic> 为 Map<String, dynamic>
            return Map<String, dynamic>.from(item);
          } else {
            return <String, dynamic>{};
          }
        }).toList();
      }
      return null;
    } catch (e) {
      debugPrint('转换 paymentList 失败: $e');
      return null;
    }
  }

  // 处理刷新按钮点击
  void _handleRefresh() async {
    if (_stateManager.selectedAddress == null) {
      _showMessage('请先选择项目地址');
      return;
    }

    try {
      // 显示加载状态
      setState(() {
        _stateManager.isLoading = true;
      });

      // 重新获取预算信息和相关数据
      await _loadBudgetInfo(_stateManager.selectedAddress!.serviceId);

      // 清空合同详情缓存，强制重新获取
      _stateManager.firstDataSource = null;
      _stateManager.secondDataSource = null;

      // 触发UI重建
      setState(() {
        _stateManager.isLoading = false;
      });

      _showMessage('刷新成功');
      debugPrint('数据刷新完成');
    } catch (e) {
      setState(() {
        _stateManager.isLoading = false;
      });
      debugPrint('刷新数据失败: $e');
      _showMessage('刷新失败，请重试');
    }
  }

  // 刷新合同状态（签约完成后调用）
  void _refreshContractStatus() async {
    if (_stateManager.selectedAddress == null) return;

    try {
      // 重新获取预算信息以更新合同状态
      await _loadBudgetInfo(_stateManager.selectedAddress!.serviceId);

      // 触发UI重建
      setState(() {});

      debugPrint('合同状态已刷新');
    } catch (e) {
      debugPrint('刷新合同状态失败: $e');
    }
  }

  // 计算优惠后的金额
  double _calculateDiscountedAmount(String contractType) {
    if (_stateManager.budgetInfo == null) return 0.0;

    double originalAmount = 0.0;
    double discountAmount = 0.0;

    if (contractType == '1') {
      // 套餐包
      if (_stateManager.budgetInfo!['packageBudgetPrice'] != null) {
        if (_stateManager.budgetInfo!['packageBudgetPrice'] is num) {
          originalAmount =
              _stateManager.budgetInfo!['packageBudgetPrice'].toDouble();
        } else if (_stateManager.budgetInfo!['packageBudgetPrice'] is String) {
          originalAmount = double.tryParse(
                  _stateManager.budgetInfo!['packageBudgetPrice']) ??
              0.0;
        }
      }

      // 获取套餐包优惠金额
      if (_stateManager.packageDiscountInfo != null &&
          _stateManager.packageDiscountInfo!['discountPrice'] != null) {
        if (_stateManager.packageDiscountInfo!['discountPrice'] is num) {
          discountAmount =
              _stateManager.packageDiscountInfo!['discountPrice'].toDouble();
        } else if (_stateManager.packageDiscountInfo!['discountPrice']
            is String) {
          String discountPriceStr =
              _stateManager.packageDiscountInfo!['discountPrice'];
          if (discountPriceStr.isNotEmpty) {
            discountAmount = double.tryParse(discountPriceStr) ?? 0.0;
          }
        }
      }
    } else if (contractType == '2') {
      // 个性化
      if (_stateManager.budgetInfo!['personalBudgetPrice'] != null) {
        if (_stateManager.budgetInfo!['personalBudgetPrice'] is num) {
          originalAmount =
              _stateManager.budgetInfo!['personalBudgetPrice'].toDouble();
        } else if (_stateManager.budgetInfo!['personalBudgetPrice'] is String) {
          originalAmount = double.tryParse(
                  _stateManager.budgetInfo!['personalBudgetPrice']) ??
              0.0;
        }
      }

      // 获取个性化优惠金额
      if (_stateManager.personalDiscountInfo != null &&
          _stateManager.personalDiscountInfo!['discountPrice'] != null) {
        if (_stateManager.personalDiscountInfo!['discountPrice'] is num) {
          discountAmount =
              _stateManager.personalDiscountInfo!['discountPrice'].toDouble();
        } else if (_stateManager.personalDiscountInfo!['discountPrice']
            is String) {
          String discountPriceStr =
              _stateManager.personalDiscountInfo!['discountPrice'];
          if (discountPriceStr.isNotEmpty) {
            discountAmount = double.tryParse(discountPriceStr) ?? 0.0;
          }
        }
      }
    }

    // 返回优惠后的金额
    return originalAmount - discountAmount;
  }

  //获取E 签宝合同状态
  Future<Map<String, dynamic>?> _getEContractStatus(
      String serviceId, String contractType) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/contract/logic',
        queryParameters: {'serviceId': serviceId, 'contractType': contractType},
      );
      if (response != null) {
        // 设置合同ID到实例变量（保持原有逻辑）
        if (contractType == '1') {
          _e1signContractId = response['id'];
        } else if (contractType == '2') {
          _e2signContractId = response['id'];
        }
        return response;
      }
      return null;
    } catch (e) {
      debugPrint('获取E签宝合同状态失败: $e');
      return null;
    }
  }

  // 处理搜索逻辑
  void _handleSearch(String phone) {
    if (phone.isEmpty) {
      // 如果输入为空，重置到初始状态
      _resetToInitialState();
      _showMessage('请输入手机号');
      return;
    }

    // 执行搜索
    _queryProjectAddressByPhone(phone);
  }

  // 重置到初始状态
  void _resetToInitialState() {
    setState(() {
      _stateManager.clearSearchResults(); // 清空搜索相关状态
    });
  }

  // 根据手机号查询项目地址
  Future<void> _queryProjectAddressByPhone(String phone) async {
    setState(() {
      _stateManager.isLoading = true;
      // 清空之前的搜索结果
      _stateManager.clearSearchResults();
    });

    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/quick/sign/project/info',
        queryParameters: {'phone': phone},
      );

      if (response != null) {
        debugPrint('API返回的response类型: ${response.runtimeType}');
        debugPrint('API返回的response内容: $response');

        if (response is List) {
          final List<dynamic> dataList = response;
          final List<ProjectAddressData> addressList = [];

          for (int i = 0; i < dataList.length; i++) {
            try {
              final item = dataList[i];
              if (item is Map<String, dynamic>) {
                final address = ProjectAddressData.fromJson(item);
                addressList.add(address);
              }
            } catch (e) {
              debugPrint('解析第$i个地址数据失败: $e');
            }
          }

          setState(() {
            _stateManager.projectAddressList = addressList;
            _stateManager.selectedAddress = null; // 重置选中的地址
          });

          if (_stateManager.projectAddressList.isEmpty) {
            _showMessage('未找到该手机号对应的项目地址');
          } else if (_stateManager.projectAddressList.length == 1) {
            // 如果只有一个地址，自动选中并获取预算信息
            setState(() {
              _stateManager.selectedAddress =
                  _stateManager.projectAddressList.first;
            });
            // 自动获取预算信息
            _loadBudgetInfo(_stateManager.projectAddressList.first.serviceId);
          }
        } else {
          _showMessage('返回数据格式错误');
        }
      } else {
        _showMessage('查询失败');
      }
    } catch (e) {
      debugPrint('查询项目地址失败: $e');
      _showMessage('查询失败，请检查网络连接');
    } finally {
      setState(() {
        _stateManager.isLoading = false;
      });
    }
  }

  // 显示地址选择底推框
  void _showAddressSelectionDialog() {
    debugPrint('显示地址选择对话框，地址数量: ${_stateManager.projectAddressList.length}');
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _AddressSelectionBottomSheet(
        addressList: _stateManager.projectAddressList,
        selectedAddress: _stateManager.selectedAddress,
        onAddressSelected: (address) {
          setState(() {
            _stateManager.selectedAddress = address;
          });
          // 选择地址后先收起键盘，再获取预算信息
          _dismissKeyboard();
          _loadBudgetInfo(address.serviceId);
        },
      ),
    ).then((_) {
      // 地址选择对话框关闭后确保键盘不弹起
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          FocusScope.of(context).unfocus();
          _searchFocusNode.unfocus();
        }
      });
    });
  }

  // 加载预算信息
  Future<void> _loadBudgetInfo(String serviceId) async {
    try {
      // 同时获取预算信息、优惠信息和合同状态
      final results = await Future.wait([
        getBudgetInfoByAddress(serviceId),
        _getDiscountInfo(serviceId, '1'), // 套餐包优惠信息
        _getDiscountInfo(serviceId, '2'), // 个性化优惠信息
        _getEContractStatus(serviceId, '1'), // 套餐包合同状态
        _getEContractStatus(serviceId, '2'), // 个性化合同状态
      ]);

      final budgetData = results[0];
      final packageDiscountData = results[1];
      final personalDiscountData = results[2];
      final packageContractData = results[3];
      final personalContractData = results[4];

      setState(() {
        _stateManager.budgetInfo =
            (budgetData is Map<String, dynamic> && budgetData.isEmpty)
                ? null
                : budgetData;
        _stateManager.packageDiscountInfo = packageDiscountData;
        _stateManager.personalDiscountInfo = personalDiscountData;
        _stateManager.packageContractStatus = packageContractData;
        _stateManager.personalContractStatus = personalContractData;
      });

      // 加载完预算信息后确保键盘不弹起
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          FocusScope.of(context).unfocus();
          _searchFocusNode.unfocus();
        }
      });
    } catch (e) {
      debugPrint('获取预算信息失败: $e');
      _showMessage('获取预算信息失败，请重试');
    }
  }

// 显示提示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.grey[700],
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
      ),
    );
  }

  //去签约
  Future<void> _requestDirectSign(
      String eid, String type, String agentName, String agentPhone) async {
    final apiManager = ApiManager();
    try {
      final response = await apiManager.post(
        '/api/signature/direct/sign',
        data: {
          'esignContractId': eid,
          'psnOrOrg': type,
          'agentName': agentName,
          'agentPhone': agentPhone,
        },
      );

      if (response != null) {
        // 显示提示弹窗
        _showSuccessDialog();
        // 刷新合同状态
        _refreshContractStatus();
      }
    } catch (e) {
      // 签约失败处理
    }
  }

  // 显示成功提示弹窗
  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('提示'),
          content: const Text('已将电子合同链接通过短信形式发送至客户手机，请及时提醒客户完成合同签署，链接有效期三小时'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭弹窗
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  // 处理查看合同逻辑
  void _handleViewContract(String contractType, bool alreadySign) async {
    // 点击查看合同按钮时先收起键盘
    _dismissKeyboard();

    if (_stateManager.selectedAddress == null) {
      _showMessage('请先选择项目地址');
      return;
    }

    try {
      // 获取合同详情
      final contractData = await _requestData(
          _stateManager.selectedAddress!.serviceId, contractType);

      if (contractData == null) {
        _showMessage('获取合同详情失败，请重试');
        return;
      }

      String esignContractId = contractType == '1'
          ? _e1signContractId ?? ''
          : _e2signContractId ?? '';

      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 根据签约状态决定查看合同的方式
      if (alreadySign) {
        // 已签约：使用合同图片选择器
        _showContractPicPicker(
            contractData['contractPic'] as List<dynamic>?, esignContractId);
      } else {
        // 未签约但合同已填写：直接跳转到扫描页面
        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => ScanContractPDFWidget(
                    esignContractId: esignContractId,
                    contractUrl: contractData['contractPic']?[0] ?? '',
                    isDirectLoad: alreadySign,
                  )),
        ).then((_) {
          // 从查看合同页面返回后，确保键盘不会自动弹起
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              FocusScope.of(context).unfocus();
              _searchFocusNode.unfocus();
            }
          });
          // 刷新合同状态
          _refreshContractStatus();
        });
      }
    } catch (e) {
      debugPrint('查看合同失败: $e');
      _showMessage('查看合同失败，请重试');
    }
  }

  // 显示合同图片选择器底部弹窗
  void _showContractPicPicker(
      List<dynamic>? contractPics, String esignContractId) {
    if (contractPics == null || contractPics.isEmpty) {
      // 如果没有合同图片，直接显示提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('没有可用的合同文件')),
      );
      return;
    }

    // 创建一个可以保存选中索引的变量
    int selectedIndex = 0;

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 250.h,
          padding: EdgeInsets.all(16.r),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '选择合同文件',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: const Icon(Icons.close),
                  ),
                ],
              ),
              const Divider(),
              Expanded(
                child: CupertinoPicker(
                  magnification: 1.2,
                  squeeze: 1.2,
                  useMagnifier: true,
                  itemExtent: 40,
                  scrollController: FixedExtentScrollController(initialItem: 0),
                  onSelectedItemChanged: (int index) {
                    // 保存选中的索引
                    selectedIndex = index;
                  },
                  children:
                      List<Widget>.generate(contractPics.length, (int index) {
                    final filename = _getFilenameFromUrl(contractPics[index]);
                    return Center(
                      child: Text(
                        '合同文件 ${index + 1}: $filename',
                        style: TextStyle(fontSize: 14.sp),
                      ),
                    );
                  }),
                ),
              ),
              SizedBox(height: 10.h),
              GestureDetector(
                onTap: () {
                  Navigator.pop(context); // 关闭选择器
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ScanContractPDFWidget(
                        esignContractId: esignContractId,
                        contractUrl: contractPics[selectedIndex],
                        isDirectLoad: true, // 直接加载模式
                      ),
                    ),
                  ).then((_) {
                    // 从查看合同页面返回后，确保键盘不会自动弹起
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (mounted) {
                        FocusScope.of(context).unfocus();
                        _searchFocusNode.unfocus();
                      }
                    });
                  });
                },
                child: Container(
                  width: double.infinity,
                  height: 40.h,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Center(
                    child: Text(
                      '确定',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16.sp,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 从URL中提取文件名
  String _getFilenameFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final pathSegments = uri.pathSegments;
      if (pathSegments.isNotEmpty) {
        return pathSegments.last.length > 15
            ? '${pathSegments.last.substring(0, 15)}...'
            : pathSegments.last;
      }
    } catch (e) {
      // 解析URL出错时
    }
    return '未命名文件';
  }
}

// 地址选择底推框组件
class _AddressSelectionBottomSheet extends StatelessWidget {
  final List<ProjectAddressData> addressList;
  final ProjectAddressData? selectedAddress;
  final Function(ProjectAddressData) onAddressSelected;

  const _AddressSelectionBottomSheet({
    required this.addressList,
    required this.selectedAddress,
    required this.onAddressSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部拖拽指示器
            Container(
              width: 36.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 8.h, bottom: 16.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),

            // 标题
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Row(
                children: [
                  Text(
                    '选择项目地址',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.black,
                    ),
                  ),
                  const Spacer(),
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: EdgeInsets.all(8.w),
                      child: Text(
                        '取消',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 16.h),

            // 地址列表
            Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.6,
              ),
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: addressList.length,
                separatorBuilder: (context, index) => Container(
                  height: 1.h,
                  margin: EdgeInsets.symmetric(horizontal: 16.w),
                  color: Colors.grey[100],
                ),
                itemBuilder: (context, index) {
                  final address = addressList[index];
                  final isSelected =
                      selectedAddress?.serviceId == address.serviceId;

                  return GestureDetector(
                    onTap: () {
                      onAddressSelected(address);
                      Navigator.pop(context);
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 16.w, vertical: 16.h),
                      color: Colors.white,
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              address.address,
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: isSelected
                                    ? HexColor('#FFB26D')
                                    : Colors.black,
                                fontWeight: isSelected
                                    ? FontWeight.w600
                                    : FontWeight.normal,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (isSelected)
                            Icon(
                              Icons.check_circle,
                              color: HexColor('#FFB26D'),
                              size: 24.w,
                            ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            SizedBox(height: 16.h),
          ],
        ),
      ),
    );
  }
}

// 显示快速签约底推框的便捷方法
void showQuickSigningBottomSheet(BuildContext context) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => const QuickSigningBottomSheet(),
  );
}

//根据项目地址获取预算信息
Future<Map<String, dynamic>> getBudgetInfoByAddress(String serviceId) async {
  final apiManager = ApiManager();
  final response = await apiManager.get(
    '/api/signature/quick/sign/budget/info',
    queryParameters: {'serviceId': serviceId},
  );

  if (response != null) {
    // response是一个数组，判断是否为空
    if (response is List && response.isNotEmpty) {
      // 如果有数据，取第一个元素返回
      return response[0];
    } else {
      // 如果数组为空，返回空对象表示暂无数据
      return {};
    }
  } else {
    return {};
  }
}

//获取优惠申请信息
Future<Map<String, dynamic>?> _getDiscountInfo(
    String serviceId, String contractType) async {
  try {
    final apiManager = ApiManager();
    final response = await apiManager.get(
      '/api/signature/discount/info',
      queryParameters: {'serviceId': serviceId, 'contractType': contractType},
    );
    if (response != null) {
      return response;
    }
    return null;
  } catch (e) {
    debugPrint('获取优惠信息失败: $e');
    return null;
  }
}

//合同详情
Future<Map<String, dynamic>?> _requestData(
    String serviceId, String contractType) async {
  final apiManager = ApiManager();
  final response = await apiManager.get(
    '/api/signature/naked/contract',
    queryParameters: {'serviceId': serviceId, 'contractType': contractType},
  );

  if (response != null) {
    return response;
  }
  return null;
}
