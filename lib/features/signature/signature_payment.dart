import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/features/signature/payment_success_page.dart';
import 'package:oktoast/oktoast.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'dart:convert';

// 付款方式枚举
enum PaymentMode {
  installment, // 分期付款
  oneTime, // 一次性付款
  custom, // 自定义金额
}

class SignaturePaymentWidget extends StatefulWidget {
  final Map<String, dynamic> contractData;
  final String? customerName; // 客户姓名
  final String? customerAddress; // 客户地址
  const SignaturePaymentWidget({
    super.key,
    required this.contractData,
    this.customerName,
    this.customerAddress,
  });

  @override
  State<SignaturePaymentWidget> createState() => _SignaturePaymentWidgetState();
}

class _SignaturePaymentWidgetState extends State<SignaturePaymentWidget> {
  // 状态变量
  int selectedAccountIndex = 0; // 默认选中第一个账户
  int selectedPaymentMethodIndex = 0; // 默认选中第一个付款方式
  Set<int> selectedPaymentPeriods = {}; // 选中的分期期数（使用Set存储多选）

  // 付款方式相关状态
  PaymentMode currentPaymentMode = PaymentMode.installment; // 当前付款方式
  double customAmount = 0.0; // 自定义金额
  final TextEditingController _customAmountController = TextEditingController();

  // 分期付款数据
  List<Map<String, dynamic>> _paymentPeriods = [];

  // 公司账户数据
  List<Map<String, dynamic>> _companyAccounts = [];

  // POS机相关数据
  List<Map<String, dynamic>> _posMachineList = [];
  Map<String, dynamic>? _selectedPosMachine;

  // 二维码相关数据
  String? _qrCodeData;

  // WebSocket 相关变量
  WebSocketChannel? _webSocketChannel;
  String? _wsUrl;
  bool _isWsConnected = false;
  String? _currentTimestamp;

  @override
  void initState() {
    super.initState();
    _getPaymentPeriods();
    _getCompanyAccounts();
    _getPosMachineList();
  }

  @override
  void dispose() {
    _disconnectWebSocket();
    _customAmountController.dispose();
    super.dispose();
  }

  // 构建分期付款项目
  Widget _buildPaymentPeriodItem(
      String period, String remaining, String amount, int index) {
    final periodData = _paymentPeriods[index];
    final price = double.tryParse(periodData['price']?.toString() ?? '0') ?? 0;

    // 判断是否为0元期数（自动选中且不可点击）
    bool isZeroPeriod = price == 0;
    bool isSelected = selectedPaymentPeriods.contains(index) || isZeroPeriod;
    bool isClickable = !isZeroPeriod;

    return GestureDetector(
      onTap: isClickable
          ? () {
              setState(() {
                if (selectedPaymentPeriods.contains(index)) {
                  // 如果当前期数已选中，取消选择（同时取消后面的期数）
                  selectedPaymentPeriods
                      .removeWhere((periodIndex) => periodIndex >= index);
                } else {
                  // 如果当前期数未选中，选择当前期数（同时自动选择前面的期数）
                  for (int i = 0; i <= index; i++) {
                    selectedPaymentPeriods.add(i);
                  }
                }
              });
            }
          : null,
      child: Row(
        children: [
          // 选择圆圈
          Container(
            width: 20.w,
            height: 20.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected
                    ? (isZeroPeriod
                        ? const Color(0xFF999999)
                        : const Color(0xFF333333))
                    : const Color(0xFFDDDDDD),
                width: 2,
              ),
              color: isSelected
                  ? (isZeroPeriod
                      ? const Color(0xFF999999)
                      : const Color(0xFF333333))
                  : Colors.transparent,
            ),
            child: isSelected
                ? Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 12.sp,
                  )
                : null,
          ),
          SizedBox(width: 12.w),
          // 期数和剩余金额
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  period,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: isZeroPeriod
                        ? const Color(0xFF999999)
                        : const Color(0xFF333333),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  '剩余：$remaining',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: const Color(0xFF999999),
                  ),
                ),
              ],
            ),
          ),
          // 金额
          Text(
            amount,
            style: TextStyle(
              fontSize: 16.sp,
              color: isZeroPeriod
                  ? const Color(0xFF999999)
                  : const Color(0xFF333333),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // 构建分期付款列表
  List<Widget> _buildPaymentPeriodsList() {
    List<Widget> widgets = [];
    for (int i = 0; i < _paymentPeriods.length; i++) {
      final period = _paymentPeriods[i];
      final type = period['type']?.toString() ?? '${i + 1}';
      final price = period['price']?.toString() ?? '0';
      final unpaidAmount = period['unpaidAmount']?.toString() ?? '0';

      // 格式化金额显示
      final formattedPrice = '¥${_formatAmount(price)}';
      final formattedUnpaid = _formatAmount(unpaidAmount);

      widgets.add(_buildPaymentPeriodItem(
          '第$type期', formattedUnpaid, formattedPrice, i));

      // 添加间距，除了最后一个
      if (i < _paymentPeriods.length - 1) {
        widgets.add(SizedBox(height: 16.h));
      }
    }
    return widgets;
  }

  // 构建付款内容区域
  Widget _buildPaymentContent() {
    switch (currentPaymentMode) {
      case PaymentMode.installment:
        // 分期付款模式 - 显示分期列表
        return Column(
          children: [
            SizedBox(height: 20.h),
            ..._buildPaymentPeriodsList(),
          ],
        );

      case PaymentMode.oneTime:
        // 一次性付款模式 - 显示一行文本
        return Column(
          children: [
            SizedBox(height: 20.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '一次性付款：',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF333333),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '¥${_formatAmount(_getTotalUnpaidAmount().toString())}',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF333333),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        );

      case PaymentMode.custom:
        // 自定义金额模式 - 显示自定义金额
        return Column(
          children: [
            SizedBox(height: 20.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '自定义金额：',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF333333),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                GestureDetector(
                  onTap: () => _showCustomAmountDialog(),
                  child: Row(
                    children: [
                      Text(
                        '¥${_formatAmount(customAmount.toString())}',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: const Color(0xFF333333),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Icon(
                        Icons.edit,
                        size: 16.sp,
                        color: const Color(0xFF4A90E2),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        );
    }
  }

  // 格式化金额显示
  String _formatAmount(String amount) {
    try {
      final double value = double.parse(amount);
      if (value == 0) return '0';
      // 格式化为千分位显示
      return value.toStringAsFixed(2).replaceAllMapped(
            RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
            (Match m) => '${m[1]},',
          );
    } catch (e) {
      return amount;
    }
  }

  // 计算所有分期的剩余金额总和
  double _getTotalUnpaidAmount() {
    double total = 0;
    for (final period in _paymentPeriods) {
      final unpaidAmount =
          double.tryParse(period['unpaidAmount']?.toString() ?? '0') ?? 0;
      total += unpaidAmount;
    }
    return total;
  }

  // 计算合同金额总额（从分期数据中累加）
  double _getTotalContractAmount() {
    double total = 0;
    for (final period in _paymentPeriods) {
      final price = double.tryParse(period['price']?.toString() ?? '0') ?? 0;
      total += price;
    }
    return total;
  }

  // 计算选中期数的总金额
  double _getSelectedPeriodsAmount() {
    double total = 0;
    for (int index in selectedPaymentPeriods) {
      if (index < _paymentPeriods.length) {
        final price = double.tryParse(
                _paymentPeriods[index]['price']?.toString() ?? '0') ??
            0;
        total += price;
      }
    }
    return total;
  }

  // 获取当前应付金额
  double _getCurrentPaymentAmount() {
    switch (currentPaymentMode) {
      case PaymentMode.installment:
        return _getSelectedPeriodsAmount();
      case PaymentMode.oneTime:
        return _getTotalUnpaidAmount();
      case PaymentMode.custom:
        return customAmount;
    }
  }

  // 获取付款方案值
  String _getPaymentPlanValue() {
    switch (currentPaymentMode) {
      case PaymentMode.installment:
        return 'stages'; // 分期付款
      case PaymentMode.oneTime:
        return 'all'; // 一次性付款
      case PaymentMode.custom:
        return 'customize'; // 自定义金额
    }
  }

  // 获取选中的分期付款列表
  List<Map<String, dynamic>> _getSelectedPaymentList() {
    if (currentPaymentMode == PaymentMode.installment &&
        selectedPaymentPeriods.isNotEmpty) {
      // 分期付款模式：返回选中期数对应的Map数据
      List<Map<String, dynamic>> selectedList = [];
      // 将选中的期数索引转换为有序列表并排序
      List<int> sortedIndexes = selectedPaymentPeriods.toList()..sort();

      for (int index in sortedIndexes) {
        if (index < _paymentPeriods.length) {
          selectedList.add(_paymentPeriods[index]);
        }
      }
      return selectedList;
    } else {
      // 一次性付款或自定义金额模式：返回空列表或所有期数
      return [];
    }
  }

  // 初始化时自动选中价格为0的期数
  void _initializeZeroPeriods() {
    selectedPaymentPeriods.clear();
    for (int i = 0; i < _paymentPeriods.length; i++) {
      final price =
          double.tryParse(_paymentPeriods[i]['price']?.toString() ?? '0') ?? 0;
      if (price == 0) {
        selectedPaymentPeriods.add(i);
      }
    }
  }

  // 获取付款方式显示文本
  String _getPaymentModeText() {
    switch (currentPaymentMode) {
      case PaymentMode.installment:
        return '分期付款-${_paymentPeriods.length}期';
      case PaymentMode.oneTime:
        return '一次性付款';
      case PaymentMode.custom:
        return '自定义金额';
    }
  }

  // 显示付款方式选择浮窗
  void _showPaymentModeSelector(BuildContext context, Offset position) {
    showMenu<PaymentMode>(
      context: context,
      position: RelativeRect.fromLTRB(
        position.dx,
        position.dy + 30.h, // 在点击位置下方显示
        position.dx + 200.w,
        position.dy + 200.h,
      ),
      items: [
        PopupMenuItem<PaymentMode>(
          value: PaymentMode.installment,
          child: Text('分期付款-${_paymentPeriods.length}期'),
        ),
        const PopupMenuItem<PaymentMode>(
          value: PaymentMode.oneTime,
          child: Text('一次性付款'),
        ),
        const PopupMenuItem<PaymentMode>(
          value: PaymentMode.custom,
          child: Text('自定义金额'),
        ),
      ],
    ).then((PaymentMode? selectedMode) {
      if (selectedMode != null && selectedMode != currentPaymentMode) {
        _handlePaymentModeChange(selectedMode);
      }
    });
  }

  // 处理付款方式变更
  void _handlePaymentModeChange(PaymentMode newMode) {
    setState(() {
      currentPaymentMode = newMode;

      if (newMode == PaymentMode.custom) {
        // 如果选择自定义金额，显示输入对话框
        _showCustomAmountDialog();
      } else if (newMode == PaymentMode.installment) {
        // 重新初始化分期选择
        _initializeZeroPeriods();
      } else if (newMode == PaymentMode.oneTime) {
        // 清空分期选择
        selectedPaymentPeriods.clear();
      }
    });
  }

  // 显示POS机选择弹窗 - 两列布局
  void _showPosMachineSelector() {
    if (_posMachineList.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('暂无可用的POS机')),
      );
      return;
    }

    int selectedDeptIndex = 0;
    int selectedPosIndex = 0;

    // 如果已经有选中的POS机，找到对应的索引
    if (_selectedPosMachine != null) {
      for (int deptIdx = 0; deptIdx < _posMachineList.length; deptIdx++) {
        final posList =
            _posMachineList[deptIdx]['posTerminalList'] as List<dynamic>? ?? [];
        for (int posIdx = 0; posIdx < posList.length; posIdx++) {
          if (posList[posIdx]['id'] == _selectedPosMachine!['id']) {
            selectedDeptIndex = deptIdx;
            selectedPosIndex = posIdx;
            break;
          }
        }
      }
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            final currentPosList = _posMachineList.isNotEmpty &&
                    selectedDeptIndex < _posMachineList.length
                ? _posMachineList[selectedDeptIndex]['posTerminalList']
                        as List<dynamic>? ??
                    []
                : <dynamic>[];

            // 确保选中的POS索引不超出范围
            if (selectedPosIndex >= currentPosList.length) {
              selectedPosIndex = currentPosList.isNotEmpty ? 0 : -1;
            }

            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Container(
                width: MediaQuery.of(context).size.width * 0.9,
                height: MediaQuery.of(context).size.height * 0.6,
                padding: EdgeInsets.all(20.w),
                child: Column(
                  children: [
                    // 标题
                    Text(
                      '选择POS机',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF333333),
                      ),
                    ),
                    SizedBox(height: 20.h),
                    // 两列布局
                    Expanded(
                      child: Row(
                        children: [
                          // 左列：部门列表
                          Expanded(
                            flex: 1,
                            child: Container(
                              decoration: BoxDecoration(
                                border:
                                    Border.all(color: const Color(0xFFE0E0E0)),
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: Column(
                                children: [
                                  // 左列标题
                                  Container(
                                    width: double.infinity,
                                    padding:
                                        EdgeInsets.symmetric(vertical: 12.h),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFF8F8F8),
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(8.r),
                                        topRight: Radius.circular(8.r),
                                      ),
                                    ),
                                    child: Text(
                                      '部门',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xFF666666),
                                      ),
                                    ),
                                  ),
                                  // 部门列表
                                  Expanded(
                                    child: ListView.builder(
                                      itemCount: _posMachineList.length,
                                      itemBuilder: (context, index) {
                                        final deptData = _posMachineList[index];
                                        final deptName =
                                            deptData['deptName'] ?? '';
                                        final isSelected =
                                            selectedDeptIndex == index;

                                        return GestureDetector(
                                          onTap: () {
                                            setDialogState(() {
                                              selectedDeptIndex = index;
                                              selectedPosIndex = 0; // 重置POS选择
                                            });
                                          },
                                          child: Container(
                                            padding: EdgeInsets.symmetric(
                                                vertical: 16.h,
                                                horizontal: 12.w),
                                            decoration: BoxDecoration(
                                              color: isSelected
                                                  ? const Color(0xFFE3F2FD)
                                                  : Colors.transparent,
                                              border: const Border(
                                                bottom: BorderSide(
                                                  color: Color(0xFFE0E0E0),
                                                  width: 0.5,
                                                ),
                                              ),
                                            ),
                                            child: Text(
                                              deptName,
                                              style: TextStyle(
                                                fontSize: 13.sp,
                                                color: isSelected
                                                    ? const Color(0xFF1976D2)
                                                    : const Color(0xFF333333),
                                                fontWeight: isSelected
                                                    ? FontWeight.w600
                                                    : FontWeight.normal,
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(width: 1.w),
                          // 右列：POS机列表
                          Expanded(
                            flex: 1,
                            child: Container(
                              decoration: BoxDecoration(
                                border:
                                    Border.all(color: const Color(0xFFE0E0E0)),
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: Column(
                                children: [
                                  // 右列标题
                                  Container(
                                    width: double.infinity,
                                    padding:
                                        EdgeInsets.symmetric(vertical: 12.h),
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFF8F8F8),
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(8.r),
                                        topRight: Radius.circular(8.r),
                                      ),
                                    ),
                                    child: Text(
                                      'POS机',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        fontSize: 14.sp,
                                        fontWeight: FontWeight.w600,
                                        color: const Color(0xFF666666),
                                      ),
                                    ),
                                  ),
                                  // POS机列表
                                  Expanded(
                                    child: currentPosList.isEmpty
                                        ? Center(
                                            child: Text(
                                              '暂无POS机',
                                              style: TextStyle(
                                                fontSize: 13.sp,
                                                color: const Color(0xFF999999),
                                              ),
                                            ),
                                          )
                                        : ListView.builder(
                                            itemCount: currentPosList.length,
                                            itemBuilder: (context, index) {
                                              final pos = currentPosList[index];
                                              final terminalName =
                                                  pos['terminalName'] ?? '';
                                              final terminalCode =
                                                  pos['terminalCode'] ?? '';
                                              final isSelected =
                                                  selectedPosIndex == index;

                                              return GestureDetector(
                                                onTap: () {
                                                  setDialogState(() {
                                                    selectedPosIndex = index;
                                                  });
                                                },
                                                child: Container(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical: 16.h,
                                                      horizontal: 12.w),
                                                  decoration: BoxDecoration(
                                                    color: isSelected
                                                        ? const Color(
                                                            0xFFE3F2FD)
                                                        : Colors.transparent,
                                                    border: const Border(
                                                      bottom: BorderSide(
                                                        color:
                                                            Color(0xFFE0E0E0),
                                                        width: 0.5,
                                                      ),
                                                    ),
                                                  ),
                                                  child: Column(
                                                    children: [
                                                      Text(
                                                        terminalName,
                                                        style: TextStyle(
                                                          fontSize: 13.sp,
                                                          color: isSelected
                                                              ? const Color(
                                                                  0xFF1976D2)
                                                              : const Color(
                                                                  0xFF333333),
                                                          fontWeight: isSelected
                                                              ? FontWeight.w600
                                                              : FontWeight
                                                                  .normal,
                                                        ),
                                                        textAlign:
                                                            TextAlign.center,
                                                      ),
                                                      SizedBox(height: 2.h),
                                                      Text(
                                                        terminalCode,
                                                        style: TextStyle(
                                                          fontSize: 11.sp,
                                                          color: const Color(
                                                              0xFF999999),
                                                        ),
                                                        textAlign:
                                                            TextAlign.center,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 20.h),
                    // 底部按钮
                    Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            style: TextButton.styleFrom(
                              backgroundColor: const Color(0xFFF5F5F5),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              padding: EdgeInsets.symmetric(vertical: 12.h),
                            ),
                            child: Text(
                              '取消',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: const Color(0xFF666666),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: currentPosList.isNotEmpty &&
                                    selectedPosIndex >= 0
                                ? () {
                                    setState(() {
                                      _selectedPosMachine =
                                          currentPosList[selectedPosIndex];
                                    });
                                    Navigator.of(context).pop();
                                  }
                                : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF1976D2),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              padding: EdgeInsets.symmetric(vertical: 12.h),
                            ),
                            child: Text(
                              '确定',
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  // 显示自定义金额输入对话框
  void _showCustomAmountDialog() {
    _customAmountController.clear();

    showDialog<double>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('输入自定义金额'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(height: 16.h),
              TextField(
                controller: _customAmountController,
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                decoration: const InputDecoration(
                  labelText: '金额',
                  prefixText: '¥ ',
                  border: OutlineInputBorder(),
                ),
                autofocus: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // 如果取消，恢复到分期付款模式
                setState(() {
                  currentPaymentMode = PaymentMode.installment;
                  _initializeZeroPeriods();
                });
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                final inputText = _customAmountController.text.trim();
                final amount = double.tryParse(inputText);

                if (amount == null || amount <= 0) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('请输入有效金额（金额必须大于0）')),
                  );
                  return;
                }

                setState(() {
                  customAmount = amount;
                  selectedPaymentPeriods.clear();
                });
                Navigator.of(context).pop();
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  // 构建账户选择项目
  Widget _buildAccountItem(String accountName, int index) {
    bool isSelected = selectedAccountIndex == index;
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedAccountIndex = index;
        });
      },
      child: Row(
        children: [
          Expanded(
            child: Text(
              accountName,
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF333333),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          // 选择圆圈
          Container(
            width: 20.w,
            height: 20.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected
                    ? const Color(0xFF333333)
                    : const Color(0xFFDDDDDD),
                width: 2,
              ),
              color: isSelected ? const Color(0xFF333333) : Colors.transparent,
            ),
            child: isSelected
                ? Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 12.sp,
                  )
                : null,
          ),
        ],
      ),
    );
  }

  // 构建付款方式选择项目
  Widget _buildPaymentMethodItem(String methodName, int index) {
    bool isSelected = selectedPaymentMethodIndex == index;
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedPaymentMethodIndex = index;
        });

        // 如果选择的是POS机收款，显示POS机选择弹窗
        if (index == 1) {
          _showPosMachineSelector();
        }
      },
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  methodName,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF333333),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              // 选择圆圈
              Container(
                width: 20.w,
                height: 20.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected
                        ? const Color(0xFF333333)
                        : const Color(0xFFDDDDDD),
                    width: 2,
                  ),
                  color:
                      isSelected ? const Color(0xFF333333) : Colors.transparent,
                ),
                child: isSelected
                    ? Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 12.sp,
                      )
                    : null,
              ),
            ],
          ),
          // 显示选中的POS机信息（只对POS机收款显示）
          if (index == 1 && isSelected && _selectedPosMachine != null) ...[
            SizedBox(height: 8.h),
            Row(
              children: [
                Expanded(
                  child: Text(
                    '${_selectedPosMachine!['terminalName']} (${_selectedPosMachine!['terminalCode']})',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xFF999999),
                    ),
                  ),
                ),
                SizedBox(width: 20.w), // 与选择圆圈对齐的空间
              ],
            ),
          ],
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('付款方式'),
        backgroundColor: Colors.white,
        foregroundColor: const Color.fromRGBO(0, 0, 0, 1),
        elevation: 0,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 付款确认信息卡片
            Container(
              margin: EdgeInsets.all(16.w),
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // 成功图标
                  Container(
                    width: 48.w,
                    height: 48.w,
                    decoration: const BoxDecoration(
                      color: Color(0xFF4CAF50),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 28.sp,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  // 提示文字
                  Text(
                    '您的合同已签约成功，请确认付款信息',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: const Color(0xFF333333),
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 20.h),
                  // 合同金额
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '合同金额：',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFF666666),
                        ),
                      ),
                      Text(
                        '¥ ${_formatAmount(_getTotalContractAmount().toString())}',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: const Color(0xFF333333),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12.h),
                  // 剩余金额
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '剩余金额：',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFF666666),
                        ),
                      ),
                      Text(
                        '¥ ${_formatAmount(_getTotalUnpaidAmount().toString())}',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: const Color(0xFF333333),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // 付款方案选择卡片
            Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题行
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '付款方案',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: const Color(0xFF333333),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      GestureDetector(
                        onTapDown: (TapDownDetails details) {
                          _showPaymentModeSelector(
                              context, details.globalPosition);
                        },
                        child: Row(
                          children: [
                            Text(
                              _getPaymentModeText(),
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: const Color(0xFF4A90E2),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            SizedBox(width: 4.w),
                            Icon(
                              Icons.keyboard_arrow_down,
                              color: const Color(0xFF4A90E2),
                              size: 20.sp,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  // 付款内容区域
                  _buildPaymentContent(),
                ],
              ),
            ),
            SizedBox(height: 16.h),
            // 收款公司和账户选择卡片
            Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 收款公司
                  Text(
                    '收款公司',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: const Color(0xFF333333),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 12.h),
                  Text(
                    widget.contractData['incomeCompanyName'] ?? '',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 24.h),
                  // 收款账户
                  Text(
                    '收款账户',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: const Color(0xFF333333),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  // 账户选择列表
                  ..._companyAccounts.asMap().entries.map((entry) {
                    int index = entry.key;
                    Map<String, dynamic> account = entry.value;
                    String accountName = account['dictLabel'] ?? '未知账户';

                    return Column(
                      children: [
                        _buildAccountItem(accountName, index),
                        if (index < _companyAccounts.length - 1)
                          SizedBox(height: 12.h),
                      ],
                    );
                  }).toList(),
                ],
              ),
            ),
            SizedBox(height: 16.h),
            // 付款方式选择卡片
            Container(
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 付款方式标题
                  Text(
                    '付款方式',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: const Color(0xFF333333),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  // 付款方式选择列表
                  _buildPaymentMethodItem('二维码收款', 0),
                  SizedBox(height: 12.h),
                  _buildPaymentMethodItem('POS机收款', 1),
                ],
              ),
            ),
            SizedBox(
              height: 12.h,
            )
          ],
        ),
      ),
      bottomNavigationBar: Container(
          color: Colors.white,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          height: MediaQuery.of(context).padding.bottom + 60.h,
          child: SafeArea(
            child: Row(
              // 左边是 ¥ 符号和金额,右边是立即支付按钮
              children: [
                // 左边价格区域
                Expanded(
                  child: Row(
                    children: [
                      Text(
                        '¥',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: const Color(0xFFFF6B35),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _formatAmount(_getCurrentPaymentAmount().toString()),
                        style: TextStyle(
                          fontSize: 20.sp,
                          color: const Color(0xFFFF6B35),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                // 右边支付按钮
                SizedBox(
                  width: 120.w,
                  height: 44.h,
                  child: ElevatedButton(
                    onPressed: _getCurrentPaymentAmount() > 0
                        ? () {
                            //获取时间戳
                            final timestamp =
                                DateTime.now().millisecondsSinceEpoch;
                            _submitPayment(timestamp.toString());
                          }
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _getCurrentPaymentAmount() > 0
                          ? Colors.black
                          : const Color(0xFFCCCCCC),
                      foregroundColor: _getCurrentPaymentAmount() > 0
                          ? Colors.white
                          : const Color(0xFF999999),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24.r),
                      ),
                      elevation: 0,
                      disabledBackgroundColor: const Color(0xFFCCCCCC),
                      disabledForegroundColor: const Color(0xFF999999),
                    ),
                    child: Text(
                      '立即支付',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          )),
    );
  }

  // 刷新页面数据
  Future<void> _refreshPageData() async {
    // 重置状态
    setState(() {
      selectedPaymentPeriods.clear();
      currentPaymentMode = PaymentMode.installment;
      customAmount = 0.0;
    });

    // 重新获取数据
    await _getPaymentPeriods();
  }

  //获取分期信息
  Future<void> _getPaymentPeriods() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/receive/installment/info',
        queryParameters: {
          'contractId': widget.contractData['contractId'] ?? ''
        },
      );
      if (response != null && mounted) {
        setState(() {
          _paymentPeriods = List<Map<String, dynamic>>.from(response);
          // 初始化时自动选中价格为0的期数
          _initializeZeroPeriods();
        });
      }
    } catch (e) {
      showToast('获取分期信息失败');
    }
  }

  //根据查询公司 ID 查询公司账户
  Future<void> _getCompanyAccounts() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/receive/account/list',
        queryParameters: {'companyId': widget.contractData['incomeCompany']},
      );
      if (response != null && mounted) {
        setState(() {
          _companyAccounts = List<Map<String, dynamic>>.from(response);
        });
      }
    } catch (e) {
      showToast('获取公司账户失败');
    }
  }

  //获取 Pos 机列表
  Future<void> _getPosMachineList() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/pos/list',
        queryParameters: {
          'incomeCompany': widget.contractData['incomeCompany']
        },
      );
      if (response != null && mounted) {
        setState(() {
          // 根据API返回的数据结构解析
          if (response != null && response['unionPayPosTerminalList'] != null) {
            _posMachineList = List<Map<String, dynamic>>.from(
                response['unionPayPosTerminalList']);
          } else {
            _posMachineList = [];
          }
        });
      }
    } catch (e) {
      showToast('获取Pos 机失败');
    }
  }

  //提交支付信息
  Future<void> _submitPayment(String timestamp) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.post(
        '/api/signature/submit/payment/info',
        data: {
          'userId': timestamp, //唯一标识
          'projectId': widget.contractData['customerProjectId'], //项目 ID
          'serviceId': widget.contractData['customerProjectServiceId'], //服务 ID
          'contractId': widget.contractData['contractId'] ?? '', // 合同ID
          'incomeType': widget.contractData['contractlype'] == '1'
              ? '14'
              : '10', //一期（套餐）：14  二期（个性化）：10
          'incomeAmount':
              _getCurrentPaymentAmount().toStringAsFixed(2), // UI左下角的金额，保留两位小数
          'collectionMethod':
              selectedPaymentMethodIndex == 1 ? '11' : '9', // POS机收款传11，二维码收款传9
          'collectionCompany': widget.contractData['incomeCompany'], //收款公司ID
          'collectionAccount': _companyAccounts[selectedAccountIndex]
              ['dictValue'], //收款账户ID

          'paymentPlan':
              _getPaymentPlanValue(), //付款方案（all：一次性付款，customize：自定义金额，stages：分期）
          'paymentList': _getSelectedPaymentList(),
          'terminalId': 'ce5c98bea83e4d3289f3fc5f25c445a6',
          'payment': {
            'incomeAmount': _getCurrentPaymentAmount().toStringAsFixed(2)
          }
        },
      );
      if (response != null) {
        // 初始化 WebSocket 连接监听支付状态
        _initWebSocket(timestamp);

        // 如果是二维码收款，获取二维码
        if (selectedPaymentMethodIndex == 0) {
          _getQrCode(timestamp);
        } else {
          // POS机收款，调用POS机支付接口
          _submitPosPayment(timestamp);
        }
      } else {
        showToast('提交支付信息失败');
      }
    } catch (e) {
      showToast('提交支付信息失败');
    }
  }

  //获取二维码
  Future<void> _getQrCode(String timestamp) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.post(
        '/api/signature/qr/code/payment',
        data: {
          'userId': timestamp, //唯一标识
          'incomeAmount': _getCurrentPaymentAmount().toStringAsFixed(2),
          'companyId': widget.contractData['incomeCompany']
        },
      );
      if (response != null) {
        setState(() {
          _qrCodeData = response['qrcode'];
        });
        // 显示二维码弹窗
        _showQrCodeDialog();
      } else {
        showToast('获取二维码失败');
      }
    } catch (e) {
      showToast('获取二维码失败');
    }
  }

  //提交 Pos机收款
  Future<void> _submitPosPayment(String timestamp) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.post(
        '/api/signature/pos/payment',
        data: {
          'userId': timestamp, //唯一标识
          'incomeAmount': _getCurrentPaymentAmount().toStringAsFixed(2),
          'companyId': widget.contractData['incomeCompany'],
          'terminalCode': _selectedPosMachine?['terminalCode'] ?? '', //pos 机编号
          'customerAddress': widget.customerAddress ?? '', //客户地址
          'customerName': widget.customerName ?? '', //客户姓名
        },
      );
      if (response != null) {
        showToast('POS机支付成功');
        if (mounted) {
          showToast('订单已提交,请求 POS 机支付');
        }
      } else {
        showToast('POS机支付失败');
      }
    } catch (e) {
      showToast('POS机支付失败');
    }
  }

  // 显示二维码弹窗
  void _showQrCodeDialog() {
    if (_qrCodeData == null || _qrCodeData!.isEmpty) {
      showToast('二维码数据为空');
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Container(
            padding: EdgeInsets.all(24.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题
                Text(
                  '扫码支付',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF333333),
                  ),
                ),
                SizedBox(height: 20.h),
                // 二维码
                Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(
                      color: const Color(0xFFE0E0E0),
                      width: 1,
                    ),
                  ),
                  child: QrImageView(
                    data: _qrCodeData!,
                    version: QrVersions.auto,
                    size: 200.w,
                    backgroundColor: Colors.white,
                  ),
                ),
                SizedBox(height: 20.h),
                // 提示文字
                Text(
                  '请使用支付宝或微信扫码支付',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF666666),
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 8.h),
                Text(
                  '¥${_formatAmount(_getCurrentPaymentAmount().toString())}',
                  style: TextStyle(
                    fontSize: 20.sp,
                    color: const Color(0xFFFF6B35),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 24.h),
                // 关闭按钮
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1976D2),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                    ),
                    child: Text(
                      '关闭',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 初始化 WebSocket 连接
  void _initWebSocket(String timestamp) {
    _currentTimestamp = timestamp;
    _generateWebSocketUrl(timestamp);
    _connectWebSocket();
  }

  /// 生成 WebSocket URL
  void _generateWebSocketUrl(String timestamp) {
    if (kDebugMode) {
      // Debug 环境
      _wsUrl = 'ws://erf.gazo.net.cn:8087/test-api/message/ws/$timestamp';
    } else {
      // Release 环境
      _wsUrl = 'wss://erf.zglife.com.cn/prod-api/message/ws/$timestamp';
    }

    debugPrint('WebSocket URL: $_wsUrl');
  }

  /// 连接 WebSocket
  void _connectWebSocket() {
    if (_wsUrl == null || !mounted) return;

    try {
      _webSocketChannel = WebSocketChannel.connect(Uri.parse(_wsUrl!));
      _isWsConnected = true;

      debugPrint('WebSocket 连接成功');

      // 监听 WebSocket 消息
      _webSocketChannel!.stream.listen(
        (message) {
          if (mounted) {
            debugPrint('WebSocket 收到消息: $message');
            _handleWebSocketMessage(message);
          }
        },
        onError: (error) {
          debugPrint('WebSocket 错误: $error');
          if (mounted) {
            _isWsConnected = false;
            _reconnectWebSocket();
          }
        },
        onDone: () {
          debugPrint('WebSocket 连接关闭');
          if (mounted) {
            _isWsConnected = false;
            _reconnectWebSocket();
          }
        },
        cancelOnError: false, // 不要在错误时取消订阅
      );
    } catch (e) {
      debugPrint('WebSocket 连接失败: $e');
      if (mounted) {
        _isWsConnected = false;
        _reconnectWebSocket();
      }
    }
  }

  /// 处理 WebSocket 消息
  void _handleWebSocketMessage(dynamic message) {
    try {
      debugPrint('处理 WebSocket 消息: $message');

      // 尝试解析 JSON 消息
      Map<String, dynamic>? messageData;
      try {
        messageData = jsonDecode(message.toString());
      } catch (e) {
        debugPrint('消息不是有效的 JSON 格式: $e');
        return;
      }

      // 检查是否是支付状态消息
      if (messageData != null && messageData['key'] == 'payStatus') {
        debugPrint('收到支付状态消息，跳转到支付成功页面');

        // 先断开 WebSocket 连接
        _disconnectWebSocket();

        // 跳转到支付成功页面
        if (mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => PaymentSuccessPage(
                onCompleted: () {
                  // 刷新当前页面数据
                  _refreshPageData();
                },
              ),
            ),
          );
        }
        return;
      }
    } catch (e) {
      debugPrint('处理 WebSocket 消息时出错: $e');
    }
  }

  /// 重连 WebSocket
  void _reconnectWebSocket() {
    if (!mounted) return;

    Future.delayed(const Duration(seconds: 5), () {
      if (mounted && !_isWsConnected && _currentTimestamp != null) {
        debugPrint('WebSocket 尝试重连...');
        _connectWebSocket();
      }
    });
  }

  /// 断开 WebSocket 连接
  void _disconnectWebSocket() {
    if (_webSocketChannel != null) {
      try {
        // 使用标准的正常关闭状态码 1000，而不是 1001
        _webSocketChannel!.sink.close(1000, 'Normal closure');
      } catch (e) {
        debugPrint('关闭 WebSocket 时出错: $e');
        // 如果正常关闭失败，尝试强制关闭
        try {
          _webSocketChannel!.sink.close();
        } catch (e2) {
          debugPrint('强制关闭 WebSocket 时出错: $e2');
        }
      } finally {
        _webSocketChannel = null;
        _isWsConnected = false;
        debugPrint('WebSocket 连接已断开');
      }
    }
  }
}
