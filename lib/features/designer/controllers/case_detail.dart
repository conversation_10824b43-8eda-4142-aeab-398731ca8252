import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/shared/dialogs/appointment-dialog.dart'; // 确认弹窗路径
import 'package:flutter_smarthome/core/network/api_manager.dart'; // 确认ApiManager路径
import 'package:flutter_smarthome/core/utils/hex_color.dart'; // 确认HexColor路径
import 'package:flutter_smarthome/core/utils/network_image_helper.dart'; // 确认NetworkImageHelper路径
import 'package:flutter_smarthome/core/utils/string_utils.dart'; // 确认StringUtils路径
import 'package:webview_flutter/webview_flutter.dart'; // 导入WebView包
import 'package:oktoast/oktoast.dart'; // 导入oktoast
import 'package:flutter_smarthome/core/models/models.dart';

class CaseDetailWidget extends StatefulWidget {
  final String title;
  final String caseId;
  const CaseDetailWidget({
    super.key,
    required this.title,
    required this.caseId,
  });

  @override
  State<CaseDetailWidget> createState() => _CaseDetailWidgetState();
}

class _CaseDetailWidgetState extends State<CaseDetailWidget> {
  // 案例详情数据，设为可空，初始为null表示未加载
  CaseDetail? _caseDetailModel; // 案例详情 Model
  final ScrollController _scrollController = ScrollController();
  double _opacity = 0.0; // AppBar透明度
  bool _isLoading = true; // 初始加载状态
  double _webViewHeight = 100.h; // WebView初始高度
  bool _isWebViewReady = false; // WebView是否已准备好

  // --- WebView 相关状态 ---
  late final WebViewController _webViewController;
  final Completer<void> _pageLoadedCompleter = Completer<void>();
  static const String _heightChannelName = 'PageHeight'; // JS通道名称

  @override
  void initState() {
    super.initState();

    // 添加保险定时器：3秒后如果还在加载状态，强制停止加载
    Timer(const Duration(seconds: 3), () {
      if (mounted && _isLoading) {
        debugPrint("WebView加载超时，强制停止加载状态");
        setState(() {
          _isLoading = false;
          if (_webViewHeight < 50.h) {
            _webViewHeight = 200.h; // 给个合理的默认高度
          }
        });
      }
    });

    // --- 初始化 WebView 控制器 (增强Android兼容性) ---
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) async {
            if (!_pageLoadedCompleter.isCompleted) {
              _pageLoadedCompleter.complete();
            }
            _injectJSForHeight(); // 页面加载完注入JS
            if (mounted) {
              setState(() {
                _isWebViewReady = true;
              });
            }
          },
          onWebResourceError: (WebResourceError error) {
            if (!_pageLoadedCompleter.isCompleted) {
              _pageLoadedCompleter.completeError(error);
            }
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
            }
          },
        ),
      )
      ..addJavaScriptChannel(
        _heightChannelName,
        onMessageReceived: (JavaScriptMessage message) {
          final String heightStr = message.message;
          final double? height = double.tryParse(heightStr);
          if (height != null && height > 0 && mounted) {
            final newHeight = height;
            if ((newHeight - _webViewHeight).abs() > 1.0) {
              setState(() {
                _webViewHeight = newHeight;
              });
            }
          }
        },
      );

    _scrollController.addListener(_onScroll); // 监听滚动
    _getCaseDetail(); // 获取案例详情数据
  }

  // --- 注入JS获取高度 ---
  Future<void> _injectJSForHeight() async {
    final String jsCode = '''
      try {
        function sendHeight() {
          let height = Math.max(
            document.body ? document.body.scrollHeight : 0,
            document.documentElement ? document.documentElement.scrollHeight : 0,
            300
          );
          
          // 兼容iOS和Android的不同JS通道访问方式
          let channel = null;
          if (typeof $_heightChannelName !== 'undefined' && $_heightChannelName && $_heightChannelName.postMessage) {
            channel = $_heightChannelName;  // Android方式
          } else if (window.$_heightChannelName && window.$_heightChannelName.postMessage) {
            channel = window.$_heightChannelName;  // iOS方式
          }
          
          if (channel) {
            try {
              channel.postMessage(height.toString());
            } catch (e) {
              console.error('发送高度失败:', e);
            }
          }
        }
        
        // 立即获取高度
        sendHeight();
        
        // 延迟重试，确保内容完全加载
        setTimeout(sendHeight, 500);
        setTimeout(sendHeight, 1500);
        
      } catch (e) { 
        console.error('JS脚本执行错误:', e);
      }
    '''
        .replaceAll('\$_heightChannelName', _heightChannelName);

    try {
      await _pageLoadedCompleter.future.timeout(const Duration(seconds: 5));
      await _webViewController.runJavaScript(jsCode);

      // 备用高度保护机制
      Timer(const Duration(seconds: 2), () {
        if (mounted && _webViewHeight <= 100.h) {
          setState(() {
            _webViewHeight = 300.h;
          });
        }
      });
    } catch (e) {
      if (mounted) {
        setState(() {
          _webViewHeight = 250.h;
        });
      }
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll); // 移除滚动监听
    _scrollController.dispose(); // 释放滚动控制器
    super.dispose();
  }

  // 滚动监听，更新AppBar透明度
  void _onScroll() {
    if (!mounted) return; // 检查组件是否挂载
    final double opacity = (_scrollController.offset / 100).clamp(0.0, 1.0);
    if (_opacity != opacity) {
      setState(() {
        _opacity = opacity;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 加载状态判断：只有在数据未获取到时才显示加载，WebView的加载不应该阻塞整个页面
    final bool showLoading = _caseDetailModel == null;

    return Scaffold(
      backgroundColor: Colors.white,
      extendBodyBehindAppBar: true, // AppBar背景透明
      appBar: AppBar(
        elevation: 0, // 无阴影
        backgroundColor: Colors.white.withOpacity(_opacity), // 根据滚动改变透明度
        iconTheme: IconThemeData(
          color: _opacity >= 0.5 ? Colors.black : Colors.white, // 图标颜色变化
        ),
        title: Text(
          widget.title, // 使用传入的页面标题
          style: TextStyle(
            color: Colors.black.withOpacity(_opacity), // 标题颜色变化
            fontSize: 16.sp, // 调整标题字体大小
          ),
        ),
      ),
      body: showLoading
          ? const Center(child: CircularProgressIndicator()) // 显示加载指示器
          : CustomScrollView(
              // 使用CustomScrollView实现复杂滚动效果
              controller: _scrollController,
              slivers: [
                // --- Sliver 1: 顶部背景图片 ---
                SliverToBoxAdapter(
                  child: _buildTopBackgroundImage(context), // 构建顶部图片
                ),

                // --- Sliver 2: 案例标题 ---
                SliverToBoxAdapter(
                  // 或者用 SliverPadding + SliverToBoxAdapter
                  child: Padding(
                    padding: EdgeInsets.only(
                        left: 20.w,
                        right: 20.w,
                        top: 20.h,
                        bottom: 10.h), // 调整间距
                    child: Text(
                      _caseDetailModel?.caseTitle ??
                          '案例标题加载中...', // 显示案例标题，处理null
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: HexColor('#333333'),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),

                // --- Sliver 3: 设计师信息 ---
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: _buildPersonalInfo(), // 构建设计师信息区域
                  ),
                ),

                // --- Sliver 4: 案例基本信息 ---
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.only(
                        left: 16.w, right: 16.w, top: 20.h), // 调整间距
                    child: _buildCaseInfo(), // 构建案例基本信息区域
                  ),
                ),

                // --- Sliver 5: "设计简介"标题 ---
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.only(
                        left: 16.w,
                        right: 16.w,
                        top: 20.h,
                        bottom: 4.h), // 调整间距
                    child: Text(
                      '设计简介',
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: HexColor('#333333'),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),

                // --- Sliver 6: WebView 内容区域 ---
                SliverToBoxAdapter(
                  child: SizedBox(
                    height: _webViewHeight, // 使用动态获取的高度
                    child: _caseDetailModel != null
                        ? WebViewWidget(
                            controller: _webViewController,
                          )
                        : Container(
                            alignment: Alignment.center,
                            child: const CircularProgressIndicator(),
                          ),
                  ),
                ),
              ],
            ),
    );
  }

  // 构建顶部背景图片 Widget
  Widget _buildTopBackgroundImage(BuildContext context) {
    // 默认图片URL，以防API数据中没有图片
    const String defaultImage =
        'https://fileserver.gazolife.cn/2025/7/20250701_ad9a4f70848f275491d5b675baad3cb7_20250701163439A344.png';

    // 安全地获取图片列表
    List<String> pics = _caseDetailModel?.caseMainPic ?? [];

    // 如果列表为空或没有获取到数据，使用默认图片，否则使用第一张
    final String imageUrl = pics.isEmpty ? defaultImage : pics.first;

    // 返回包含网络图片的 SizedBox
    return SizedBox(
      width: double.infinity,
      // 高度包含状态栏高度
      height: 150.h + MediaQuery.of(context).padding.top,
      child: NetworkImageHelper().getCachedNetworkImage(
        imageUrl: imageUrl,
        fit: BoxFit.cover, // 图片填充方式
      ),
    );
  }

  // 构建设计师个人信息区域 Widget
  Widget _buildPersonalInfo() {
    // 安全地获取设计师擅长风格列表
    List<String> excelStyles = _caseDetailModel?.excelStyle ?? [];
    int? caseNumber;
    String? designerName;
    String? avatar;
    caseNumber = _caseDetailModel?.caseNumber;
    designerName = _caseDetailModel?.designerName;
    avatar = _caseDetailModel?.avatar;

    // 准备显示的文本信息
    final String styleText =
        excelStyles.isEmpty ? "" : StringUtils.joinList(excelStyles); // 擅长风格
    final String caseCountText = StringUtils.formatDisplay(
      caseNumber, // 案例数量
      prefix: '案例作品',
      suffix: '套',
      defaultValue: '案例作品 N/A 套', // 处理null或无效值
    );
    // 组合风格和案例数量文本，只有风格不为空时才加分隔符
    final String combinedInfo =
        styleText.isNotEmpty ? '$styleText | $caseCountText' : caseCountText;

    // 返回 Row 布局
    return Row(
      mainAxisAlignment: MainAxisAlignment.start, // 主轴对齐方式
      crossAxisAlignment: CrossAxisAlignment.center, // 交叉轴对齐方式
      children: [
        // 设计师头像
        ClipOval(
          // 圆形裁剪
          child: NetworkImageHelper().getCachedNetworkImage(
            imageUrl: avatar ??
                'https://fileserver.gazolife.cn/2025/4/20250423_40d0c34c94ee99542fd4e8fe127681ce_20250423104550A695.png', // 头像URL，处理null
            width: 40.w,
            height: 40.h,
            fit: BoxFit.cover,
            // 可以添加 placeholder 或 errorWidget
          ),
        ),
        SizedBox(width: 12.w), // 头像和文字间距
        // 设计师姓名和信息
        Expanded(
          // 使用Expanded占据剩余空间，避免长文本溢出
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start, // 文字左对齐
            children: [
              Text(
                designerName ?? '设计师', // 姓名，处理null
                style: TextStyle(
                  fontSize: 15.sp,
                  color: HexColor('#222222'),
                  fontWeight: FontWeight.w500, // 加一点粗细
                ),
                maxLines: 1, // 最多一行
                overflow: TextOverflow.ellipsis, // 溢出显示省略号
              ),
              SizedBox(height: 4.h), // 姓名和下方信息间距
              Text(
                combinedInfo, // 显示组合后的信息
                style: TextStyle(
                  fontSize: 12.sp,
                  color: HexColor('#999999'),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        SizedBox(width: 12.w), // 信息和按钮间距
        // 预约按钮
        InkWell(
          // 添加点击波纹效果
          onTap: _showDialog, // 点击时显示弹窗
          borderRadius: BorderRadius.circular(14.w), // 匹配按钮圆角
          child: Container(
            width: 56.w,
            height: 28.h,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Colors.black, // 背景色
              borderRadius: BorderRadius.circular(14.w), // 圆角
            ),
            child: Text(
              '预约',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.white, // 文字颜色
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 构建案例基本信息区域 Widget
  Widget _buildCaseInfo() {
    // 安全地获取案例信息
    List<String> designStyles = _caseDetailModel?.designStyle ?? [];
    String? householdType;
    String? area;
    householdType = _caseDetailModel?.householdType;
    area = _caseDetailModel?.area;

    // 使用 '、' 连接设计风格
    final String styleText = StringUtils.joinList(designStyles, separator: '、');

    // 返回信息容器
    return Container(
      width: double.infinity, // 宽度撑满
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w), // 内边距
      decoration: BoxDecoration(
        color: HexColor('#F8F8F8'), // 背景色
        borderRadius: BorderRadius.circular(4.w), // 圆角
      ),
      child: IntrinsicHeight(
        // 让分隔线与内容等高
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween, // 子项均匀分布
          children: [
            // 户型信息列
            _buildInfoColumn('户型', householdType ?? 'N/A'),
            // 分隔线
            _buildVerticalDivider(),
            // 建筑面积列
            _buildInfoColumn(
              '建筑面积',
              StringUtils.formatDisplay(
                area,
                suffix: '㎡',
                defaultValue: 'N/A', // 处理null或无效值
              ),
            ),
            // 分隔线
            _buildVerticalDivider(),
            // 设计风格列
            _buildInfoColumn('设计风格', styleText.isNotEmpty ? styleText : 'N/A'),
          ],
        ),
      ),
    );
  }

  // 辅助方法：构建信息列 (用于户型、面积、风格)
  Widget _buildInfoColumn(String label, String value) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center, // 垂直居中
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 12.sp, color: HexColor('#999999')),
        ),
        SizedBox(height: 4.h), // 标签和值之间的间距
        Text(
          value,
          style: TextStyle(fontSize: 14.sp, color: HexColor('#333333')),
          maxLines: 1, // 最多一行
          overflow: TextOverflow.ellipsis, // 超出显示省略号
        ),
      ],
    );
  }

  // 辅助方法：构建垂直分隔线
  Widget _buildVerticalDivider() {
    return Container(
      width: 1,
      // height: 24.w, // 高度由IntrinsicHeight控制
      color: HexColor('#EEEEEE'), // 分隔线颜色
      margin: EdgeInsets.symmetric(horizontal: 8.w), // 水平间距，根据需要调整
    );
  }

  // 显示预约弹窗
  void _showDialog() {
    // 检查mounted状态，虽然show方法通常自己会处理
    if (!mounted) return;
    AppointmentBottomSheet.show(
      context,
      onSubmit: (name, contact) {
        // 回调中通常不需要再次检查mounted，因为是同步执行的
        debugPrint('预约姓名: $name, 联系方式: $contact');
        _handleSubmit(name, contact); // 调用提交方法
      },
    );
  }

  // 获取案例详情数据的异步方法
  Future<void> _getCaseDetail() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/cases/detail',
        queryParameters: {'id': widget.caseId},
      );
      if (response != null && mounted) {
        try {
          setState(() {
            _caseDetailModel = CaseDetail.fromJson(response);
            _isLoading = false;
          });
        } catch (e) {
          debugPrint('案例详情解析失败: $e');
          if (mounted) {
            setState(() {
              _isLoading = false;
            });
          }
        }

        // 延迟确保状态更新完成
        await Future.delayed(const Duration(milliseconds: 100));
        if (mounted) {
          String rawHtml = _caseDetailModel?.caseInfo ?? '';
          if (rawHtml.trim().isEmpty) {
            rawHtml =
                '<p style="text-align: center; color: #999; padding: 20px;">暂无设计简介内容</p>';
          }
          String finalHtml = """
            <!DOCTYPE html>
            <html>
            <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
              <style>
                body { font-family: sans-serif; word-wrap: break-word; margin: 0; padding: 16px; background-color: white; color: #333; line-height: 1.6; }
                img { max-width: 100%; height: auto; display: block; margin: 10px 0; }
                p { margin: 10px 0; }
                h1, h2, h3 { margin: 15px 0 10px 0; }
              </style>
            </head>
            <body>$rawHtml</body>
            </html>
          """;
          try {
            await _webViewController.loadHtmlString(finalHtml);
          } catch (e) {
            debugPrint('HTML加载失败: $e');
          }
        }
      } else if (mounted) {
        setState(() {
          _isLoading = false;
        });
        debugPrint('获取案例详情失败或组件已卸载');
      }
    } catch (e) {
      debugPrint('获取案例详情API调用出错：$e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 处理预约提交的异步方法
  void _handleSubmit(String name, String contact) async {
    // 可以考虑在这里添加一个加载指示器
    try {
      final apiManager = ApiManager();
      final response = await apiManager.post(
        '/api/home/<USER>/quick/appointment', // 提交API端点
        data: {'userName': name, 'userPhone': contact}, // 提交数据
      );
      // 检查组件是否仍然挂载
      if (mounted) {
        if (response != null) {
          // 提交成功提示
          showToast('预约成功');
          // 可以在这里关闭弹窗或执行其他操作
        } else {
          // 处理提交失败的情况
          showToast('预约失败，请稍后重试');
        }
      }
    } catch (e) {
      debugPrint('预约提交API调用出错：$e');
      if (mounted) {
        // API调用出错提示
        showToast('预约服务出错: $e');
      }
    } finally {
      // 无论成功或失败，都可以在这里移除加载指示器
    }
  }
}
