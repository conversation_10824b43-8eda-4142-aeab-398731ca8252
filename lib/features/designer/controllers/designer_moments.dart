import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/shared/components/photo_gallery.dart';
import 'package:flutter_smarthome/features/discover/controllers/article_detail.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/empty_state.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';
import 'package:flutter_smarthome/core/utils/network_image_helper.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:flutter_smarthome/core/models/designer_moment_model.dart';

class DesignerMomentsListWidget extends StatefulWidget {
  final String userId;
  const DesignerMomentsListWidget({
    super.key,
    required this.userId,
  });
  @override
  State<DesignerMomentsListWidget> createState() =>
      _DesignerMomentsListWidgetState();
}

class _DesignerMomentsListWidgetState extends State<DesignerMomentsListWidget> {
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  int pageNum = 1;
  final int pageSize = 10;
  final List<DesignerMomentModel> _designerMomentsList = [];

  @override
  void initState() {
    super.initState();
    _getMomentsList();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        header: const WaterDropHeader(),
        footer: CustomFooter(
          builder: (BuildContext context, LoadStatus? mode) {
            Widget body;
            if (mode == LoadStatus.idle) {
              body = const Text("上拉加载");
            } else if (mode == LoadStatus.loading) {
              body = const CircularProgressIndicator();
            } else if (mode == LoadStatus.failed) {
              body = const Text("加载失败！点击重试！");
            } else if (mode == LoadStatus.canLoading) {
              body = const Text("松手加载更多");
            } else {
              body = const Text("");
            }
            return SizedBox(
              height: 55.0,
              child: Center(child: body),
            );
          },
        ),
        controller: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        child: _buildListView(),
      ),
    );
  }

  Widget _buildListView() {
    return _designerMomentsList.isEmpty
        ? EmptyStateWidget(
            onRefresh: _onRefresh,
            emptyText: '暂无数据',
            buttonText: '点击刷新',
          )
        : ListView.builder(
            itemCount: _designerMomentsList.length,
            itemBuilder: (BuildContext context, int index) {
              return GestureDetector(
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => ArticleDetailWidget(
                        title: _designerMomentsList[index].dynamicTitle ?? '',
                        articleId: _designerMomentsList[index].id,
                      ),
                    ),
                  );
                },
                child: _buildListCell(_designerMomentsList[index]),
              );
            },
          );
  }

  Widget _buildListCell(DesignerMomentModel moment) {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w),
            child: Text(
              moment.dynamicTitle ?? '',
              style: TextStyle(
                fontSize: 15.sp,
                color: HexColor('#222222'),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w),
            child: Text(
              moment.dynamicInfo ?? '',
              style: TextStyle(
                fontSize: 13.sp,
                color: HexColor('#666666'),
              ),
            ),
          ),
          if (moment.dynamicPic.isNotEmpty)
            Container(
              margin: EdgeInsets.only(top: 12.h, bottom: 12.h),
              height: 100.h,
              child: ListView.builder(
                padding: EdgeInsets.only(left: 11.w, right: 12.w),
                scrollDirection: Axis.horizontal,
                itemCount: moment.dynamicPic.length,
                itemBuilder: (context, index) {
                  final List<String> images = moment.dynamicPic;
                  return GestureDetector(
                    onTap: () {
                      // 点击图片进入照片墙
                      _showPhotoGallery(context, images, index);
                    },
                    child: Container(
                      margin: EdgeInsets.only(left: 8.w),
                      width: 120.w,
                      height: 90.h,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(6),
                        child: NetworkImageHelper().getCachedNetworkImage(
                          imageUrl: images[index],
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          Padding(
            padding: EdgeInsets.only(left: 12.w, right: 12.w, top: 12.h),
            child: Text(
              moment.createTime ?? '',
              style: TextStyle(
                fontSize: 11.sp,
                color: HexColor('#999999'),
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 12.w, right: 12.w, top: 12.h),
            child: Divider(
              height: 1.h,
              color: Colors.grey[300],
            ),
          ),
        ],
      ),
    );
  }

  void _onRefresh() async {
    pageNum = 1;
    _designerMomentsList.clear();
    await _getMomentsList();
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    pageNum++;
    await _getMomentsList();
    if (_designerMomentsList.isEmpty) {
      _refreshController.loadNoData();
    } else {
      _refreshController.loadComplete();
    }
  }

  //获取设计师列表
  Future<void> _getMomentsList() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/designer/dynamic',
        queryParameters: {
          'pageNum': pageNum,
          'pageSize': pageSize,
          'userId': widget.userId,
        },
      );

      // 判断是否还有更多数据
      if (response['pageTotal'] == pageNum || response['pageTotal'] == 0) {
        _refreshController.loadNoData();
      }

      if (response['rows'] != null && response['rows'].isNotEmpty) {
        final List<DesignerMomentModel> arr =
            DesignerMomentModel.listFromJson(response['rows']);
        if (mounted) {
          setState(() {
            _designerMomentsList.addAll(arr);
          });
        }
      }
    } catch (e) {
      print(e);
    }
  }

  // 显示照片墙
  void _showPhotoGallery(
      BuildContext context, List<String> images, int initialIndex) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PhotoGalleryWidget(
          images: images,
          initialIndex: initialIndex,
        ),
      ),
    );
  }
}
