import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_smarthome/core/models/models.dart';

void main() {
  group('BaseModel Tests', () {
    test('safeString should handle various input types', () {
      final json = {
        'string_value': 'test',
        'int_value': 123,
        'null_value': null,
        'bool_value': true,
      };

      expect(BaseModel.safeString(json, 'string_value'), equals('test'));
      expect(BaseModel.safeString(json, 'int_value'), equals('123'));
      expect(BaseModel.safeString(json, 'null_value'), isNull);
      expect(BaseModel.safeString(json, 'bool_value'), equals('true'));
      expect(BaseModel.safeString(json, 'missing_key'), isNull);
    });

    test('safeInt should handle various input types', () {
      final json = {
        'int_value': 123,
        'string_int': '456',
        'string_invalid': 'abc',
        'double_value': 78.9,
        'null_value': null,
      };

      expect(BaseModel.safeInt(json, 'int_value'), equals(123));
      expect(BaseModel.safeInt(json, 'string_int'), equals(456));
      expect(BaseModel.safeInt(json, 'string_invalid'), isNull);
      expect(BaseModel.safeInt(json, 'double_value'), isNull);
      expect(BaseModel.safeInt(json, 'null_value'), isNull);
      expect(BaseModel.safeInt(json, 'missing_key'), isNull);
    });

    test('safeDouble should handle various input types', () {
      final json = {
        'double_value': 123.45,
        'int_value': 123,
        'string_double': '456.78',
        'string_invalid': 'abc',
        'null_value': null,
      };

      expect(BaseModel.safeDouble(json, 'double_value'), equals(123.45));
      expect(BaseModel.safeDouble(json, 'int_value'), equals(123.0));
      expect(BaseModel.safeDouble(json, 'string_double'), equals(456.78));
      expect(BaseModel.safeDouble(json, 'string_invalid'), isNull);
      expect(BaseModel.safeDouble(json, 'null_value'), isNull);
      expect(BaseModel.safeDouble(json, 'missing_key'), isNull);
    });

    test('safeBool should handle various input types', () {
      final json = {
        'bool_true': true,
        'bool_false': false,
        'string_true': 'true',
        'string_false': 'false',
        'string_1': '1',
        'string_0': '0',
        'int_1': 1,
        'int_0': 0,
        'null_value': null,
      };

      expect(BaseModel.safeBool(json, 'bool_true'), isTrue);
      expect(BaseModel.safeBool(json, 'bool_false'), isFalse);
      expect(BaseModel.safeBool(json, 'string_true'), isTrue);
      expect(BaseModel.safeBool(json, 'string_false'), isFalse);
      expect(BaseModel.safeBool(json, 'string_1'), isTrue);
      expect(BaseModel.safeBool(json, 'string_0'), isFalse);
      expect(BaseModel.safeBool(json, 'int_1'), isTrue);
      expect(BaseModel.safeBool(json, 'int_0'), isFalse);
      expect(BaseModel.safeBool(json, 'null_value'), isNull);
      expect(BaseModel.safeBool(json, 'missing_key'), isNull);
    });
  });

  group('UserModel Tests', () {
    test('should create UserModel from JSON', () {
      final json = {
        'mobile': '***********',
        'nickname': '测试用户',
        'name': '张三',
        'sex': '1',
        'avatar': 'https://example.com/avatar.jpg',
        'accessToken': 'token123',
        'city': '北京',
      };

      final user = UserModel.fromJson(json);

      expect(user.mobile, equals('***********'));
      expect(user.nickname, equals('测试用户'));
      expect(user.name, equals('张三'));
      expect(user.sex, equals('1'));
      expect(user.avatar, equals('https://example.com/avatar.jpg'));
      expect(user.accessToken, equals('token123'));
      expect(user.city, equals('北京'));
    });

    test('should handle null values in JSON', () {
      final json = <String, dynamic>{
        'mobile': null,
        'nickname': null,
      };

      final user = UserModel.fromJson(json);

      expect(user.mobile, isNull);
      expect(user.nickname, isNull);
    });

    test('should convert UserModel to JSON', () {
      final user = UserModel(
        mobile: '***********',
        nickname: '测试用户',
        accessToken: 'token123',
      );

      final json = user.toJson();

      expect(json['mobile'], equals('***********'));
      expect(json['nickname'], equals('测试用户'));
      expect(json['accessToken'], equals('token123'));
      expect(json['password'], isNull);
    });

    test('should create copy with updated values', () {
      final user = UserModel(
        mobile: '***********',
        nickname: '原昵称',
      );

      final updatedUser = user.copyWith(nickname: '新昵称');

      expect(updatedUser.mobile, equals('***********'));
      expect(updatedUser.nickname, equals('新昵称'));
      expect(user.nickname, equals('原昵称')); // 原对象不变
    });
  });

  group('ProductItem Tests', () {
    test('should create ProductItem from JSON', () {
      final json = {
        'id': 'product123',
        'mainPic': 'https://example.com/product.jpg',
        'name': '测试商品',
        'businessName': '测试商家',
        'businessLogo': 'https://example.com/logo.jpg',
        'minPrice': 99.99,
        'minPointPrice': '100',
      };

      final product = ProductItem.fromJson(json);

      expect(product.id, equals('product123'));
      expect(product.imageUrl, equals('https://example.com/product.jpg'));
      expect(product.title, equals('测试商品'));
      expect(product.shop, equals('测试商家'));
      expect(product.shopIcon, equals('https://example.com/logo.jpg'));
      expect(product.price, equals(99.99));
      expect(product.points, equals('100'));
    });

    test('should handle missing or null values', () {
      final json = {
        'id': 'product123',
        'name': '测试商品',
        'businessName': '测试商家',
        'minPrice': null,
        'minPointPrice': null,
      };

      final product = ProductItem.fromJson(json);

      expect(product.id, equals('product123'));
      expect(product.imageUrl, equals(''));
      expect(product.title, equals('测试商品'));
      expect(product.shop, equals('测试商家'));
      expect(product.shopIcon, isNull);
      expect(product.price, equals(0.0));
      expect(product.points, equals('0'));
    });

    test('should handle string price values', () {
      final json = {
        'id': 'product123',
        'mainPic': '',
        'name': '测试商品',
        'businessName': '测试商家',
        'minPrice': '199.99',
        'minPointPrice': '200',
      };

      final product = ProductItem.fromJson(json);

      expect(product.price, equals(199.99));
      expect(product.points, equals('200'));
    });

    test('should handle shopping_list.dart API response format', () {
      // 模拟shopping_list.dart中的实际API响应格式
      final json = {
        'id': 'prod456',
        'mainPic': 'https://example.com/image.jpg',
        'name': '实际商品',
        'businessName': '实际商家',
        'minPrice': 299,
        'minPointPrice': 300,
        'businessLogo': 'https://example.com/logo.jpg',
      };

      final product = ProductItem.fromJson(json);

      expect(product.id, equals('prod456'));
      expect(product.imageUrl, equals('https://example.com/image.jpg'));
      expect(product.title, equals('实际商品'));
      expect(product.shop, equals('实际商家'));
      expect(product.price, equals(299.0));
      expect(product.points, equals('300'));
      expect(product.shopIcon, equals('https://example.com/logo.jpg'));
    });
  });

  group('ShoppingDetail Tests', () {
    test('should create ShoppingDetail from JSON', () {
      final json = {
        'id': 'detail123',
        'name': '商品详情',
        'mainPic': 'https://example.com/main.jpg',
        'picUrls': ['https://example.com/1.jpg', 'https://example.com/2.jpg'],
        'commodityInfo': '<p>商品描述</p>',
        'businessId': 'business123',
        'businessName': '商家名称',
        'minPrice': 299.99,
        'isCollection': 'collection123',
        'collectionCount': 10,
      };

      final detail = ShoppingDetail.fromJson(json);

      expect(detail.id, equals('detail123'));
      expect(detail.name, equals('商品详情'));
      expect(detail.mainPic, equals('https://example.com/main.jpg'));
      expect(detail.picUrls, hasLength(2));
      expect(detail.picUrls![0], equals('https://example.com/1.jpg'));
      expect(detail.commodityInfo, equals('<p>商品描述</p>'));
      expect(detail.businessId, equals('business123'));
      expect(detail.businessName, equals('商家名称'));
      expect(detail.minPrice, equals(299.99));
      expect(detail.isCollection, equals('collection123'));
      expect(detail.collectionCount, equals(10));
    });
  });

  group('AddressModel Tests', () {
    test('should create AddressModel from JSON', () {
      final json = {
        'id': 'addr123',
        'firstName': '张三',
        'phoneNumber': '***********',
        'state': '北京市',
        'city': '北京市',
        'district': '朝阳区',
        'detailedAddress': '某某街道123号',
        'isDefault': "1", // 测试字符串格式
      };

      final address = AddressModel.fromJson(json);

      expect(address.id, equals('addr123'));
      expect(address.firstName, equals('张三'));
      expect(address.phoneNumber, equals('***********'));
      expect(address.state, equals('北京市'));
      expect(address.city, equals('北京市'));
      expect(address.district, equals('朝阳区'));
      expect(address.detailedAddress, equals('某某街道123号'));
      expect(address.isDefault, isTrue);
    });

    test('should generate complete address', () {
      final address = AddressModel(
        state: '北京市',
        city: '北京市',
        district: '朝阳区',
        detailedAddress: '某某街道123号',
      );

      expect(address.completeAddress, equals('北京市 北京市 朝阳区 某某街道123号'));
    });

    test('should handle different isDefault formats', () {
      // 测试字符串 "1"
      final json1 = {'isDefault': "1"};
      final address1 = AddressModel.fromJson(json1);
      expect(address1.isDefault, isTrue);

      // 测试字符串 "0"
      final json2 = {'isDefault': "0"};
      final address2 = AddressModel.fromJson(json2);
      expect(address2.isDefault, isFalse);

      // 测试布尔值
      final json3 = {'isDefault': true};
      final address3 = AddressModel.fromJson(json3);
      expect(address3.isDefault, isTrue);

      // 测试整数
      final json4 = {'isDefault': 1};
      final address4 = AddressModel.fromJson(json4);
      expect(address4.isDefault, isTrue);
    });
  });

  group('OrderModel Tests', () {
    test('should create OrderModel from JSON', () {
      final json = {
        'orderNumber': 'ORD123456',
        'orderStatus': '2',
        'orderStatusName': '已支付',
        'payAmount': 299.99,
        'customerCommodityShopOrderInfoItemRespVo': [
          {
            'commodityId': 'prod123',
            'name': '智能灯泡',
            'mainPic': 'https://example.com/image.jpg',
            'commodityPropertyId': 'prop123',
            'commodityProperty': '白光/E27',
            'commodityNum': 2,
            'unitPrice': 149.99,
            'totalPrice': 299.98,
          }
        ],
      };

      final order = OrderModel.fromJson(json);

      expect(order.orderNumber, equals('ORD123456'));
      expect(order.orderStatus, equals('2'));
      expect(order.orderStatusName, equals('已支付'));
      expect(order.payAmount, equals(299.99));
      expect(order.orderItems, hasLength(1));

      final firstItem = order.orderItems!.first;
      expect(firstItem.name, equals('智能灯泡'));
      expect(firstItem.commodityNum, equals(2));
      expect(firstItem.commodityProperty, equals('白光/E27'));
    });

    test('should handle empty order items', () {
      final json = {
        'orderNumber': 'ORD123456',
        'orderStatus': '1',
        'customerCommodityShopOrderInfoItemRespVo': [],
      };

      final order = OrderModel.fromJson(json);

      expect(order.orderNumber, equals('ORD123456'));
      expect(order.orderItems, isEmpty);
    });
  });

  group('CollectionType Tests', () {
    test('should find CollectionType by value', () {
      expect(CollectionType.fromValue(7), equals(CollectionType.commodity));
      expect(CollectionType.fromValue(1), equals(CollectionType.case_));
      expect(CollectionType.fromValue(2), equals(CollectionType.designer));
      expect(CollectionType.fromValue(3), equals(CollectionType.article));
      expect(CollectionType.fromValue(999), isNull);
    });

    test('should create collection requests', () {
      final commodityRequest = CollectionRequest.forCommodity('product123');
      expect(commodityRequest.businessId, equals('product123'));
      expect(commodityRequest.businessType, equals(7));

      final caseRequest = CollectionRequest.forCase('case123');
      expect(caseRequest.businessId, equals('case123'));
      expect(caseRequest.businessType, equals(1));
    });
  });

  group('ApiResponse Tests', () {
    test('should create ApiResponse from JSON', () {
      final json = {
        'code': 200,
        'msg': '成功',
        'data': {'key': 'value'},
      };

      final response = ApiResponse<Map<String, dynamic>>.fromJson(
        json,
        (data) => data as Map<String, dynamic>,
      );

      expect(response.code, equals(200));
      expect(response.message, equals('成功'));
      expect(response.success, isTrue);
      expect(response.data, isNotNull);
      expect(response.data!['key'], equals('value'));
    });

    test('should handle error response', () {
      final json = {
        'code': 500,
        'msg': '服务器错误',
        'data': null,
      };

      final response = ApiResponse<String>.fromJson(json, null);

      expect(response.code, equals(500));
      expect(response.message, equals('服务器错误'));
      expect(response.success, isFalse);
      expect(response.data, isNull);
    });
  });

  group('ShoppingCartItem Tests', () {
    test('should create ShoppingCartItem from shopping cart API response', () {
      // 模拟购物车API的实际响应格式
      final json = {
        'id': 'cart123',
        'commodityId': 'commodity456',
        'commodityPropertyId': 'property789',
        'name': '测试购物车商品',
        'mainPic': 'https://example.com/cart-item.jpg',
        'commodityProperty': '红色,大号',
        'businessName': '测试商家',
        'businessLogo': 'https://example.com/business-logo.jpg',
        'count': 2,
        'salesPrice': 199.99,
        'pointPrice': '200',
        'commoditySkuStatus': '1',
      };

      final cartItem = ShoppingCartItem.fromJson(json);

      expect(cartItem.id, equals('cart123'));
      expect(cartItem.commodityId, equals('commodity456'));
      expect(cartItem.commodityPropertyId, equals('property789'));
      expect(cartItem.name, equals('测试购物车商品'));
      expect(cartItem.mainPic, equals('https://example.com/cart-item.jpg'));
      expect(cartItem.commodityProperty, equals('红色,大号'));
      expect(cartItem.businessName, equals('测试商家'));
      expect(cartItem.businessLogo,
          equals('https://example.com/business-logo.jpg'));
      expect(cartItem.count, equals(2));
      expect(cartItem.salesPrice, equals(199.99));
      expect(cartItem.pointPrice, equals('200'));
      expect(cartItem.commoditySkuStatus, equals('1'));
      expect(cartItem.selected, isFalse);
    });

    test('should handle invalid cart item status', () {
      final json = {
        'id': 'cart456',
        'name': '失效商品',
        'commoditySkuStatus': '0',
        'count': 1,
        'salesPrice': 99.99,
      };

      final cartItem = ShoppingCartItem.fromJson(json);

      expect(cartItem.isInvalid, isTrue);
      expect(cartItem.isValid, isFalse);
    });

    test('should handle valid cart item status', () {
      final json = {
        'id': 'cart789',
        'name': '有效商品',
        'commoditySkuStatus': '1',
        'count': 3,
        'salesPrice': 299.99,
      };

      final cartItem = ShoppingCartItem.fromJson(json);

      expect(cartItem.isValid, isTrue);
      expect(cartItem.isInvalid, isFalse);
    });

    test('should create copy with updated selection', () {
      final cartItem = ShoppingCartItem(
        id: 'cart123',
        name: '测试商品',
        selected: false,
      );

      final selectedItem = cartItem.copyWith(selected: true);

      expect(selectedItem.selected, isTrue);
      expect(cartItem.selected, isFalse); // 原对象不变
      expect(selectedItem.id, equals('cart123'));
      expect(selectedItem.name, equals('测试商品'));
    });
  });

  group('DesignerCaseModel Tests', () {
    test('should create DesignerCaseModel from JSON', () {
      final json = {
        'id': 'case123',
        'caseTitle': '现代简约风格案例',
        'caseIntro': '这是一个现代简约风格的设计案例',
        'caseMainPic': [
          'https://example.com/case1.jpg',
          'https://example.com/case2.jpg'
        ],
        'excelStyle': ['现代', '简约'],
        'householdType': '三室两厅',
      };

      final designerCase = DesignerCaseModel.fromJson(json);

      expect(designerCase.id, equals('case123'));
      expect(designerCase.caseTitle, equals('现代简约风格案例'));
      expect(designerCase.caseIntro, equals('这是一个现代简约风格的设计案例'));
      expect(designerCase.caseMainPic, hasLength(2));
      expect(
          designerCase.caseMainPic[0], equals('https://example.com/case1.jpg'));
      expect(designerCase.excelStyle, hasLength(2));
      expect(designerCase.excelStyle[0], equals('现代'));
      expect(designerCase.householdType, equals('三室两厅'));
    });

    test('should handle null and missing values', () {
      final json = {
        'id': 'case123',
        'caseTitle': '测试案例',
        'caseMainPic': null,
        'excelStyle': null,
      };

      final designerCase = DesignerCaseModel.fromJson(json);

      expect(designerCase.id, equals('case123'));
      expect(designerCase.caseTitle, equals('测试案例'));
      expect(designerCase.caseMainPic, isEmpty);
      expect(designerCase.excelStyle, isEmpty);
      expect(designerCase.caseIntro, isNull);
    });

    test('should convert to JSON correctly', () {
      final designerCase = DesignerCaseModel(
        id: 'case123',
        caseTitle: '测试案例',
        caseMainPic: ['https://example.com/image.jpg'],
        excelStyle: ['现代'],
        householdType: '三室两厅',
        caseIntro: '测试简介',
      );

      final json = designerCase.toJson();

      expect(json['id'], equals('case123'));
      expect(json['caseTitle'], equals('测试案例'));
      expect(json['caseMainPic'], hasLength(1));
      expect(json['excelStyle'], hasLength(1));
      expect(json['householdType'], equals('三室两厅'));
      expect(json['caseIntro'], equals('测试简介'));
    });

    test('should create copy with updated values', () {
      final originalCase = DesignerCaseModel(
        id: 'case123',
        caseTitle: '原标题',
        caseMainPic: ['image1.jpg'],
        excelStyle: ['现代'],
      );

      final updatedCase = originalCase.copyWith(
        caseTitle: '新标题',
        householdType: '两室一厅',
      );

      expect(updatedCase.id, equals('case123'));
      expect(updatedCase.caseTitle, equals('新标题'));
      expect(updatedCase.householdType, equals('两室一厅'));
      expect(updatedCase.caseMainPic, equals(['image1.jpg']));
      expect(originalCase.caseTitle, equals('原标题')); // 原对象不变
    });

    test('should parse list from JSON', () {
      final jsonList = [
        {
          'id': 'case1',
          'caseTitle': '案例1',
          'caseMainPic': ['image1.jpg'],
          'excelStyle': ['现代'],
        },
        {
          'id': 'case2',
          'caseTitle': '案例2',
          'caseMainPic': ['image2.jpg'],
          'excelStyle': ['简约'],
        },
      ];

      final caseList = DesignerCaseModel.listFromJson(jsonList);

      expect(caseList, hasLength(2));
      expect(caseList[0].id, equals('case1'));
      expect(caseList[1].id, equals('case2'));
    });
  });

  group('ArticleModel Tests', () {
    test('should create ArticleModel from JSON', () {
      final json = {
        'id': 'article123',
        'resourceTitle': '智能家居装修指南',
        'resourceIntro': '智能家居装修的完整指南',
        'resourceInfo': '<p>这是一篇关于智能家居装修的文章</p>',
        'mainPic': 'https://example.com/article.jpg',
        'createTime': '2024-01-01 10:00:00',
        'userLikeCount': '100',
        'userCommentCount': '50',
      };

      final article = ArticleModel.fromJson(json);

      expect(article.id, equals('article123'));
      expect(article.resourceTitle, equals('智能家居装修指南'));
      expect(article.resourceIntro, equals('智能家居装修的完整指南'));
      expect(article.resourceInfo, equals('<p>这是一篇关于智能家居装修的文章</p>'));
      expect(article.mainPic, equals('https://example.com/article.jpg'));
      expect(article.createTime, equals('2024-01-01 10:00:00'));
      expect(article.userLikeCount, equals('100'));
      expect(article.userCommentCount, equals('50'));
    });

    test('should handle missing optional fields', () {
      final json = {
        'id': 'article123',
        'resourceTitle': '测试文章',
      };

      final article = ArticleModel.fromJson(json);

      expect(article.id, equals('article123'));
      expect(article.resourceTitle, equals('测试文章'));
      expect(article.resourceIntro, isNull);
      expect(article.resourceInfo, isNull);
      expect(article.mainPic, isNull);
      expect(article.userLikeCount, isNull);
      expect(article.userCommentCount, isNull);
    });

    test('should convert to JSON correctly', () {
      final article = ArticleModel(
        id: 'article123',
        resourceTitle: '测试文章',
        resourceIntro: '测试简介',
        mainPic: 'https://example.com/test.jpg',
        userLikeCount: '10',
      );

      final json = article.toJson();

      expect(json['id'], equals('article123'));
      expect(json['resourceTitle'], equals('测试文章'));
      expect(json['resourceIntro'], equals('测试简介'));
      expect(json['mainPic'], equals('https://example.com/test.jpg'));
      expect(json['userLikeCount'], equals('10'));
    });

    test('should create copy with updated fields', () {
      final originalArticle = ArticleModel(
        id: 'article123',
        resourceTitle: '原标题',
        userLikeCount: '5',
      );

      final updatedArticle = originalArticle.copyWithFields(
        resourceTitle: '新标题',
        resourceIntro: '新简介',
      );

      expect(updatedArticle.id, equals('article123'));
      expect(updatedArticle.resourceTitle, equals('新标题'));
      expect(updatedArticle.resourceIntro, equals('新简介'));
      expect(updatedArticle.userLikeCount, equals('5'));
      expect(originalArticle.resourceTitle, equals('原标题')); // 原对象不变
    });
  });

  group('RecommendModel Tests', () {
    test('should create RecommendModel with article data', () {
      final articleJson = {
        'id': 'article123',
        'resourceTitle': '推荐文章',
        'resourceInfo': '文章内容',
        'mainPic': 'https://example.com/article.jpg',
      };

      final json = {
        'resourceType': 4,
        'gazoHuiArticle': articleJson,
      };

      final recommend = RecommendModel.fromJson(json);

      expect(recommend.resourceType, equals(4));
      expect(recommend.article, isNotNull);
      expect(recommend.article!.id, equals('article123'));
      expect(recommend.article!.resourceTitle, equals('推荐文章'));
      expect(recommend.designerCase, isNull);
    });

    test('should create RecommendModel with designer case data', () {
      final caseJson = {
        'id': 'case123',
        'caseTitle': '推荐案例',
        'caseMainPic': ['https://example.com/case.jpg'],
        'excelStyle': ['现代'],
        'householdType': '三室两厅',
      };

      final json = {
        'resourceType': 1,
        'gazoHuiDesignerCase': caseJson,
      };

      final recommend = RecommendModel.fromJson(json);

      expect(recommend.resourceType, equals(1));
      expect(recommend.designerCase, isNotNull);
      expect(recommend.designerCase!.id, equals('case123'));
      expect(recommend.designerCase!.caseTitle, equals('推荐案例'));
      expect(recommend.article, isNull);
    });

    test('should handle unknown resource type', () {
      final json = {
        'resourceType': 999,
      };

      final recommend = RecommendModel.fromJson(json);

      expect(recommend.resourceType, equals(999));
      expect(recommend.article, isNull);
      expect(recommend.designerCase, isNull);
    });

    test('should convert to JSON correctly', () {
      final article = ArticleModel(
        id: 'article123',
        resourceTitle: '测试文章',
      );

      final recommend = RecommendModel(
        resourceType: 4,
        article: article,
      );

      final json = recommend.toJson();

      expect(json['resourceType'], equals(4));
      expect(json['gazoHuiArticle'], isNotNull);
      expect(json['gazoHuiDesignerCase'], isNull);
    });

    test('should create copy with updated values', () {
      final originalRecommend = RecommendModel(
        resourceType: 4,
        article: ArticleModel(id: 'article123'),
      );

      final updatedRecommend = originalRecommend.copyWith(
        resourceType: 1,
        designerCase: DesignerCaseModel(
          id: 'case123',
          caseMainPic: [],
          excelStyle: [],
        ),
      );

      expect(updatedRecommend.resourceType, equals(1));
      expect(updatedRecommend.designerCase, isNotNull);
      expect(updatedRecommend.article, isNotNull); // 保留原有的article
      expect(originalRecommend.resourceType, equals(4)); // 原对象不变
    });

    test('should parse list from JSON', () {
      final jsonList = [
        {
          'resourceType': 4,
          'gazoHuiArticle': {
            'id': 'article1',
            'resourceTitle': '文章1',
          },
        },
        {
          'resourceType': 1,
          'gazoHuiDesignerCase': {
            'id': 'case1',
            'caseTitle': '案例1',
            'caseMainPic': [],
            'excelStyle': [],
          },
        },
      ];

      final recommendList = RecommendModel.listFromJson(jsonList);

      expect(recommendList, hasLength(2));
      expect(recommendList[0].resourceType, equals(4));
      expect(recommendList[0].article, isNotNull);
      expect(recommendList[1].resourceType, equals(1));
      expect(recommendList[1].designerCase, isNotNull);
    });
  });

  group('PostModel Tests', () {
    test('should create PostModel from JSON', () {
      final json = {
        'avatarUrl': 'https://example.com/avatar.jpg',
        'postId': 123,
        'postName': '软件工程师',
        'tenantCode': 'TENANT001',
      };

      final post = PostModel.fromJson(json);

      expect(post.avatarUrl, equals('https://example.com/avatar.jpg'));
      expect(post.postId, equals(123));
      expect(post.postName, equals('软件工程师'));
      expect(post.tenantCode, equals('TENANT001'));
    });

    test('should handle missing optional fields', () {
      final json = {
        'postId': 456,
        'postName': '产品经理',
        'tenantCode': 'TENANT002',
      };

      final post = PostModel.fromJson(json);

      expect(post.avatarUrl, isNull);
      expect(post.postId, equals(456));
      expect(post.postName, equals('产品经理'));
      expect(post.tenantCode, equals('TENANT002'));
    });

    test('should handle invalid data types', () {
      final json = {
        'postId': 'invalid_id', // 字符串而不是整数
        'postName': null,
        'tenantCode': null,
      };

      final post = PostModel.fromJson(json);

      expect(post.postId, equals(0)); // 默认值
      expect(post.postName, equals('')); // 默认值
      expect(post.tenantCode, equals('')); // 默认值
    });

    test('should convert to JSON correctly', () {
      final post = PostModel(
        avatarUrl: 'https://example.com/avatar.jpg',
        postId: 789,
        postName: '设计师',
        tenantCode: 'TENANT003',
      );

      final json = post.toJson();

      expect(json['avatarUrl'], equals('https://example.com/avatar.jpg'));
      expect(json['postId'], equals(789));
      expect(json['postName'], equals('设计师'));
      expect(json['tenantCode'], equals('TENANT003'));
    });
  });

  group('ShoppingDetail Tests', () {
    test('should create ShoppingDetail from JSON with all fields', () {
      final json = {
        'id': 'detail123',
        'name': '智能灯泡',
        'mainPic': 'https://example.com/main.jpg',
        'picUrls': ['https://example.com/1.jpg', 'https://example.com/2.jpg'],
        'commodityInfo': '<p>商品详细信息</p>',
        'businessId': 'business123',
        'businessName': '智能家居商店',
        'businessLogo': 'https://example.com/logo.jpg',
        'minPrice': 199.99,
        'minPointPrice': '200',
        'isCollection': 'collection123',
        'collectionCount': 50,
        'commodityPropertyId': 'property123',
      };

      final detail = ShoppingDetail.fromJson(json);

      expect(detail.id, equals('detail123'));
      expect(detail.name, equals('智能灯泡'));
      expect(detail.mainPic, equals('https://example.com/main.jpg'));
      expect(detail.picUrls, hasLength(2));
      expect(detail.picUrls![0], equals('https://example.com/1.jpg'));
      expect(detail.commodityInfo, equals('<p>商品详细信息</p>'));
      expect(detail.businessId, equals('business123'));
      expect(detail.businessName, equals('智能家居商店'));
      expect(detail.minPrice, equals(199.99));
      expect(detail.isCollection, equals('collection123'));
      expect(detail.collectionCount, equals(50));
    });

    test('should handle null and missing values', () {
      final json = {
        'id': 'detail456',
        'name': '测试商品',
        'picUrls': null,
        'minPrice': null,
        'collectionCount': null,
      };

      final detail = ShoppingDetail.fromJson(json);

      expect(detail.id, equals('detail456'));
      expect(detail.name, equals('测试商品'));
      expect(detail.picUrls, isNull);
      expect(detail.minPrice, isNull);
      expect(detail.collectionCount, isNull);
      expect(detail.businessLogo, isNull);
    });

    test('should convert to JSON correctly', () {
      final detail = ShoppingDetail(
        id: 'detail789',
        name: '测试商品',
        mainPic: 'https://example.com/test.jpg',
        businessName: '测试商家',
        minPrice: 99.99,
      );

      final json = detail.toJson();

      expect(json['id'], equals('detail789'));
      expect(json['name'], equals('测试商品'));
      expect(json['mainPic'], equals('https://example.com/test.jpg'));
      expect(json['businessName'], equals('测试商家'));
      expect(json['minPrice'], equals(99.99));
    });
  });
}
