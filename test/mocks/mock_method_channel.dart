import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';

/// Mock MethodChannel for testing platform-specific functionality
/// 为测试平台特定功能提供模拟的MethodChannel实现
class MockMethodChannel {
  static final Map<String, MethodChannel> _channels = {};
  static final Map<String, Map<String, dynamic>> _responses = {};
  static final Map<String, List<MethodCall>> _callHistory = {};

  /// 设置方法调用的模拟响应
  static void setMockMethodCallHandler(
    String channelName,
    String methodName,
    dynamic response,
  ) {
    _responses[channelName] ??= {};
    _responses[channelName]![methodName] = response;
    
    _callHistory[channelName] ??= [];
    
    // 获取或创建MethodChannel
    final channel = _getChannel(channelName);
    
    // 设置方法调用处理器
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
      // 记录方法调用
      _callHistory[channelName]!.add(methodCall);
      
      // 返回模拟响应
      final channelResponses = _responses[channelName];
      if (channelResponses != null && channelResponses.containsKey(methodCall.method)) {
        final response = channelResponses[methodCall.method];
        if (response is Exception) {
          throw response;
        }
        return response;
      }
      
      // 默认返回null
      return null;
    });
  }

  /// 设置方法调用抛出异常
  static void setMockMethodCallException(
    String channelName,
    String methodName,
    Exception exception,
  ) {
    setMockMethodCallHandler(channelName, methodName, exception);
  }

  /// 获取指定通道的方法调用历史
  static List<MethodCall> getCallHistory(String channelName) {
    return List.unmodifiable(_callHistory[channelName] ?? []);
  }

  /// 获取最后一次方法调用
  static MethodCall? getLastCall(String channelName) {
    final history = _callHistory[channelName];
    return history != null && history.isNotEmpty ? history.last : null;
  }

  /// 检查是否调用了指定方法
  static bool wasMethodCalled(String channelName, String methodName) {
    final history = _callHistory[channelName];
    return history?.any((call) => call.method == methodName) ?? false;
  }

  /// 获取指定方法的调用次数
  static int getMethodCallCount(String channelName, String methodName) {
    final history = _callHistory[channelName];
    return history?.where((call) => call.method == methodName).length ?? 0;
  }

  /// 清除所有模拟数据
  static void clearAll() {
    for (final channelName in _channels.keys) {
      final channel = _channels[channelName]!;
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, null);
    }
    
    _channels.clear();
    _responses.clear();
    _callHistory.clear();
  }

  /// 清除指定通道的模拟数据
  static void clearChannel(String channelName) {
    final channel = _channels[channelName];
    if (channel != null) {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, null);
      _channels.remove(channelName);
    }
    
    _responses.remove(channelName);
    _callHistory.remove(channelName);
  }

  /// 获取或创建MethodChannel
  static MethodChannel _getChannel(String channelName) {
    return _channels.putIfAbsent(channelName, () => MethodChannel(channelName));
  }
}

/// 用户管理相关的MethodChannel模拟
class MockUserMethodChannel {
  static const String channelName = 'com.example.app/user';

  /// 设置用户同步成功响应
  static void setUserSyncSuccess() {
    MockMethodChannel.setMockMethodCallHandler(
      channelName,
      'syncUser',
      {'success': true},
    );
  }

  /// 设置用户同步失败响应
  static void setUserSyncFailure() {
    MockMethodChannel.setMockMethodCallException(
      channelName,
      'syncUser',
      PlatformException(code: 'SYNC_FAILED', message: 'Failed to sync user'),
    );
  }

  /// 设置清除用户成功响应
  static void setClearUserSuccess() {
    MockMethodChannel.setMockMethodCallHandler(
      channelName,
      'clearUser',
      {'success': true},
    );
  }

  /// 模拟用户更新通知
  static Future<void> simulateUserUpdated(Map<String, dynamic> userData) async {
    const channel = MethodChannel(channelName);
    await TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .handlePlatformMessage(
      channelName,
      const StandardMethodCodec().encodeMethodCall(
        MethodCall('userUpdated', userData),
      ),
      (data) {},
    );
  }

  /// 模拟用户清除通知
  static Future<void> simulateUserCleared() async {
    const channel = MethodChannel(channelName);
    await TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .handlePlatformMessage(
      channelName,
      const StandardMethodCodec().encodeMethodCall(
        const MethodCall('userCleared'),
      ),
      (data) {},
    );
  }

  /// 清除用户相关的模拟数据
  static void clear() {
    MockMethodChannel.clearChannel(channelName);
  }
}

/// 导航控制相关的MethodChannel模拟
class MockNavigationMethodChannel {
  static const String channelName = 'com.smartlife.navigation';

  /// 设置显示导航栏成功响应
  static void setShowNavigationBarSuccess() {
    MockMethodChannel.setMockMethodCallHandler(
      channelName,
      'showNavigationBar',
      {'success': true},
    );
  }

  /// 设置隐藏导航栏成功响应
  static void setHideNavigationBarSuccess() {
    MockMethodChannel.setMockMethodCallHandler(
      channelName,
      'hideNavigationBar',
      {'success': true},
    );
  }

  /// 清除导航相关的模拟数据
  static void clear() {
    MockMethodChannel.clearChannel(channelName);
  }
}
