import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';

/// Mock Dio for testing network requests
/// 为测试网络请求提供模拟的Dio实现
class MockDio extends Fake implements Dio {
  final Map<String, dynamic> _responses = {};
  final List<RequestOptions> _requests = [];
  bool _shouldThrowError = false;
  DioException? _errorToThrow;

  @override
  BaseOptions get options => BaseOptions();

  @override
  Interceptors get interceptors => Interceptors();

  /// 设置模拟响应
  void setMockResponse(String path, dynamic data, {int statusCode = 200}) {
    _responses[path] = {
      'data': data,
      'statusCode': statusCode,
    };
  }

  /// 设置错误响应
  void setMockError(DioException error) {
    _shouldThrowError = true;
    _errorToThrow = error;
  }

  /// 清除所有模拟数据
  void clearMocks() {
    _responses.clear();
    _requests.clear();
    _shouldThrowError = false;
    _errorToThrow = null;
  }

  /// 获取请求历史
  List<RequestOptions> get requestHistory => List.unmodifiable(_requests);

  /// 获取最后一个请求
  RequestOptions? get lastRequest => _requests.isNotEmpty ? _requests.last : null;

  @override
  Future<Response<T>> get<T>(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    return _handleRequest<T>('GET', path, data: data, queryParameters: queryParameters, options: options);
  }

  @override
  Future<Response<T>> post<T>(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    return _handleRequest<T>('POST', path, data: data, queryParameters: queryParameters, options: options);
  }

  @override
  Future<Response<T>> put<T>(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    return _handleRequest<T>('PUT', path, data: data, queryParameters: queryParameters, options: options);
  }

  @override
  Future<Response<T>> delete<T>(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    return _handleRequest<T>('DELETE', path, data: data, queryParameters: queryParameters, options: options);
  }

  Future<Response<T>> _handleRequest<T>(
    String method,
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    // 记录请求
    final requestOptions = RequestOptions(
      path: path,
      method: method,
      data: data,
      queryParameters: queryParameters,
    );
    _requests.add(requestOptions);

    // 如果设置了抛出错误
    if (_shouldThrowError && _errorToThrow != null) {
      throw _errorToThrow!;
    }

    // 查找模拟响应
    final mockResponse = _responses[path];
    if (mockResponse != null) {
      return Response<T>(
        data: mockResponse['data'] as T,
        statusCode: mockResponse['statusCode'] as int,
        requestOptions: requestOptions,
      );
    }

    // 默认成功响应
    return Response<T>(
      data: {'code': 200, 'msg': 'success', 'data': null} as T,
      statusCode: 200,
      requestOptions: requestOptions,
    );
  }
}

/// 创建模拟的DioException
DioException createMockDioException({
  required String message,
  DioExceptionType type = DioExceptionType.unknown,
  int? statusCode,
  dynamic responseData,
}) {
  final requestOptions = RequestOptions(path: '/test');
  
  Response? response;
  if (statusCode != null) {
    response = Response(
      statusCode: statusCode,
      data: responseData,
      requestOptions: requestOptions,
    );
  }

  return DioException(
    requestOptions: requestOptions,
    response: response,
    type: type,
    error: message,
  );
}
