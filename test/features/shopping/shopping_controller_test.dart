import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_smarthome/core/models/models.dart';
import '../../helpers/test_helpers.dart';
import '../../mocks/mock_shared_preferences.dart';
import '../../mocks/mock_method_channel.dart';

void main() {
  group('Shopping Controller Tests', () {
    setUp(() async {
      // 清除所有模拟数据
      MockSharedPreferences.clear();
      MockUserMethodChannel.clear();
      
      // 设置基础的模拟数据
      MockUserMethodChannel.setUserSyncSuccess();
    });

    tearDown(() {
      MockSharedPreferences.clear();
      MockUserMethodChannel.clear();
    });

    group('ProductItem Widget Tests', () {
      testWidgets('should display product information correctly', (WidgetTester tester) async {
        final testProduct = TestHelpers.createTestProduct(
          title: '智能灯泡',
          shop: '智能家居店',
          price: 99.99,
          points: '100',
        );

        // 创建一个简单的产品展示Widget
        final widget = TestHelpers.createTestWidget(
          Card(
            child: Column(
              children: [
                Text(testProduct.title ?? ''),
                Text(testProduct.shop ?? ''),
                Text('¥${testProduct.price}'),
                Text('${testProduct.points}积分'),
              ],
            ),
          ),
        );

        await tester.pumpWidget(widget);

        // 验证产品信息显示
        expect(find.text('智能灯泡'), findsOneWidget);
        expect(find.text('智能家居店'), findsOneWidget);
        expect(find.text('¥99.99'), findsOneWidget);
        expect(find.text('100积分'), findsOneWidget);
      });

      testWidgets('should handle empty product data', (WidgetTester tester) async {
        final emptyProduct = ProductItem();

        final widget = TestHelpers.createTestWidget(
          Card(
            child: Column(
              children: [
                Text(emptyProduct.title ?? '暂无标题'),
                Text(emptyProduct.shop ?? '暂无商家'),
                Text('¥${emptyProduct.price ?? 0}'),
              ],
            ),
          ),
        );

        await tester.pumpWidget(widget);

        expect(find.text('暂无标题'), findsOneWidget);
        expect(find.text('暂无商家'), findsOneWidget);
        expect(find.text('¥0.0'), findsOneWidget);
      });
    });

    group('ShoppingCartItem Widget Tests', () {
      testWidgets('should display cart item correctly', (WidgetTester tester) async {
        final testCartItem = TestHelpers.createTestCartItem(
          name: '智能开关',
          businessName: '智能设备店',
          count: 2,
          salesPrice: 199.99,
          selected: false,
        );

        final widget = TestHelpers.createTestWidget(
          Card(
            child: Row(
              children: [
                Checkbox(
                  value: testCartItem.selected,
                  onChanged: (value) {},
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(testCartItem.name ?? ''),
                      Text(testCartItem.businessName ?? ''),
                      Text('数量: ${testCartItem.count}'),
                      Text('¥${testCartItem.salesPrice}'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );

        await tester.pumpWidget(widget);

        expect(find.text('智能开关'), findsOneWidget);
        expect(find.text('智能设备店'), findsOneWidget);
        expect(find.text('数量: 2'), findsOneWidget);
        expect(find.text('¥199.99'), findsOneWidget);
        expect(find.byType(Checkbox), findsOneWidget);
      });

      testWidgets('should handle cart item selection', (WidgetTester tester) async {
        bool isSelected = false;
        final testCartItem = TestHelpers.createTestCartItem(
          name: '测试商品',
          selected: isSelected,
        );

        final widget = TestHelpers.createTestWidget(
          StatefulBuilder(
            builder: (context, setState) {
              return Checkbox(
                value: isSelected,
                onChanged: (value) {
                  setState(() {
                    isSelected = value ?? false;
                  });
                },
              );
            },
          ),
        );

        await tester.pumpWidget(widget);

        // 初始状态未选中
        expect(tester.widget<Checkbox>(find.byType(Checkbox)).value, isFalse);

        // 点击选择
        await tester.tap(find.byType(Checkbox));
        await tester.pump();

        // 验证状态改变
        expect(tester.widget<Checkbox>(find.byType(Checkbox)).value, isTrue);
      });

      testWidgets('should show invalid item status', (WidgetTester tester) async {
        final invalidCartItem = TestHelpers.createTestCartItem(
          name: '失效商品',
          commoditySkuStatus: '0', // 失效状态
        );

        final widget = TestHelpers.createTestWidget(
          Card(
            child: Column(
              children: [
                Text(invalidCartItem.name ?? ''),
                Text(invalidCartItem.isInvalid ? '商品已失效' : '商品有效'),
              ],
            ),
          ),
        );

        await tester.pumpWidget(widget);

        expect(find.text('失效商品'), findsOneWidget);
        expect(find.text('商品已失效'), findsOneWidget);
      });
    });

    group('Shopping Data Processing Tests', () {
      test('should calculate total price correctly', () {
        final cartItems = [
          TestHelpers.createTestCartItem(
            salesPrice: 100.0,
            count: 2,
            selected: true,
          ),
          TestHelpers.createTestCartItem(
            salesPrice: 50.0,
            count: 1,
            selected: true,
          ),
          TestHelpers.createTestCartItem(
            salesPrice: 200.0,
            count: 1,
            selected: false, // 未选中，不计入总价
          ),
        ];

        // 计算选中商品的总价
        double totalPrice = 0.0;
        for (final item in cartItems) {
          if (item.selected == true) {
            totalPrice += (item.salesPrice ?? 0.0) * (item.count ?? 0);
          }
        }

        expect(totalPrice, equals(250.0)); // (100*2) + (50*1) = 250
      });

      test('should filter valid cart items', () {
        final cartItems = [
          TestHelpers.createTestCartItem(
            name: '有效商品1',
            commoditySkuStatus: '1',
          ),
          TestHelpers.createTestCartItem(
            name: '失效商品',
            commoditySkuStatus: '0',
          ),
          TestHelpers.createTestCartItem(
            name: '有效商品2',
            commoditySkuStatus: '1',
          ),
        ];

        final validItems = cartItems.where((item) => item.isValid).toList();

        expect(validItems, hasLength(2));
        expect(validItems[0].name, equals('有效商品1'));
        expect(validItems[1].name, equals('有效商品2'));
      });

      test('should group items by business', () {
        final cartItems = [
          TestHelpers.createTestCartItem(
            name: '商品1',
            businessName: '商家A',
          ),
          TestHelpers.createTestCartItem(
            name: '商品2',
            businessName: '商家B',
          ),
          TestHelpers.createTestCartItem(
            name: '商品3',
            businessName: '商家A',
          ),
        ];

        // 按商家分组
        final groupedItems = <String, List<ShoppingCartItem>>{};
        for (final item in cartItems) {
          final businessName = item.businessName ?? '未知商家';
          groupedItems.putIfAbsent(businessName, () => []).add(item);
        }

        expect(groupedItems.keys, hasLength(2));
        expect(groupedItems['商家A'], hasLength(2));
        expect(groupedItems['商家B'], hasLength(1));
      });
    });

    group('Shopping Order Tests', () {
      test('should create order from cart items', () {
        final selectedItems = [
          TestHelpers.createTestCartItem(
            commodityId: 'prod1',
            name: '商品1',
            count: 2,
            salesPrice: 100.0,
          ),
          TestHelpers.createTestCartItem(
            commodityId: 'prod2',
            name: '商品2',
            count: 1,
            salesPrice: 200.0,
          ),
        ];

        // 模拟创建订单
        final orderItems = selectedItems.map((cartItem) {
          return OrderItemModel(
            commodityId: cartItem.commodityId,
            name: cartItem.name,
            commodityNum: cartItem.count,
            unitPrice: cartItem.salesPrice,
            totalPrice: (cartItem.salesPrice ?? 0.0) * (cartItem.count ?? 0),
          );
        }).toList();

        final totalAmount = orderItems.fold<double>(
          0.0,
          (sum, item) => sum + (item.totalPrice ?? 0.0),
        );

        expect(orderItems, hasLength(2));
        expect(totalAmount, equals(400.0)); // (100*2) + (200*1) = 400
        expect(orderItems[0].name, equals('商品1'));
        expect(orderItems[1].name, equals('商品2'));
      });
    });

    group('Product Search and Filter Tests', () {
      test('should filter products by price range', () {
        final products = [
          TestHelpers.createTestProduct(title: '便宜商品', price: 50.0),
          TestHelpers.createTestProduct(title: '中等商品', price: 150.0),
          TestHelpers.createTestProduct(title: '昂贵商品', price: 500.0),
        ];

        // 筛选价格在100-300之间的商品
        final filteredProducts = products.where((product) {
          final price = product.price ?? 0.0;
          return price >= 100.0 && price <= 300.0;
        }).toList();

        expect(filteredProducts, hasLength(1));
        expect(filteredProducts[0].title, equals('中等商品'));
      });

      test('should search products by keyword', () {
        final products = [
          TestHelpers.createTestProduct(title: '智能灯泡'),
          TestHelpers.createTestProduct(title: '智能开关'),
          TestHelpers.createTestProduct(title: '普通插座'),
        ];

        // 搜索包含"智能"的商品
        final searchResults = products.where((product) {
          final title = product.title ?? '';
          return title.contains('智能');
        }).toList();

        expect(searchResults, hasLength(2));
        expect(searchResults[0].title, equals('智能灯泡'));
        expect(searchResults[1].title, equals('智能开关'));
      });
    });
  });
}
