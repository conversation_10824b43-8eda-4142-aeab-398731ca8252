# GAZO-SmartHome 测试文档

## 概述

本目录包含了GAZO-SmartHome项目的全面单元测试，覆盖了核心功能模块、工具类、数据模型和共享组件。

## 测试结构

```
test/
├── README.md                          # 测试文档
├── models_test.dart                    # 数据模型测试
├── widget_test.dart                    # Widget测试
├── core/                              # 核心模块测试
│   ├── debug/
│   │   └── network_debug_manager_test.dart
│   ├── network/
│   │   └── api_manager_test.dart
│   ├── services/
│   │   └── firebase_service_test.dart
│   └── utils/
│       ├── navigation_controller_test.dart
│       ├── network_image_helper_test.dart
│       ├── string_utils_test.dart
│       └── user_manager_test.dart
├── features/                          # 功能模块测试
│   ├── decoration/
│   │   └── decoration_controller_test.dart
│   ├── designer/
│   │   └── designer_controller_test.dart
│   └── shopping/
│       └── shopping_controller_test.dart
├── shared/                            # 共享组件测试
│   ├── components/
│   │   └── shared_components_test.dart
│   └── dialogs/
│       └── dialogs_test.dart
├── helpers/                           # 测试辅助工具
│   └── test_helpers.dart
└── mocks/                             # Mock工具
    ├── mock_dio.dart
    ├── mock_method_channel.dart
    └── mock_shared_preferences.dart
```

## 测试覆盖范围

### 1. 核心服务类测试
- **UserManager**: 用户管理、登录状态、数据持久化
- **ApiManager**: HTTP请求、认证、错误处理、Token刷新
- **FirebaseService**: 崩溃报告、日志记录
- **NetworkDebugManager**: 网络调试、请求记录、统计信息

### 2. 工具类测试
- **StringUtils**: 字符串处理、列表拼接、格式化
- **NavigationController**: 导航控制、MethodChannel通信
- **NetworkImageHelper**: 网络图片加载、缓存、错误处理

### 3. 数据模型测试
- **BaseModel**: 基础模型、安全类型转换
- **UserModel**: 用户数据、JSON序列化
- **ProductItem**: 商品信息、购物相关
- **ShoppingCartItem**: 购物车、选择状态
- **AddressModel**: 地址管理、默认地址
- **OrderModel**: 订单信息、订单项
- **DesignerCaseModel**: 设计案例、风格标签
- **ArticleModel**: 文章资讯、内容管理
- **RecommendModel**: 推荐内容、资源类型

### 4. 功能模块测试
- **Shopping**: 商品展示、购物车、订单处理
- **Decoration**: 装修项目、地址管理、进度跟踪
- **Designer**: 设计案例、设计师信息、咨询服务

### 5. 共享组件测试
- **UI组件**: 按钮、输入框、卡片、列表
- **对话框**: 确认对话框、输入对话框、选择对话框
- **表单验证**: 输入验证、错误提示

## 运行测试

### 运行所有测试
```bash
flutter test
```

### 运行特定测试文件
```bash
# 运行模型测试
flutter test test/models_test.dart

# 运行核心服务测试
flutter test test/core/

# 运行功能模块测试
flutter test test/features/

# 运行共享组件测试
flutter test test/shared/
```

### 生成测试覆盖率报告
```bash
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
open coverage/html/index.html
```

## Mock工具说明

### MockSharedPreferences
模拟SharedPreferences，用于测试数据持久化功能。

```dart
// 设置测试数据
MockSharedPreferences.setMockInitialValues({
  'user_key': '{"mobile": "13800138000"}',
});

// 清空数据
MockSharedPreferences.clear();
```

### MockDio
模拟HTTP请求，用于测试网络相关功能。

```dart
final mockDio = MockDio();

// 设置模拟响应
mockDio.setMockResponse('/api/user', {
  'code': 200,
  'data': {'id': '123', 'name': 'Test User'}
});

// 设置错误响应
mockDio.setMockError(DioException(...));
```

### MockMethodChannel
模拟MethodChannel，用于测试平台通信功能。

```dart
// 设置方法调用响应
MockMethodChannel.setMockMethodCallHandler(
  'channel_name',
  'method_name',
  {'success': true}
);

// 验证方法是否被调用
expect(
  MockMethodChannel.wasMethodCalled('channel_name', 'method_name'),
  isTrue
);
```

## 测试辅助工具

### TestHelpers
提供常用的测试数据创建和验证方法。

```dart
// 创建测试用户
final testUser = TestHelpers.createTestUser(
  mobile: '13800138000',
  nickname: '测试用户',
);

// 创建测试商品
final testProduct = TestHelpers.createTestProduct(
  title: '智能灯泡',
  price: 99.99,
);

// 验证JSON序列化
TestHelpers.verifyJsonSerialization(model, Model.fromJson);
```

## 测试最佳实践

### 1. 测试命名
- 使用描述性的测试名称
- 遵循 "should [expected behavior] when [condition]" 格式

### 2. 测试结构
- 使用 `group` 组织相关测试
- 在 `setUp` 中初始化测试数据
- 在 `tearDown` 中清理资源

### 3. 断言
- 使用具体的断言方法
- 验证边界条件和异常情况
- 测试正常流程和错误流程

### 4. Mock使用
- 只Mock外部依赖
- 验证Mock的调用
- 清理Mock状态

## 持续集成

测试应该在以下情况下运行：
- 每次代码提交
- Pull Request创建时
- 发布前的完整测试

## 贡献指南

添加新功能时，请确保：
1. 为新代码编写相应的单元测试
2. 测试覆盖率不低于80%
3. 所有测试都能通过
4. 更新相关的测试文档

## 常见问题

### Q: 测试运行缓慢怎么办？
A: 可以使用 `flutter test --plain-name="specific test"` 运行特定测试。

### Q: Mock不生效怎么办？
A: 确保在测试开始前正确设置Mock，在测试结束后清理Mock状态。

### Q: Widget测试失败怎么办？
A: 检查是否正确使用了 `pumpWidget` 和 `pumpAndSettle`，确保UI更新完成。

## 参考资源

- [Flutter测试文档](https://flutter.dev/docs/testing)
- [Mockito使用指南](https://pub.dev/packages/mockito)
- [测试最佳实践](https://flutter.dev/docs/testing/best-practices)
