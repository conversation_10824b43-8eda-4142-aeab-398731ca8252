import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_smarthome/core/models/models.dart';

/// 测试辅助工具类
/// 提供常用的测试数据和工具方法
class TestHelpers {
  /// 创建测试用的UserModel
  static UserModel createTestUser({
    String? mobile = '13800138000',
    String? nickname = '测试用户',
    String? name = '张三',
    String? accessToken = 'test_token_123',
    String? city = '北京',
  }) {
    return UserModel(
      mobile: mobile,
      nickname: nickname,
      name: name,
      accessToken: accessToken,
      city: city,
    );
  }

  /// 创建测试用的ProductItem
  static ProductItem createTestProduct({
    String? id = 'test_product_123',
    String? title = '测试商品',
    String? shop = '测试商家',
    double? price = 99.99,
    String? points = '100',
    String? imageUrl = 'https://example.com/image.jpg',
  }) {
    return ProductItem(
      id: id,
      title: headline6,
      shop: shop,
      price: price,
      points: points,
      imageUrl: imageUrl,
    );
  }

  /// 创建测试用的AddressModel
  static AddressModel createTestAddress({
    String? id = 'test_address_123',
    String? firstName = '张三',
    String? phoneNumber = '13800138000',
    String? state = '北京市',
    String? city = '北京市',
    String? district = '朝阳区',
    String? detailedAddress = '某某街道123号',
    bool? isDefault = false,
  }) {
    return AddressModel(
      id: id,
      firstName: firstName,
      phoneNumber: phoneNumber,
      state: state,
      city: city,
      district: district,
      detailedAddress: detailedAddress,
      isDefault: isDefault,
    );
  }

  /// 创建测试用的ShoppingCartItem
  static ShoppingCartItem createTestCartItem({
    String? id = 'test_cart_123',
    String? commodityId = 'test_commodity_123',
    String? name = '测试购物车商品',
    String? mainPic = 'https://example.com/cart-item.jpg',
    String? commodityProperty = '红色,大号',
    String? businessName = '测试商家',
    int? count = 2,
    double? salesPrice = 199.99,
    String? pointPrice = '200',
    String? commoditySkuStatus = '1',
    bool? selected = false,
  }) {
    return ShoppingCartItem(
      id: id,
      commodityId: commodityId,
      name: name,
      mainPic: mainPic,
      commodityProperty: commodityProperty,
      businessName: businessName,
      count: count,
      salesPrice: salesPrice,
      pointPrice: pointPrice,
      commoditySkuStatus: commoditySkuStatus,
      selected: selected,
    );
  }

  /// 创建测试用的OrderModel
  static OrderModel createTestOrder({
    String? orderNumber = 'ORD123456',
    String? orderStatus = '2',
    String? orderStatusName = '已支付',
    double? payAmount = 299.99,
    List<OrderItemModel>? orderItems,
  }) {
    orderItems ??= [
      OrderItemModel(
        commodityId: 'prod123',
        name: '智能灯泡',
        mainPic: 'https://example.com/image.jpg',
        commodityPropertyId: 'prop123',
        commodityProperty: '白光/E27',
        commodityNum: 2,
        unitPrice: 149.99,
        totalPrice: 299.98,
      ),
    ];

    return OrderModel(
      orderNumber: orderNumber,
      orderStatus: orderStatus,
      orderStatusName: orderStatusName,
      payAmount: payAmount,
      orderItems: orderItems,
    );
  }

  /// 创建测试用的ArticleModel
  static ArticleModel createTestArticle({
    String? id = 'test_article_123',
    String? title = '测试文章标题',
    String? content = '测试文章内容',
    String? mainPic = 'https://example.com/article.jpg',
    String? createTime = '2024-01-01 12:00:00',
    int? viewCount = 100,
    int? likeCount = 10,
  }) {
    return ArticleModel(
      id: id,
      title: title,
      content: content,
      mainPic: mainPic,
      createTime: createTime,
      viewCount: viewCount,
      likeCount: likeCount,
    );
  }

  /// 创建测试用的DesignerCaseModel
  static DesignerCaseModel createTestDesignerCase({
    String? id = 'test_case_123',
    String? caseTitle = '测试设计案例',
    String? caseIntro = '这是一个测试设计案例',
    String? caseMainPic = 'https://example.com/case.jpg',
    String? designerName = '测试设计师',
    String? designerId = 'designer_123',
    String? designStyle = '现代简约',
    double? area = 120.5,
    String? householdType = '三室两厅',
    int? viewCount = 200,
    int? likeCount = 20,
  }) {
    return DesignerCaseModel(
      id: id,
      caseTitle: caseTitle,
      caseIntro: caseIntro,
      caseMainPic: caseMainPic,
      designerName: designerName,
      designerId: designerId,
      designStyle: designStyle,
      area: area,
      householdType: householdType,
      viewCount: viewCount,
      likeCount: likeCount,
    );
  }

  /// 等待异步操作完成
  static Future<void> waitForAsync() async {
    await Future.delayed(Duration.zero);
  }

  /// 等待指定时间
  static Future<void> waitFor(Duration duration) async {
    await Future.delayed(duration);
  }

  /// 验证JSON序列化和反序列化
  static void verifyJsonSerialization<T extends BaseModel>(
    T model,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    // 序列化为JSON
    final json = model.toJson();
    
    // 从JSON反序列化
    final deserializedModel = fromJson(json);
    
    // 再次序列化，验证一致性
    final secondJson = deserializedModel.toJson();
    
    // 验证两次序列化结果一致
    expect(json, equals(secondJson));
  }

  /// 创建测试用的Widget包装器
  static Widget createTestWidget(Widget child) {
    return MaterialApp(
      home: Scaffold(
        body: child,
      ),
    );
  }

  /// 验证列表不为空且包含指定数量的元素
  static void verifyListNotEmpty<T>(List<T>? list, {int? expectedLength}) {
    expect(list, isNotNull);
    expect(list, isNotEmpty);
    if (expectedLength != null) {
      expect(list!.length, equals(expectedLength));
    }
  }

  /// 验证字符串不为空
  static void verifyStringNotEmpty(String? value) {
    expect(value, isNotNull);
    expect(value, isNotEmpty);
  }

  /// 验证数值在指定范围内
  static void verifyNumberInRange(num? value, num min, num max) {
    expect(value, isNotNull);
    expect(value!, greaterThanOrEqualTo(min));
    expect(value, lessThanOrEqualTo(max));
  }
}
