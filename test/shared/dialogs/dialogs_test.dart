import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../helpers/test_helpers.dart';

void main() {
  group('Shared Dialogs Tests', () {
    group('Confirmation Dialog Tests', () {
      testWidgets('should display confirmation dialog with title and message', (WidgetTester tester) async {
        bool? result;

        final widget = TestHelpers.createTestWidget(
          Builder(
            builder: (context) {
              return ElevatedButton(
                onPressed: () async {
                  result = await showDialog<bool>(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('确认操作'),
                      content: const Text('您确定要执行此操作吗？'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(false),
                          child: const Text('取消'),
                        ),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(true),
                          child: const Text('确认'),
                        ),
                      ],
                    ),
                  );
                },
                child: const Text('显示确认对话框'),
              );
            },
          ),
        );

        await tester.pumpWidget(widget);

        // 点击按钮显示对话框
        await tester.tap(find.text('显示确认对话框'));
        await tester.pumpAndSettle();

        expect(find.text('确认操作'), findsOneWidget);
        expect(find.text('您确定要执行此操作吗？'), findsOneWidget);
        expect(find.text('取消'), findsOneWidget);
        expect(find.text('确认'), findsOneWidget);

        // 测试确认按钮
        await tester.tap(find.text('确认'));
        await tester.pumpAndSettle();

        expect(result, isTrue);
        expect(find.text('确认操作'), findsNothing);
      });

      testWidgets('should handle cancel action', (WidgetTester tester) async {
        bool? result;

        final widget = TestHelpers.createTestWidget(
          Builder(
            builder: (context) {
              return ElevatedButton(
                onPressed: () async {
                  result = await showDialog<bool>(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('删除确认'),
                      content: const Text('删除后无法恢复，确定删除吗？'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(false),
                          child: const Text('取消'),
                        ),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(true),
                          style: TextButton.styleFrom(foregroundColor: Colors.red),
                          child: const Text('删除'),
                        ),
                      ],
                    ),
                  );
                },
                child: const Text('删除项目'),
              );
            },
          ),
        );

        await tester.pumpWidget(widget);

        await tester.tap(find.text('删除项目'));
        await tester.pumpAndSettle();

        // 点击取消
        await tester.tap(find.text('取消'));
        await tester.pumpAndSettle();

        expect(result, isFalse);
      });
    });

    group('Input Dialog Tests', () {
      testWidgets('should display input dialog', (WidgetTester tester) async {
        String? inputResult;
        final controller = TextEditingController();

        final widget = TestHelpers.createTestWidget(
          Builder(
            builder: (context) {
              return ElevatedButton(
                onPressed: () async {
                  inputResult = await showDialog<String>(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('输入名称'),
                      content: TextField(
                        controller: controller,
                        decoration: const InputDecoration(
                          hintText: '请输入项目名称',
                          border: OutlineInputBorder(),
                        ),
                        autofocus: true,
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('取消'),
                        ),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(controller.text),
                          child: const Text('确定'),
                        ),
                      ],
                    ),
                  );
                },
                child: const Text('输入对话框'),
              );
            },
          ),
        );

        await tester.pumpWidget(widget);

        await tester.tap(find.text('输入对话框'));
        await tester.pumpAndSettle();

        expect(find.text('输入名称'), findsOneWidget);
        expect(find.text('请输入项目名称'), findsOneWidget);

        // 输入文本
        await tester.enterText(find.byType(TextField), '新项目');
        await tester.tap(find.text('确定'));
        await tester.pumpAndSettle();

        expect(inputResult, equals('新项目'));
      });

      testWidgets('should validate input in dialog', (WidgetTester tester) async {
        final formKey = GlobalKey<FormState>();
        final controller = TextEditingController();

        final widget = TestHelpers.createTestWidget(
          Builder(
            builder: (context) {
              return ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('创建账户'),
                      content: Form(
                        key: formKey,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            TextFormField(
                              controller: controller,
                              decoration: const InputDecoration(
                                labelText: '用户名',
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return '请输入用户名';
                                }
                                if (value.length < 3) {
                                  return '用户名至少3个字符';
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('取消'),
                        ),
                        TextButton(
                          onPressed: () {
                            if (formKey.currentState!.validate()) {
                              Navigator.of(context).pop(controller.text);
                            }
                          },
                          child: const Text('创建'),
                        ),
                      ],
                    ),
                  );
                },
                child: const Text('创建账户'),
              );
            },
          ),
        );

        await tester.pumpWidget(widget);

        await tester.tap(find.text('创建账户'));
        await tester.pumpAndSettle();

        // 测试空值验证
        await tester.tap(find.text('创建'));
        await tester.pump();
        expect(find.text('请输入用户名'), findsOneWidget);

        // 测试长度验证
        await tester.enterText(find.byType(TextFormField), 'ab');
        await tester.tap(find.text('创建'));
        await tester.pump();
        expect(find.text('用户名至少3个字符'), findsOneWidget);

        // 测试有效输入
        await tester.enterText(find.byType(TextFormField), 'validuser');
        await tester.tap(find.text('创建'));
        await tester.pumpAndSettle();

        expect(find.text('创建账户'), findsNothing);
      });
    });

    group('Selection Dialog Tests', () {
      testWidgets('should display single selection dialog', (WidgetTester tester) async {
        String? selectedOption;
        final options = ['选项1', '选项2', '选项3'];

        final widget = TestHelpers.createTestWidget(
          Builder(
            builder: (context) {
              return ElevatedButton(
                onPressed: () async {
                  selectedOption = await showDialog<String>(
                    context: context,
                    builder: (context) => SimpleDialog(
                      title: const Text('选择选项'),
                      children: options.map((option) {
                        return SimpleDialogOption(
                          onPressed: () => Navigator.of(context).pop(option),
                          child: Text(option),
                        );
                      }).toList(),
                    ),
                  );
                },
                child: const Text('选择选项'),
              );
            },
          ),
        );

        await tester.pumpWidget(widget);

        await tester.tap(find.text('选择选项'));
        await tester.pumpAndSettle();

        expect(find.text('选择选项'), findsNWidgets(2)); // 标题和按钮
        expect(find.text('选项1'), findsOneWidget);
        expect(find.text('选项2'), findsOneWidget);
        expect(find.text('选项3'), findsOneWidget);

        // 选择选项2
        await tester.tap(find.text('选项2'));
        await tester.pumpAndSettle();

        expect(selectedOption, equals('选项2'));
      });

      testWidgets('should display multi-selection dialog', (WidgetTester tester) async {
        List<String> selectedOptions = [];
        final options = ['选项A', '选项B', '选项C'];
        final selectedStates = List.generate(options.length, (index) => false);

        final widget = TestHelpers.createTestWidget(
          StatefulBuilder(
            builder: (context, setState) {
              return ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('多选选项'),
                      content: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: options.asMap().entries.map((entry) {
                          final index = entry.key;
                          final option = entry.value;
                          return CheckboxListTile(
                            title: Text(option),
                            value: selectedStates[index],
                            onChanged: (value) {
                              setState(() {
                                selectedStates[index] = value ?? false;
                              });
                            },
                          );
                        }).toList(),
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('取消'),
                        ),
                        TextButton(
                          onPressed: () {
                            selectedOptions = [];
                            for (int i = 0; i < options.length; i++) {
                              if (selectedStates[i]) {
                                selectedOptions.add(options[i]);
                              }
                            }
                            Navigator.of(context).pop();
                          },
                          child: const Text('确定'),
                        ),
                      ],
                    ),
                  );
                },
                child: const Text('多选对话框'),
              );
            },
          ),
        );

        await tester.pumpWidget(widget);

        await tester.tap(find.text('多选对话框'));
        await tester.pumpAndSettle();

        expect(find.text('多选选项'), findsOneWidget);
        expect(find.byType(CheckboxListTile), findsNWidgets(3));

        // 选择选项A和选项C
        await tester.tap(find.text('选项A'));
        await tester.pump();
        await tester.tap(find.text('选项C'));
        await tester.pump();

        await tester.tap(find.text('确定'));
        await tester.pumpAndSettle();

        expect(selectedOptions, hasLength(2));
        expect(selectedOptions, contains('选项A'));
        expect(selectedOptions, contains('选项C'));
      });
    });

    group('Loading Dialog Tests', () {
      testWidgets('should display loading dialog', (WidgetTester tester) async {
        final widget = TestHelpers.createTestWidget(
          Builder(
            builder: (context) {
              return ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) => const AlertDialog(
                      content: Row(
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(width: 16),
                          Text('加载中...'),
                        ],
                      ),
                    ),
                  );
                },
                child: const Text('显示加载'),
              );
            },
          ),
        );

        await tester.pumpWidget(widget);

        await tester.tap(find.text('显示加载'));
        await tester.pumpAndSettle();

        expect(find.text('加载中...'), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsOneWidget);

        // 测试不能通过点击背景关闭
        await tester.tapAt(const Offset(50, 50));
        await tester.pump();
        expect(find.text('加载中...'), findsOneWidget);
      });
    });
  });
}
