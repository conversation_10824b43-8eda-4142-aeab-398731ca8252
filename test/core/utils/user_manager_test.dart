import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_smarthome/core/utils/user_manager.dart';
import '../../mocks/mock_shared_preferences.dart';
import '../../mocks/mock_method_channel.dart';
import '../../helpers/test_helpers.dart';

void main() {
  group('UserManager Tests', () {
    late UserManager userManager;

    setUp(() async {
      // 清除所有模拟数据
      MockSharedPreferences.clear();
      MockUserMethodChannel.clear();
      
      // 设置MethodChannel模拟
      MockUserMethodChannel.setUserSyncSuccess();
      MockUserMethodChannel.setClearUserSuccess();
      
      // 获取UserManager实例
      userManager = UserManager.instance;
      await userManager.init();
    });

    tearDown(() {
      MockSharedPreferences.clear();
      MockUserMethodChannel.clear();
    });

    group('Initialization', () {
      test('should initialize successfully', () async {
        expect(userManager, isNotNull);
        expect(userManager.notifier, isNotNull);
      });

      test('should load existing user from SharedPreferences', () async {
        // 准备测试数据
        final testUser = TestHelpers.createTestUser();
        MockSharedPreferences.setString('user_key', testUser.toJson().toString());
        
        // 重新初始化
        await userManager.init();
        
        // 验证用户已加载
        expect(userManager.user, isNotNull);
        expect(userManager.isLoggedIn, isTrue);
      });

      test('should handle invalid user data gracefully', () async {
        // 设置无效的用户数据
        MockSharedPreferences.setString('user_key', 'invalid_json');
        
        // 重新初始化
        await userManager.init();
        
        // 验证用户为空
        expect(userManager.user, isNull);
        expect(userManager.isLoggedIn, isFalse);
      });
    });

    group('User Management', () {
      test('should save user successfully', () async {
        final testUser = TestHelpers.createTestUser();
        
        await userManager.saveUser(testUser);
        
        expect(userManager.user, isNotNull);
        expect(userManager.user!.mobile, equals(testUser.mobile));
        expect(userManager.user!.nickname, equals(testUser.nickname));
        expect(userManager.isLoggedIn, isTrue);
        
        // 验证数据已保存到SharedPreferences
        expect(MockSharedPreferences.containsKey('user_key'), isTrue);
      });

      test('should clear user successfully', () async {
        // 先保存用户
        final testUser = TestHelpers.createTestUser();
        await userManager.saveUser(testUser);
        expect(userManager.isLoggedIn, isTrue);
        
        // 清除用户
        await userManager.clearUser();
        
        expect(userManager.user, isNull);
        expect(userManager.isLoggedIn, isFalse);
        expect(MockSharedPreferences.containsKey('user_key'), isFalse);
      });

      test('should update user successfully', () async {
        // 先保存用户
        final testUser = TestHelpers.createTestUser();
        await userManager.saveUser(testUser);
        
        // 更新用户
        await userManager.updateUser((user) {
          user.nickname = '更新后的昵称';
          user.city = '上海';
        });
        
        expect(userManager.user!.nickname, equals('更新后的昵称'));
        expect(userManager.user!.city, equals('上海'));
        expect(userManager.user!.mobile, equals(testUser.mobile)); // 其他字段保持不变
      });

      test('should throw exception when updating non-existent user', () async {
        expect(
          () => userManager.updateUser((user) => user.nickname = 'test'),
          throwsA(isA<UserManagerException>()),
        );
      });
    });

    group('Login Status', () {
      test('should return false when user is null', () {
        expect(userManager.isLoggedIn, isFalse);
      });

      test('should return false when accessToken is null', () async {
        final userWithoutToken = TestHelpers.createTestUser(accessToken: null);
        await userManager.saveUser(userWithoutToken);
        
        expect(userManager.isLoggedIn, isFalse);
      });

      test('should return false when accessToken is empty', () async {
        final userWithEmptyToken = TestHelpers.createTestUser(accessToken: '');
        await userManager.saveUser(userWithEmptyToken);
        
        expect(userManager.isLoggedIn, isFalse);
      });

      test('should return true when user has valid accessToken', () async {
        final userWithToken = TestHelpers.createTestUser(accessToken: 'valid_token');
        await userManager.saveUser(userWithToken);
        
        expect(userManager.isLoggedIn, isTrue);
      });
    });

    group('Notifications', () {
      test('should notify listeners when user changes', () async {
        bool notified = false;
        userManager.notifier.addListener(() {
          notified = true;
        });
        
        final testUser = TestHelpers.createTestUser();
        await userManager.saveUser(testUser);
        
        expect(notified, isTrue);
      });

      test('should notify listeners when user is cleared', () async {
        // 先保存用户
        final testUser = TestHelpers.createTestUser();
        await userManager.saveUser(testUser);
        
        bool notified = false;
        userManager.notifier.addListener(() {
          notified = true;
        });
        
        await userManager.clearUser();
        
        expect(notified, isTrue);
      });
    });

    group('Error Handling', () {
      test('should handle SharedPreferences errors gracefully', () async {
        // 这里可以模拟SharedPreferences错误，但由于我们使用的是Mock，
        // 实际的错误处理测试可能需要更复杂的设置
        expect(() => userManager.init(), returnsNormally);
      });

      test('should handle JSON parsing errors gracefully', () async {
        MockSharedPreferences.setString('user_key', '{invalid json}');
        
        await userManager.init();
        
        expect(userManager.user, isNull);
        expect(userManager.isLoggedIn, isFalse);
      });
    });

    group('Method Channel Integration', () {
      test('should sync user to native platform', () async {
        final testUser = TestHelpers.createTestUser();
        
        await userManager.saveUser(testUser);
        
        // 等待异步操作完成
        await TestHelpers.waitForAsync();
        
        // 验证MethodChannel被调用
        expect(
          MockMethodChannel.wasMethodCalled('com.example.app/user', 'syncUser'),
          isTrue,
        );
      });

      test('should handle native sync errors gracefully', () async {
        // 设置MethodChannel抛出异常
        MockUserMethodChannel.setUserSyncFailure();
        
        final testUser = TestHelpers.createTestUser();
        
        // 保存用户应该成功，即使原生同步失败
        await expectLater(
          userManager.saveUser(testUser),
          completes,
        );
        
        expect(userManager.user, isNotNull);
        expect(userManager.isLoggedIn, isTrue);
      });
    });
  });
}
