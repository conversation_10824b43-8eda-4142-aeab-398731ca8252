import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_smarthome/core/utils/string_utils.dart';

void main() {
  group('StringUtils Tests', () {
    group('joinList', () {
      test('should join string list with default separator', () {
        final list = ['apple', 'banana', 'cherry'];
        final result = StringUtils.joinList(list);
        
        expect(result, equals('apple, banana, cherry'));
      });

      test('should join string list with custom separator', () {
        final list = ['apple', 'banana', 'cherry'];
        final result = StringUtils.joinList(list, separator: ' | ');
        
        expect(result, equals('apple | banana | cherry'));
      });

      test('should handle mixed type list', () {
        final list = ['apple', 123, true, 45.67];
        final result = StringUtils.joinList(list);
        
        expect(result, equals('apple, 123, true, 45.67'));
      });

      test('should handle empty list', () {
        final list = <String>[];
        final result = StringUtils.joinList(list);
        
        expect(result, equals(''));
      });

      test('should handle null list', () {
        final result = StringUtils.joinList(null);
        
        expect(result, equals(''));
      });

      test('should handle list with null values', () {
        final list = ['apple', null, 'cherry'];
        final result = StringUtils.joinList(list);
        
        expect(result, equals('apple, , cherry'));
      });

      test('should skip empty values when skipEmpty is true', () {
        final list = ['apple', '', 'cherry', null];
        final result = StringUtils.joinList(list, skipEmpty: true);
        
        expect(result, equals('apple, cherry'));
      });

      test('should include empty values when skipEmpty is false', () {
        final list = ['apple', '', 'cherry'];
        final result = StringUtils.joinList(list, skipEmpty: false);
        
        expect(result, equals('apple, , cherry'));
      });

      test('should return default value for null input', () {
        final result = StringUtils.joinList(null, defaultValue: 'default');
        
        expect(result, equals('default'));
      });

      test('should handle non-list input gracefully', () {
        final result = StringUtils.joinList('not a list');
        
        expect(result, equals(''));
      });

      test('should handle single item list', () {
        final list = ['single'];
        final result = StringUtils.joinList(list);
        
        expect(result, equals('single'));
      });
    });

    group('joinWithLimit', () {
      test('should join list within length limit', () {
        final list = ['apple', 'banana'];
        final result = StringUtils.joinWithLimit(list, maxLength: 20);
        
        expect(result, equals('apple, banana'));
        expect(result.length, lessThanOrEqualTo(20));
      });

      test('should truncate when exceeding length limit', () {
        final list = ['apple', 'banana', 'cherry', 'date', 'elderberry'];
        final result = StringUtils.joinWithLimit(list, maxLength: 15);
        
        expect(result, equals('apple, banana, ...'));
        expect(result.length, equals(18)); // 15 + 3 for '...'
      });

      test('should use custom overflow indicator', () {
        final list = ['apple', 'banana', 'cherry'];
        final result = StringUtils.joinWithLimit(
          list,
          maxLength: 10,
          overflow: ' [more]',
        );
        
        expect(result, equals('apple, ban [more]'));
      });

      test('should handle empty list', () {
        final result = StringUtils.joinWithLimit([], maxLength: 10);
        
        expect(result, equals(''));
      });

      test('should handle null list', () {
        final result = StringUtils.joinWithLimit(null, maxLength: 10);
        
        expect(result, equals(''));
      });

      test('should return default value for null input', () {
        final result = StringUtils.joinWithLimit(
          null,
          maxLength: 10,
          defaultValue: 'default',
        );
        
        expect(result, equals('default'));
      });

      test('should handle very small maxLength', () {
        final list = ['apple'];
        final result = StringUtils.joinWithLimit(list, maxLength: 3);
        
        expect(result, equals('app...'));
      });
    });

    group('getListItemSafe', () {
      test('should return item at valid index', () {
        final list = ['apple', 'banana', 'cherry'];
        final result = StringUtils.getListItemSafe(list, 1);
        
        expect(result, equals('banana'));
      });

      test('should return default value for invalid index', () {
        final list = ['apple', 'banana'];
        final result = StringUtils.getListItemSafe(list, 5);
        
        expect(result, equals(''));
      });

      test('should return default value for negative index', () {
        final list = ['apple', 'banana'];
        final result = StringUtils.getListItemSafe(list, -1);
        
        expect(result, equals(''));
      });

      test('should return default value for null list', () {
        final result = StringUtils.getListItemSafe(null, 0);
        
        expect(result, equals(''));
      });

      test('should return custom default value', () {
        final list = ['apple'];
        final result = StringUtils.getListItemSafe(
          list,
          5,
          defaultValue: 'not found',
        );
        
        expect(result, equals('not found'));
      });

      test('should handle non-list input', () {
        final result = StringUtils.getListItemSafe('not a list', 0);
        
        expect(result, equals(''));
      });

      test('should convert non-string values to string', () {
        final list = [123, true, 45.67];
        final result1 = StringUtils.getListItemSafe(list, 0);
        final result2 = StringUtils.getListItemSafe(list, 1);
        final result3 = StringUtils.getListItemSafe(list, 2);
        
        expect(result1, equals('123'));
        expect(result2, equals('true'));
        expect(result3, equals('45.67'));
      });

      test('should handle null values in list', () {
        final list = ['apple', null, 'cherry'];
        final result = StringUtils.getListItemSafe(list, 1);
        
        expect(result, equals(''));
      });
    });

    group('formatDisplay', () {
      test('should format string value with prefix and suffix', () {
        final result = StringUtils.formatDisplay(
          'test',
          prefix: 'Pre: ',
          suffix: ' :Post',
        );
        
        expect(result, equals('Pre: test :Post'));
      });

      test('should format number value', () {
        final result = StringUtils.formatDisplay(
          123,
          prefix: 'Number: ',
        );
        
        expect(result, equals('Number: 123'));
      });

      test('should format boolean value', () {
        final result = StringUtils.formatDisplay(
          true,
          suffix: ' (boolean)',
        );
        
        expect(result, equals('true (boolean)'));
      });

      test('should return default value for null input', () {
        final result = StringUtils.formatDisplay(
          null,
          defaultValue: 'N/A',
        );
        
        expect(result, equals('N/A'));
      });

      test('should handle empty string when skipIfEmpty is true', () {
        final result = StringUtils.formatDisplay(
          '',
          prefix: 'Pre: ',
          skipIfEmpty: true,
          defaultValue: 'empty',
        );
        
        expect(result, equals('empty'));
      });

      test('should format empty string when skipIfEmpty is false', () {
        final result = StringUtils.formatDisplay(
          '',
          prefix: 'Pre: ',
          suffix: ' :Post',
          skipIfEmpty: false,
        );
        
        expect(result, equals('Pre:  :Post'));
      });

      test('should handle only prefix', () {
        final result = StringUtils.formatDisplay(
          'test',
          prefix: 'Prefix: ',
        );
        
        expect(result, equals('Prefix: test'));
      });

      test('should handle only suffix', () {
        final result = StringUtils.formatDisplay(
          'test',
          suffix: ' suffix',
        );
        
        expect(result, equals('test suffix'));
      });

      test('should handle no prefix or suffix', () {
        final result = StringUtils.formatDisplay('test');
        
        expect(result, equals('test'));
      });
    });

    group('Error Handling', () {
      test('should handle exceptions gracefully in joinList', () {
        // 测试异常情况下的处理
        final result = StringUtils.joinList({'not': 'a list'});
        
        expect(result, equals(''));
      });

      test('should handle exceptions gracefully in joinWithLimit', () {
        final result = StringUtils.joinWithLimit({'not': 'a list'}, maxLength: 10);
        
        expect(result, equals(''));
      });
    });
  });
}
