import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_smarthome/core/utils/network_image_helper.dart';
import '../../helpers/test_helpers.dart';

void main() {
  group('NetworkImageHelper Tests', () {
    late NetworkImageHelper helper;

    setUp(() {
      helper = NetworkImageHelper();
    });

    group('Singleton Pattern', () {
      test('should return same instance', () {
        final instance1 = NetworkImageHelper();
        final instance2 = NetworkImageHelper();
        
        expect(instance1, same(instance2));
      });
    });

    group('URL Validation', () {
      testWidgets('should handle valid HTTP URLs', (WidgetTester tester) async {
        const validUrl = 'http://example.com/image.jpg';
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(imageUrl: validUrl),
        );
        
        await tester.pumpWidget(widget);
        
        // 验证widget创建成功
        expect(find.byType(Widget), findsWidgets);
      });

      testWidgets('should handle valid HTTPS URLs', (WidgetTester tester) async {
        const validUrl = 'https://example.com/image.jpg';
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(imageUrl: validUrl),
        );
        
        await tester.pumpWidget(widget);
        
        expect(find.byType(Widget), findsWidgets);
      });

      testWidgets('should handle invalid URLs gracefully', (WidgetTester tester) async {
        const invalidUrl = 'not-a-valid-url';
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(imageUrl: invalidUrl),
        );
        
        await tester.pumpWidget(widget);
        
        // 应该显示默认图片或错误widget
        expect(find.byType(Widget), findsWidgets);
      });

      testWidgets('should handle empty URLs', (WidgetTester tester) async {
        const emptyUrl = '';
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(imageUrl: emptyUrl),
        );
        
        await tester.pumpWidget(widget);
        
        expect(find.byType(Widget), findsWidgets);
      });

      testWidgets('should handle null URLs', (WidgetTester tester) async {
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(imageUrl: ''),
        );
        
        await tester.pumpWidget(widget);
        
        expect(find.byType(Widget), findsWidgets);
      });
    });

    group('Image Widget Creation', () {
      testWidgets('should create widget with specified dimensions', (WidgetTester tester) async {
        const imageUrl = 'https://example.com/image.jpg';
        const width = 100.0;
        const height = 150.0;
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(
            imageUrl: imageUrl,
            width: width,
            height: height,
          ),
        );
        
        await tester.pumpWidget(widget);
        
        expect(find.byType(Widget), findsWidgets);
      });

      testWidgets('should create widget with specified fit', (WidgetTester tester) async {
        const imageUrl = 'https://example.com/image.jpg';
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(
            imageUrl: imageUrl,
            fit: BoxFit.contain,
          ),
        );
        
        await tester.pumpWidget(widget);
        
        expect(find.byType(Widget), findsWidgets);
      });

      testWidgets('should create widget with custom default image', (WidgetTester tester) async {
        const imageUrl = 'https://example.com/image.jpg';
        const customDefaultImage = 'assets/images/custom_default.png';
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(
            imageUrl: imageUrl,
            defaultImageAsset: customDefaultImage,
          ),
        );
        
        await tester.pumpWidget(widget);
        
        expect(find.byType(Widget), findsWidgets);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle network errors gracefully', (WidgetTester tester) async {
        const invalidUrl = 'https://nonexistent.example.com/image.jpg';
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(imageUrl: invalidUrl),
        );
        
        await tester.pumpWidget(widget);
        
        // 应该显示错误widget或默认图片
        expect(find.byType(Widget), findsWidgets);
      });

      testWidgets('should handle malformed URLs', (WidgetTester tester) async {
        const malformedUrl = 'http://[invalid-url]';
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(imageUrl: malformedUrl),
        );
        
        await tester.pumpWidget(widget);
        
        expect(find.byType(Widget), findsWidgets);
      });

      testWidgets('should handle very long URLs', (WidgetTester tester) async {
        final longUrl = 'https://example.com/${'very-long-path/' * 100}image.jpg';
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(imageUrl: longUrl),
        );
        
        await tester.pumpWidget(widget);
        
        expect(find.byType(Widget), findsWidgets);
      });
    });

    group('Custom Widgets', () {
      testWidgets('should accept custom placeholder widget', (WidgetTester tester) async {
        const imageUrl = 'https://example.com/image.jpg';
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(
            imageUrl: imageUrl,
            placeholder: (context, url) => const CircularProgressIndicator(),
          ),
        );
        
        await tester.pumpWidget(widget);
        
        expect(find.byType(Widget), findsWidgets);
      });

      testWidgets('should accept custom error widget', (WidgetTester tester) async {
        const imageUrl = 'https://example.com/image.jpg';
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(
            imageUrl: imageUrl,
            errorWidget: (context, url, error) => const Icon(Icons.error),
          ),
        );
        
        await tester.pumpWidget(widget);
        
        expect(find.byType(Widget), findsWidgets);
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle URLs with special characters', (WidgetTester tester) async {
        const specialUrl = 'https://example.com/image with spaces & symbols!@#.jpg';
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(imageUrl: specialUrl),
        );
        
        await tester.pumpWidget(widget);
        
        expect(find.byType(Widget), findsWidgets);
      });

      testWidgets('should handle URLs with query parameters', (WidgetTester tester) async {
        const urlWithParams = 'https://example.com/image.jpg?width=100&height=200&format=webp';
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(imageUrl: urlWithParams),
        );
        
        await tester.pumpWidget(widget);
        
        expect(find.byType(Widget), findsWidgets);
      });

      testWidgets('should handle URLs with fragments', (WidgetTester tester) async {
        const urlWithFragment = 'https://example.com/image.jpg#section1';
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(imageUrl: urlWithFragment),
        );
        
        await tester.pumpWidget(widget);
        
        expect(find.byType(Widget), findsWidgets);
      });

      testWidgets('should handle zero dimensions', (WidgetTester tester) async {
        const imageUrl = 'https://example.com/image.jpg';
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(
            imageUrl: imageUrl,
            width: 0,
            height: 0,
          ),
        );
        
        await tester.pumpWidget(widget);
        
        expect(find.byType(Widget), findsWidgets);
      });

      testWidgets('should handle negative dimensions', (WidgetTester tester) async {
        const imageUrl = 'https://example.com/image.jpg';
        
        final widget = TestHelpers.createTestWidget(
          helper.getCachedNetworkImage(
            imageUrl: imageUrl,
            width: -10,
            height: -20,
          ),
        );
        
        await tester.pumpWidget(widget);
        
        expect(find.byType(Widget), findsWidgets);
      });
    });

    group('Different Image Formats', () {
      final imageFormats = [
        'https://example.com/image.jpg',
        'https://example.com/image.jpeg',
        'https://example.com/image.png',
        'https://example.com/image.gif',
        'https://example.com/image.webp',
        'https://example.com/image.svg',
        'https://example.com/image.bmp',
      ];

      for (final imageUrl in imageFormats) {
        testWidgets('should handle ${imageUrl.split('.').last} format', (WidgetTester tester) async {
          final widget = TestHelpers.createTestWidget(
            helper.getCachedNetworkImage(imageUrl: imageUrl),
          );
          
          await tester.pumpWidget(widget);
          
          expect(find.byType(Widget), findsWidgets);
        });
      }
    });

    group('Performance', () {
      testWidgets('should handle multiple images efficiently', (WidgetTester tester) async {
        final imageUrls = List.generate(
          10,
          (index) => 'https://example.com/image$index.jpg',
        );
        
        final widgets = imageUrls.map(
          (url) => helper.getCachedNetworkImage(imageUrl: url),
        ).toList();
        
        final widget = TestHelpers.createTestWidget(
          Column(children: widgets),
        );
        
        await tester.pumpWidget(widget);
        
        expect(find.byType(Widget), findsWidgets);
      });

      testWidgets('should handle rapid widget creation', (WidgetTester tester) async {
        const imageUrl = 'https://example.com/image.jpg';
        
        // 快速创建多个相同的图片widget
        for (int i = 0; i < 50; i++) {
          final widget = TestHelpers.createTestWidget(
            helper.getCachedNetworkImage(imageUrl: imageUrl),
          );
          
          await tester.pumpWidget(widget);
        }
        
        expect(find.byType(Widget), findsWidgets);
      });
    });
  });
}
