// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		08057DBF2CED802B007C6977 /* libsqlite3.0.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 08057DBE2CED802B007C6977 /* libsqlite3.0.tbd */; };
		08057DC12CED81A0007C6977 /* libsqlite3.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 08057DC02CED819F007C6977 /* libsqlite3.tbd */; };
		08057DC42CED8896007C6977 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 08057DC32CED8896007C6977 /* GeneratedPluginRegistrant.m */; };
		08057DC72CED8AF6007C6977 /* FLNativeViewFactory.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DC52CED8AF6007C6977 /* FLNativeViewFactory.swift */; };
		08057E2C2CEDA9EE007C6977 /* SmartDeviceSegmentListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DE52CEDA9EE007C6977 /* SmartDeviceSegmentListCell.swift */; };
		08057E2D2CEDA9EE007C6977 /* SearchHistoryManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DF92CEDA9EE007C6977 /* SearchHistoryManager.swift */; };
		08057E332CEDA9EE007C6977 /* BaseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DC92CEDA9EE007C6977 /* BaseViewController.swift */; };
		08057E372CEDA9EE007C6977 /* FurnishViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DF72CEDA9EE007C6977 /* FurnishViewModel.swift */; };
		08057E3A2CEDA9EE007C6977 /* CommonParametersPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DFF2CEDA9EE007C6977 /* CommonParametersPlugin.swift */; };
		08057E3D2CEDA9EE007C6977 /* LoadingHUD.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057E252CEDA9EE007C6977 /* LoadingHUD.swift */; };
		08057E3E2CEDA9EE007C6977 /* SmartDeviceReusableView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DE42CEDA9EE007C6977 /* SmartDeviceReusableView.swift */; };
		08057E442CEDA9EE007C6977 /* Extension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DCC2CEDA9EE007C6977 /* Extension.swift */; };
		08057E4D2CEDA9EE007C6977 /* UserManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DFA2CEDA9EE007C6977 /* UserManager.swift */; };
		08057E4E2CEDA9EE007C6977 /* HorizontalScrollingButtonsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057E242CEDA9EE007C6977 /* HorizontalScrollingButtonsView.swift */; };
		08057E522CEDA9EE007C6977 /* SmartDeviceHomeController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DF32CEDA9EE007C6977 /* SmartDeviceHomeController.swift */; };
		08057E542CEDA9EE007C6977 /* CacheManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DF82CEDA9EE007C6977 /* CacheManager.swift */; };
		08057E572CEDA9EE007C6977 /* SmartDeviceListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DE32CEDA9EE007C6977 /* SmartDeviceListCell.swift */; };
		08057E5D2CEDA9EE007C6977 /* APIService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DFD2CEDA9EE007C6977 /* APIService.swift */; };
		08057E622CEDA9EE007C6977 /* NetworkMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057E012CEDA9EE007C6977 /* NetworkMonitor.swift */; };
		08057E652CEDA9EE007C6977 /* ProgressHUDManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057E292CEDA9EE007C6977 /* ProgressHUDManager.swift */; };
		08057E682CEDA9EE007C6977 /* ViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057E022CEDA9EE007C6977 /* ViewModel.swift */; };
		08057E6A2CEDA9EE007C6977 /* CommonModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DFE2CEDA9EE007C6977 /* CommonModel.swift */; };
		08057E6B2CEDA9EE007C6977 /* DebuggingDecoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DCB2CEDA9EE007C6977 /* DebuggingDecoder.swift */; };
		08057E6D2CEDA9EE007C6977 /* SmartDeviceListController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DF42CEDA9EE007C6977 /* SmartDeviceListController.swift */; };
		08057E6E2CEDA9EE007C6977 /* SmartDeviceInspirationListCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DE22CEDA9EE007C6977 /* SmartDeviceInspirationListCell.swift */; };
		08057E702CEDA9EE007C6977 /* BaseWebController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DCA2CEDA9EE007C6977 /* BaseWebController.swift */; };
		08057E722CEDA9EE007C6977 /* SmartDeviceHomeHeaderCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DE12CEDA9EE007C6977 /* SmartDeviceHomeHeaderCell.swift */; };
		08057E732CEDA9EE007C6977 /* NetworkLoggerPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057E002CEDA9EE007C6977 /* NetworkLoggerPlugin.swift */; };
		08057E7C2CEDA9EE007C6977 /* SmartDeviceSegmentController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DF52CEDA9EE007C6977 /* SmartDeviceSegmentController.swift */; };
		08057E7E2CEDA9EE007C6977 /* UserModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DFB2CEDA9EE007C6977 /* UserModel.swift */; };
		08057E7F2CEDA9EE007C6977 /* Screen.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08057DCD2CEDA9EE007C6977 /* Screen.swift */; };
		081458822DA90B09001B0974 /* thing_custom_config.json in Resources */ = {isa = PBXBuildFile; fileRef = 081458812DA90B09001B0974 /* thing_custom_config.json */; };
		08169D4A2DB238B900B316F8 /* deviceDetailConfig.json in Resources */ = {isa = PBXBuildFile; fileRef = 08169D492DB238B900B316F8 /* deviceDetailConfig.json */; };
		08B03E142D5463FC00CF2B7B /* QuoteSceneWebViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08B03E132D5463FC00CF2B7B /* QuoteSceneWebViewController.swift */; };
		08B55F9E2D587EC900455258 /* SmartHomeFamilysController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 08B55F9D2D587EC900455258 /* SmartHomeFamilysController.swift */; };
		3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 74858FAE1ED2DC5600515810 /* AppDelegate.swift */; };
		957EDD4F55099D27B19F527E /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = B94A2938C3FC082A05A8C034 /* GoogleService-Info.plist */; };
		97C146FC1CF9000F007C117D /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		CD71E04B430DD1C222A28DF6 /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0971E948B915590730312431 /* Pods_Runner.framework */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		9705A1C41CF9048500538489 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		08057DBC2CED7FD6007C6977 /* SQLCipher.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; path = SQLCipher.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		08057DBE2CED802B007C6977 /* libsqlite3.0.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.0.tbd; path = usr/lib/libsqlite3.0.tbd; sourceTree = SDKROOT; };
		08057DC02CED819F007C6977 /* libsqlite3.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.tbd; path = usr/lib/libsqlite3.tbd; sourceTree = SDKROOT; };
		08057DC22CED8896007C6977 /* GeneratedPluginRegistrant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeneratedPluginRegistrant.h; sourceTree = "<group>"; };
		08057DC32CED8896007C6977 /* GeneratedPluginRegistrant.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = GeneratedPluginRegistrant.m; sourceTree = "<group>"; };
		08057DC52CED8AF6007C6977 /* FLNativeViewFactory.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FLNativeViewFactory.swift; sourceTree = "<group>"; };
		08057DC92CEDA9EE007C6977 /* BaseViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseViewController.swift; sourceTree = "<group>"; };
		08057DCA2CEDA9EE007C6977 /* BaseWebController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseWebController.swift; sourceTree = "<group>"; };
		08057DCB2CEDA9EE007C6977 /* DebuggingDecoder.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DebuggingDecoder.swift; sourceTree = "<group>"; };
		08057DCC2CEDA9EE007C6977 /* Extension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Extension.swift; sourceTree = "<group>"; };
		08057DCD2CEDA9EE007C6977 /* Screen.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Screen.swift; sourceTree = "<group>"; };
		08057DE12CEDA9EE007C6977 /* SmartDeviceHomeHeaderCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartDeviceHomeHeaderCell.swift; sourceTree = "<group>"; };
		08057DE22CEDA9EE007C6977 /* SmartDeviceInspirationListCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartDeviceInspirationListCell.swift; sourceTree = "<group>"; };
		08057DE32CEDA9EE007C6977 /* SmartDeviceListCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartDeviceListCell.swift; sourceTree = "<group>"; };
		08057DE42CEDA9EE007C6977 /* SmartDeviceReusableView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartDeviceReusableView.swift; sourceTree = "<group>"; };
		08057DE52CEDA9EE007C6977 /* SmartDeviceSegmentListCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartDeviceSegmentListCell.swift; sourceTree = "<group>"; };
		08057DF32CEDA9EE007C6977 /* SmartDeviceHomeController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartDeviceHomeController.swift; sourceTree = "<group>"; };
		08057DF42CEDA9EE007C6977 /* SmartDeviceListController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartDeviceListController.swift; sourceTree = "<group>"; };
		08057DF52CEDA9EE007C6977 /* SmartDeviceSegmentController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartDeviceSegmentController.swift; sourceTree = "<group>"; };
		08057DF72CEDA9EE007C6977 /* FurnishViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FurnishViewModel.swift; sourceTree = "<group>"; };
		08057DF82CEDA9EE007C6977 /* CacheManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CacheManager.swift; sourceTree = "<group>"; };
		08057DF92CEDA9EE007C6977 /* SearchHistoryManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchHistoryManager.swift; sourceTree = "<group>"; };
		08057DFA2CEDA9EE007C6977 /* UserManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserManager.swift; sourceTree = "<group>"; };
		08057DFB2CEDA9EE007C6977 /* UserModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserModel.swift; sourceTree = "<group>"; };
		08057DFD2CEDA9EE007C6977 /* APIService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIService.swift; sourceTree = "<group>"; };
		08057DFE2CEDA9EE007C6977 /* CommonModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommonModel.swift; sourceTree = "<group>"; };
		08057DFF2CEDA9EE007C6977 /* CommonParametersPlugin.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CommonParametersPlugin.swift; sourceTree = "<group>"; };
		08057E002CEDA9EE007C6977 /* NetworkLoggerPlugin.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkLoggerPlugin.swift; sourceTree = "<group>"; };
		08057E012CEDA9EE007C6977 /* NetworkMonitor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkMonitor.swift; sourceTree = "<group>"; };
		08057E022CEDA9EE007C6977 /* ViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewModel.swift; sourceTree = "<group>"; };
		08057E242CEDA9EE007C6977 /* HorizontalScrollingButtonsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HorizontalScrollingButtonsView.swift; sourceTree = "<group>"; };
		08057E252CEDA9EE007C6977 /* LoadingHUD.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoadingHUD.swift; sourceTree = "<group>"; };
		08057E292CEDA9EE007C6977 /* ProgressHUDManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProgressHUDManager.swift; sourceTree = "<group>"; };
		081458812DA90B09001B0974 /* thing_custom_config.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = thing_custom_config.json; sourceTree = "<group>"; };
		08169D492DB238B900B316F8 /* deviceDetailConfig.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = deviceDetailConfig.json; sourceTree = "<group>"; };
		083D9C0B2D12AC750003FDA7 /* Runner.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Runner.entitlements; sourceTree = "<group>"; };
		08B03E132D5463FC00CF2B7B /* QuoteSceneWebViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuoteSceneWebViewController.swift; sourceTree = "<group>"; };
		08B55F9D2D587EC900455258 /* SmartHomeFamilysController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SmartHomeFamilysController.swift; sourceTree = "<group>"; };
		0971E948B915590730312431 /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1C8DB9BAA7DEC98A568941B9 /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
		23211595F262BC9CE605858E /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = AppFrameworkInfo.plist; path = Flutter/AppFrameworkInfo.plist; sourceTree = "<group>"; };
		4345EFE34033073EDC2C6C81 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Runner-Bridging-Header.h"; sourceTree = "<group>"; };
		74858FAE1ED2DC5600515810 /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		7AFA3C8E1D35360C0083082E /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Release.xcconfig; path = Flutter/Release.xcconfig; sourceTree = "<group>"; };
		9740EEB21CF90195004384FC /* Debug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Debug.xcconfig; path = Flutter/Debug.xcconfig; sourceTree = "<group>"; };
		9740EEB31CF90195004384FC /* Generated.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Generated.xcconfig; path = Flutter/Generated.xcconfig; sourceTree = "<group>"; };
		97C146EE1CF9000F007C117D /* Runner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Runner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97C146FB1CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		97C146FD1CF9000F007C117D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97C147001CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		97C147021CF9000F007C117D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B94A2938C3FC082A05A8C034 /* GoogleService-Info.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "Runner/GoogleService-Info.plist"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		97C146EB1CF9000F007C117D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				08057DC12CED81A0007C6977 /* libsqlite3.tbd in Frameworks */,
				08057DBF2CED802B007C6977 /* libsqlite3.0.tbd in Frameworks */,
				CD71E04B430DD1C222A28DF6 /* Pods_Runner.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		08057DCE2CEDA9EE007C6977 /* Extension */ = {
			isa = PBXGroup;
			children = (
				08057DCC2CEDA9EE007C6977 /* Extension.swift */,
				08057DCD2CEDA9EE007C6977 /* Screen.swift */,
			);
			path = Extension;
			sourceTree = "<group>";
		};
		08057DE82CEDA9EE007C6977 /* View */ = {
			isa = PBXGroup;
			children = (
				08057DE12CEDA9EE007C6977 /* SmartDeviceHomeHeaderCell.swift */,
				08057DE22CEDA9EE007C6977 /* SmartDeviceInspirationListCell.swift */,
				08057DE32CEDA9EE007C6977 /* SmartDeviceListCell.swift */,
				08057DE42CEDA9EE007C6977 /* SmartDeviceReusableView.swift */,
				08057DE52CEDA9EE007C6977 /* SmartDeviceSegmentListCell.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		08057DF62CEDA9EE007C6977 /* Family */ = {
			isa = PBXGroup;
			children = (
				08057DE82CEDA9EE007C6977 /* View */,
				08057DF32CEDA9EE007C6977 /* SmartDeviceHomeController.swift */,
				08B55F9D2D587EC900455258 /* SmartHomeFamilysController.swift */,
				08057DF42CEDA9EE007C6977 /* SmartDeviceListController.swift */,
				08057DF52CEDA9EE007C6977 /* SmartDeviceSegmentController.swift */,
			);
			path = Family;
			sourceTree = "<group>";
		};
		08057DFC2CEDA9EE007C6977 /* Manager */ = {
			isa = PBXGroup;
			children = (
				08057DF82CEDA9EE007C6977 /* CacheManager.swift */,
				08057DF92CEDA9EE007C6977 /* SearchHistoryManager.swift */,
				08057DFA2CEDA9EE007C6977 /* UserManager.swift */,
				08057DFB2CEDA9EE007C6977 /* UserModel.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		08057E032CEDA9EE007C6977 /* Networking */ = {
			isa = PBXGroup;
			children = (
				08057DFD2CEDA9EE007C6977 /* APIService.swift */,
				08057DFE2CEDA9EE007C6977 /* CommonModel.swift */,
				08057DFF2CEDA9EE007C6977 /* CommonParametersPlugin.swift */,
				08057E002CEDA9EE007C6977 /* NetworkLoggerPlugin.swift */,
				08057E012CEDA9EE007C6977 /* NetworkMonitor.swift */,
				08057E022CEDA9EE007C6977 /* ViewModel.swift */,
			);
			path = Networking;
			sourceTree = "<group>";
		};
		08057E2B2CEDA9EE007C6977 /* UIComponents */ = {
			isa = PBXGroup;
			children = (
				08057E242CEDA9EE007C6977 /* HorizontalScrollingButtonsView.swift */,
				08057E252CEDA9EE007C6977 /* LoadingHUD.swift */,
				08057E292CEDA9EE007C6977 /* ProgressHUDManager.swift */,
			);
			path = UIComponents;
			sourceTree = "<group>";
		};
		825E94D7A0C06751A2DE6945 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				08057DC02CED819F007C6977 /* libsqlite3.tbd */,
				08057DBE2CED802B007C6977 /* libsqlite3.0.tbd */,
				08057DBC2CED7FD6007C6977 /* SQLCipher.framework */,
				0971E948B915590730312431 /* Pods_Runner.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		9740EEB11CF90186004384FC /* Flutter */ = {
			isa = PBXGroup;
			children = (
				3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */,
				9740EEB21CF90195004384FC /* Debug.xcconfig */,
				7AFA3C8E1D35360C0083082E /* Release.xcconfig */,
				9740EEB31CF90195004384FC /* Generated.xcconfig */,
			);
			name = Flutter;
			sourceTree = "<group>";
		};
		97C146E51CF9000F007C117D = {
			isa = PBXGroup;
			children = (
				9740EEB11CF90186004384FC /* Flutter */,
				97C146F01CF9000F007C117D /* Runner */,
				97C146EF1CF9000F007C117D /* Products */,
				BDE998F25FB99B56CA2568D5 /* Pods */,
				825E94D7A0C06751A2DE6945 /* Frameworks */,
				B94A2938C3FC082A05A8C034 /* GoogleService-Info.plist */,
			);
			sourceTree = "<group>";
		};
		97C146EF1CF9000F007C117D /* Products */ = {
			isa = PBXGroup;
			children = (
				97C146EE1CF9000F007C117D /* Runner.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97C146F01CF9000F007C117D /* Runner */ = {
			isa = PBXGroup;
			children = (
				083D9C0B2D12AC750003FDA7 /* Runner.entitlements */,
				97C146FA1CF9000F007C117D /* Main.storyboard */,
				97C146FD1CF9000F007C117D /* Assets.xcassets */,
				97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */,
				97C147021CF9000F007C117D /* Info.plist */,
				081458812DA90B09001B0974 /* thing_custom_config.json */,
				08169D492DB238B900B316F8 /* deviceDetailConfig.json */,
				74858FAE1ED2DC5600515810 /* AppDelegate.swift */,
				08057DC92CEDA9EE007C6977 /* BaseViewController.swift */,
				08057DCA2CEDA9EE007C6977 /* BaseWebController.swift */,
				08057DCB2CEDA9EE007C6977 /* DebuggingDecoder.swift */,
				08B03E132D5463FC00CF2B7B /* QuoteSceneWebViewController.swift */,
				08057DCE2CEDA9EE007C6977 /* Extension */,
				08057DF62CEDA9EE007C6977 /* Family */,
				08057DF72CEDA9EE007C6977 /* FurnishViewModel.swift */,
				08057DFC2CEDA9EE007C6977 /* Manager */,
				08057E032CEDA9EE007C6977 /* Networking */,
				08057E2B2CEDA9EE007C6977 /* UIComponents */,
				08057DC52CED8AF6007C6977 /* FLNativeViewFactory.swift */,
				08057DC22CED8896007C6977 /* GeneratedPluginRegistrant.h */,
				08057DC32CED8896007C6977 /* GeneratedPluginRegistrant.m */,
				74858FAD1ED2DC5600515810 /* Runner-Bridging-Header.h */,
			);
			path = Runner;
			sourceTree = "<group>";
		};
		BDE998F25FB99B56CA2568D5 /* Pods */ = {
			isa = PBXGroup;
			children = (
				4345EFE34033073EDC2C6C81 /* Pods-Runner.debug.xcconfig */,
				1C8DB9BAA7DEC98A568941B9 /* Pods-Runner.release.xcconfig */,
				23211595F262BC9CE605858E /* Pods-Runner.profile.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		97C146ED1CF9000F007C117D /* Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */;
			buildPhases = (
				3E714780C8D6E7B513D07F75 /* [CP] Check Pods Manifest.lock */,
				9740EEB61CF901F6004384FC /* Run Script */,
				97C146EA1CF9000F007C117D /* Sources */,
				97C146EB1CF9000F007C117D /* Frameworks */,
				97C146EC1CF9000F007C117D /* Resources */,
				9705A1C41CF9048500538489 /* Embed Frameworks */,
				3B06AD1E1E4923F5004D2608 /* Thin Binary */,
				1E52B7F0BBFB9AFF10647B6F /* [CP] Embed Pods Frameworks */,
				BC16183611E909E1B6670A1E /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Runner;
			productName = Runner;
			productReference = 97C146EE1CF9000F007C117D /* Runner.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97C146E61CF9000F007C117D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1510;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					97C146ED1CF9000F007C117D = {
						CreatedOnToolsVersion = 7.3.1;
						LastSwiftMigration = 1100;
					};
				};
			};
			buildConfigurationList = 97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 97C146E51CF9000F007C117D;
			productRefGroup = 97C146EF1CF9000F007C117D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97C146ED1CF9000F007C117D /* Runner */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		97C146EC1CF9000F007C117D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */,
				3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */,
				97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */,
				97C146FC1CF9000F007C117D /* Main.storyboard in Resources */,
				08169D4A2DB238B900B316F8 /* deviceDetailConfig.json in Resources */,
				081458822DA90B09001B0974 /* thing_custom_config.json in Resources */,
				957EDD4F55099D27B19F527E /* GoogleService-Info.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		1E52B7F0BBFB9AFF10647B6F /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		3B06AD1E1E4923F5004D2608 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin";
		};
		3E714780C8D6E7B513D07F75 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9740EEB61CF901F6004384FC /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build";
		};
		BC16183611E909E1B6670A1E /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		97C146EA1CF9000F007C117D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				74858FAF1ED2DC5600515810 /* AppDelegate.swift in Sources */,
				08057E2C2CEDA9EE007C6977 /* SmartDeviceSegmentListCell.swift in Sources */,
				08057E2D2CEDA9EE007C6977 /* SearchHistoryManager.swift in Sources */,
				08B03E142D5463FC00CF2B7B /* QuoteSceneWebViewController.swift in Sources */,
				08057E332CEDA9EE007C6977 /* BaseViewController.swift in Sources */,
				08057E372CEDA9EE007C6977 /* FurnishViewModel.swift in Sources */,
				08057E3A2CEDA9EE007C6977 /* CommonParametersPlugin.swift in Sources */,
				08057E3D2CEDA9EE007C6977 /* LoadingHUD.swift in Sources */,
				08057E3E2CEDA9EE007C6977 /* SmartDeviceReusableView.swift in Sources */,
				08B55F9E2D587EC900455258 /* SmartHomeFamilysController.swift in Sources */,
				08057E442CEDA9EE007C6977 /* Extension.swift in Sources */,
				08057E4D2CEDA9EE007C6977 /* UserManager.swift in Sources */,
				08057E4E2CEDA9EE007C6977 /* HorizontalScrollingButtonsView.swift in Sources */,
				08057E522CEDA9EE007C6977 /* SmartDeviceHomeController.swift in Sources */,
				08057E542CEDA9EE007C6977 /* CacheManager.swift in Sources */,
				08057E572CEDA9EE007C6977 /* SmartDeviceListCell.swift in Sources */,
				08057E5D2CEDA9EE007C6977 /* APIService.swift in Sources */,
				08057E622CEDA9EE007C6977 /* NetworkMonitor.swift in Sources */,
				08057E652CEDA9EE007C6977 /* ProgressHUDManager.swift in Sources */,
				08057E682CEDA9EE007C6977 /* ViewModel.swift in Sources */,
				08057E6A2CEDA9EE007C6977 /* CommonModel.swift in Sources */,
				08057E6B2CEDA9EE007C6977 /* DebuggingDecoder.swift in Sources */,
				08057E6D2CEDA9EE007C6977 /* SmartDeviceListController.swift in Sources */,
				08057E6E2CEDA9EE007C6977 /* SmartDeviceInspirationListCell.swift in Sources */,
				08057E702CEDA9EE007C6977 /* BaseWebController.swift in Sources */,
				08057E722CEDA9EE007C6977 /* SmartDeviceHomeHeaderCell.swift in Sources */,
				08057E732CEDA9EE007C6977 /* NetworkLoggerPlugin.swift in Sources */,
				08057E7C2CEDA9EE007C6977 /* SmartDeviceSegmentController.swift in Sources */,
				08057E7E2CEDA9EE007C6977 /* UserModel.swift in Sources */,
				08057E7F2CEDA9EE007C6977 /* Screen.swift in Sources */,
				08057DC72CED8AF6007C6977 /* FLNativeViewFactory.swift in Sources */,
				08057DC42CED8896007C6977 /* GeneratedPluginRegistrant.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		97C146FA1CF9000F007C117D /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C146FB1CF9000F007C117D /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C147001CF9000F007C117D /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		249021D3217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		249021D4217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CURRENT_PROJECT_VERSION = 8;
				DEVELOPMENT_TEAM = VM8EAMJRY8;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "极家汇";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jiyoujiaju.jijiahui;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		97C147031CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		97C147041CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		97C147061CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CURRENT_PROJECT_VERSION = 8;
				DEVELOPMENT_TEAM = VM8EAMJRY8;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "极家汇";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jiyoujiaju.jijiahui;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		97C147071CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				CURRENT_PROJECT_VERSION = 8;
				DEVELOPMENT_TEAM = VM8EAMJRY8;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = Runner/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "极家汇";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 3.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.jiyoujiaju.jijiahui;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147031CF9000F007C117D /* Debug */,
				97C147041CF9000F007C117D /* Release */,
				249021D3217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147061CF9000F007C117D /* Debug */,
				97C147071CF9000F007C117D /* Release */,
				249021D4217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 97C146E61CF9000F007C117D /* Project object */;
}
