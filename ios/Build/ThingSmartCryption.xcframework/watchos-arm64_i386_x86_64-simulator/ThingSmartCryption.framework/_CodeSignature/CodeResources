<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/ThingSmartCryption-watchOS-umbrella.h</key>
		<data>
		ca7wIbyHx6qkxDPYdn2N5ydrFsI=
		</data>
		<key>Headers/error_code.h</key>
		<data>
		e0l9pwTOC0iREgFq6DZdBS+Dxd0=
		</data>
		<key>Info.plist</key>
		<data>
		mXRZWoa8/ymHRg7NmxrhgwDmlPs=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		sS59LFNiXQLV3kuwYstHl4H4Jsw=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/ThingSmartCryption-watchOS-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			ca7wIbyHx6qkxDPYdn2N5ydrFsI=
			</data>
			<key>hash2</key>
			<data>
			Ny5njAs8BoL/i8amaH9d1bXugb+gEk1GDB1YD09s4cM=
			</data>
		</dict>
		<key>Headers/error_code.h</key>
		<dict>
			<key>hash</key>
			<data>
			e0l9pwTOC0iREgFq6DZdBS+Dxd0=
			</data>
			<key>hash2</key>
			<data>
			/gIxR1GRHCCn3jptLNHDi5e3mKFT95Ky6MS/S/xNDPc=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			sS59LFNiXQLV3kuwYstHl4H4Jsw=
			</data>
			<key>hash2</key>
			<data>
			BIxTc5645VZmZXWX1i8ZxYMEb9H7gyeGYdRQKQNU6a0=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
