<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>极家汇</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>极家汇</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>3.0.2</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wx8531759f373d8a56</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>3</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>weixin</string>
		<string>weixinULAPI</string>
		<string>weixinURLParamsAPI</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>MOBAppKey</key>
	<string>3b631710bd114</string>
	<key>MOBAppSecret</key>
	<string>464c349e9c56d3f344df63487c7789bf</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>App需要访问您的媒体库控制智能设备</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>App需要使用蓝牙来连接和控制智能设备</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>App需要使用蓝牙来连接和控制智能设备</string>
	<key>NSCameraUsageDescription</key>
	<string>App需要使用相机设置头像</string>
	<key>NSContactsUsageDescription</key>
	<string>App需要访问您的通讯录以控制智能设备</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>App需要使用本地网络进行设备配网</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>App需要定位权限自动获取 WIFI 信号</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>App需要定位权限自动获取 WIFI 信号</string>
	<key>NSLocationUsageDescription</key>
	<string>App需要定位权限自动获取 WIFI 信号</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>App需要定位权限自动获取 WIFI 信号</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>App需要您的同意使用智能化语音控制</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>App需要使用相册设置头像</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>App需要使用相册设置头像</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
