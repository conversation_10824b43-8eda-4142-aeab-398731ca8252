# ---------- CocoaPods 源 ----------
# CDN + **唯一** Tuya 私有仓库；去掉第二个以免重复 spec 报错
source 'https://cdn.cocoapods.org/'
source 'https://github.com/tuya/TuyaPublicSpecs.git'
source 'https://github.com/tuya/tuya-pod-specs.git'

platform :ios, '15.6'

# 关闭统计，减少 flutter build 延迟
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

# ---------- CocoaPods 全局配置 ----------
install! 'cocoapods',
  warn_for_unused_master_specs_repo: false,
  warn_for_multiple_pod_sources: true

project 'Runner', {
  'Debug'   => :debug,
  'Profile' => :release,
  'Release' => :release
}

# ---------- Flutter 集成 ----------
def flutter_root
  generated = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  abort("#{generated} missing; run `flutter pub get` first") unless File.exist?(generated)

  File.foreach(generated) do |line|
    return line.split('=').last.strip if line =~ /FLUTTER_ROOT=/
  end
  abort "FLUTTER_ROOT not found in #{generated}"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)
flutter_ios_podfile_setup

target 'Runner' do
  # 动态 / 静态二选一；静态链接可减小启动时符号冲
  use_frameworks! :linkage => :static
  use_modular_headers!

  # ---------- 插入 Flutter 生成的 Pods ----------
  flutter_install_all_ios_pods File.dirname(__FILE__)

  # ---------- 第三方常用 Pods ----------
  pod 'Moya/RxSwift'
  pod 'SnapKit'
  pod 'IQKeyboardManager'
  pod 'RxSwift'
  pod 'RxGesture'
  pod 'BRPickerView'
  pod 'Kingfisher', '~> 7.0'
  pod 'MBProgressHUD'
  pod 'MJRefresh'
  pod 'JXSegmentedView'

  # ---------- Tuya / ThingSmart SDK ----------
  pod 'ThingSmartCryption', :path => './'
  pod 'ThingSmartHomeKit',               '~> 6.2.0'
  pod 'ThingSmartFamilyBizBundle',       '~> 6.2.0'
  pod 'ThingSmartActivatorBizBundle',    '~> 6.2.0'
  pod 'ThingSmartSceneBizBundle',        '~> 6.2.0'
  pod 'ThingSmartPanelBizBundle',        '~> 6.2.0'
  pod 'ThingSmartCameraPanelBizBundle',  '~> 6.2.0'
  pod 'ThingSmartCameraRNPanelBizBundle','~> 6.2.0'
  pod 'ThingSmartCloudServiceBizBundle', '~> 6.2.0'
  pod 'ThingSmartDeviceDetailBizBundle'
  pod 'ThingSmartCameraSettingBizBundle'
  pod 'ThingSmartBusinessExtensionKitBLEExtra'
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
    target.build_configurations.each do |config|
    config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
            '$(inherited)',
 
            ## dart: [PermissionGroup.location, PermissionGroup.locationAlways, PermissionGroup.locationWhenInUse]
            'PERMISSION_LOCATION=1',
          ]
      end
  end
end
